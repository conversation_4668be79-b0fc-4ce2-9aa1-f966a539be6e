# 现代化博客系统部署指南

本指南将帮助您完整部署现代化博客系统，包括前端和后端。

## 📋 系统架构

```
┌─────────────────┐    ┌──────────────────────┐    ┌─────────────────┐
│   Next.js 前端   │───▶│ Cloudflare Workers   │───▶│  Cloudflare D1  │
│                 │    │      后端 API        │    │     数据库      │
└─────────────────┘    └──────────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Cloudflare R2   │
                       │    文件存储     │
                       └─────────────────┘
```

## 🚀 快速部署

### 第一步：部署 Cloudflare Workers 后端

```bash
# 1. 进入 workers 目录
cd workers

# 2. 安装依赖
npm install

# 3. 登录 Cloudflare
wrangler login

# 4. 创建资源
npm run setup-resources

# 5. 设置环境变量
npm run setup-secrets dev

# 6. 部署到开发环境
npm run deploy:dev
```

### 第二步：配置前端环境变量

```bash
# 1. 复制环境变量模板
cp .env.example .env.local

# 2. 编辑 .env.local，设置以下变量：
NEXT_PUBLIC_API_URL=https://your-worker.your-subdomain.workers.dev
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=e52ad4d7a6a07a326666f2a3cd9e29ec6997c363
NEXTAUTH_SECRET=your-random-secret
ADMIN_EMAIL=<EMAIL>
```

### 第三步：启动前端应用

```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev
```

## 📊 文章存储位置

### ✅ **当前存储架构**

**文章保存位置**：Cloudflare D1 数据库

**数据表结构**：
```sql
CREATE TABLE articles (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    summary TEXT,
    cover_image TEXT,
    status TEXT CHECK (status IN ('draft', 'published', 'archived')),
    category TEXT NOT NULL,
    tags TEXT, -- JSON 数组
    author_id TEXT NOT NULL,
    published_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0
);
```

**API 端点**：
- `POST /api/articles` - 创建文章
- `GET /api/articles` - 获取文章列表
- `GET /api/articles/[slug]` - 获取单篇文章
- `PUT /api/articles/[id]` - 更新文章
- `DELETE /api/articles/[id]` - 删除文章

## 🔧 详细配置步骤

### 1. Cloudflare Workers 配置

#### 创建必要资源

```bash
# D1 数据库
wrangler d1 create modern-blog-db

# KV 命名空间
wrangler kv:namespace create "CACHE"
wrangler kv:namespace create "SESSIONS"

# R2 存储桶
wrangler r2 bucket create modern-blog-storage

# 执行数据库 schema
wrangler d1 execute modern-blog-db --file=./schema.sql
```

#### 设置环境变量

```bash
# GitHub Client Secret
echo "e52ad4d7a6a07a326666f2a3cd9e29ec6997c363" | wrangler secret put GITHUB_CLIENT_SECRET

# JWT 密钥
echo "your-super-secret-jwt-key" | wrangler secret put JWT_SECRET

# 管理员邮箱
echo '["<EMAIL>"]' | wrangler secret put ADMIN_EMAILS
```

#### 更新 wrangler.toml

将创建的资源 ID 更新到配置文件中：

```toml
[[kv_namespaces]]
binding = "CACHE"
id = "your-cache-namespace-id"

[[d1_databases]]
binding = "DB"
database_name = "modern-blog-db"
database_id = "your-database-id"

[[r2_buckets]]
binding = "STORAGE"
bucket_name = "modern-blog-storage"
```

### 2. 前端配置

#### Next.js 配置

更新 `next.config.js`：

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/api/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
```

#### 环境变量

```bash
# .env.local
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-random-secret-here
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=e52ad4d7a6a07a326666f2a3cd9e29ec6997c363
NEXT_PUBLIC_API_URL=https://your-worker.your-subdomain.workers.dev
ADMIN_EMAIL=<EMAIL>
```

## 🧪 测试部署

### 1. 验证后端 API

```bash
# 健康检查
curl https://your-worker.your-subdomain.workers.dev/api/health

# 预期响应
{
  "success": true,
  "message": "API is healthy",
  "timestamp": "2024-12-01T12:00:00.000Z",
  "environment": "development"
}
```

### 2. 测试文章创建

1. 访问 `http://localhost:3000/dashboard/articles/new`
2. 填写文章信息
3. 点击"保存草稿"或"发布文章"
4. 检查浏览器开发者工具的网络请求
5. 验证文章是否保存到数据库

### 3. 验证数据库

```bash
# 查看数据库中的文章
wrangler d1 execute modern-blog-db --command="SELECT * FROM articles LIMIT 5"
```

## 🔄 数据流程

### 文章创建流程

```
1. 用户在前端编辑器中创建文章
   ↓
2. 前端调用 articlesAPI.createArticle()
   ↓
3. 请求发送到 /api/articles (通过 rewrite 转发到 Workers)
   ↓
4. Workers 后端处理请求
   ↓
5. 数据保存到 Cloudflare D1 数据库
   ↓
6. 返回创建的文章数据给前端
   ↓
7. 前端显示成功消息或跳转到文章列表
```

## 📈 生产环境部署

### 1. 部署到生产环境

```bash
# 部署 Workers 到生产环境
cd workers
npm run deploy:prod

# 设置生产环境变量
npm run setup-secrets prod
```

### 2. 前端生产部署

```bash
# 构建前端应用
npm run build

# 部署到 Vercel (推荐)
vercel --prod

# 或部署到其他平台
npm run start
```

### 3. 域名配置

1. 在 Cloudflare 中配置自定义域名
2. 更新前端环境变量中的 API URL
3. 更新 GitHub OAuth 回调 URL

## 🛠️ 故障排除

### 常见问题

1. **文章保存失败**
   - 检查 API URL 配置
   - 验证 Workers 部署状态
   - 查看浏览器网络请求错误

2. **认证失败**
   - 检查 GitHub OAuth 配置
   - 验证环境变量设置
   - 确认回调 URL 正确

3. **数据库连接失败**
   - 检查 D1 数据库配置
   - 验证 schema 是否执行成功
   - 查看 Workers 日志

### 调试命令

```bash
# 查看 Workers 日志
wrangler tail

# 检查数据库状态
wrangler d1 execute modern-blog-db --command="SELECT COUNT(*) FROM articles"

# 测试 API 端点
curl -X POST https://your-worker.workers.dev/api/articles \
  -H "Content-Type: application/json" \
  -d '{"title":"Test","content":"Test content","category":"test","tags":[],"status":"draft"}'
```

## 📞 获取帮助

如果遇到问题：

1. 查看 `workers/README.md` 了解详细配置
2. 检查 `workers/COMPATIBILITY.md` 了解兼容性信息
3. 查看项目 GitHub Issues
4. 联系技术支持

---

🎉 **恭喜！** 您的现代化博客系统现在已经完全配置好，文章将保存到 Cloudflare D1 数据库中！

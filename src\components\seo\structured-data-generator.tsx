'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { motion } from 'framer-motion'
import { 
  Code, 
  Copy, 
  Check, 
  Plus, 
  Trash2, 
  RefreshCw,
  FileJson,
  Braces,
  FileCheck,
  AlertTriangle,
  Info,
  Settings,
  Eye
} from 'lucide-react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useToast } from '@/components/ui/toast'
import { cn } from '@/lib/utils'

interface StructuredDataTemplate {
  id: string
  type: string
  name: string
  description: string
  fields: StructuredDataField[]
  example: string
}

interface StructuredDataField {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array'
  required: boolean
  description: string
  default?: string
}

interface StructuredDataGeneratorProps {
  url?: string
  className?: string
}

export function StructuredDataGenerator({ url, className }: StructuredDataGeneratorProps) {
  const t = useTranslations('structuredData')
  const { showToast } = useToast()
  
  const [templates, setTemplates] = useState<StructuredDataTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [generatedCode, setGeneratedCode] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<{
    valid: boolean
    errors: string[]
    warnings: string[]
  } | null>(null)
  const [targetUrl, setTargetUrl] = useState(url || '')

  // 加载模板
  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/seo/structured-data/templates')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setTemplates(data.data.templates)
        }
      }
    } catch (error) {
      console.error('Failed to load templates:', error)
      showToast.error(t('loadTemplatesFailed'))
    } finally {
      setIsLoading(false)
    }
  }

  // 选择模板
  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId)
    const template = templates.find(t => t.id === templateId)
    
    if (template) {
      // 初始化表单数据
      const initialData: Record<string, any> = {}
      template.fields.forEach(field => {
        if (field.default !== undefined) {
          initialData[field.name] = field.default
        } else {
          switch (field.type) {
            case 'string':
              initialData[field.name] = ''
              break
            case 'number':
              initialData[field.name] = 0
              break
            case 'boolean':
              initialData[field.name] = false
              break
            case 'date':
              initialData[field.name] = new Date().toISOString().split('T')[0]
              break
            case 'object':
              initialData[field.name] = {}
              break
            case 'array':
              initialData[field.name] = []
              break
          }
        }
      })
      setFormData(initialData)
      setGeneratedCode('')
      setValidationResult(null)
    }
  }

  // 更新表单数据
  const updateFormData = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }))
  }

  // 生成结构化数据
  const generateStructuredData = async () => {
    const template = templates.find(t => t.id === selectedTemplate)
    if (!template) return

    // 验证必填字段
    const missingFields = template.fields
      .filter(field => field.required && !formData[field.name])
      .map(field => field.name)

    if (missingFields.length > 0) {
      showToast.error(t('requiredFieldsMissing', { fields: missingFields.join(', ') }))
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/seo/structured-data/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: selectedTemplate,
          data: formData,
          url: targetUrl
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setGeneratedCode(data.data.code)
          showToast.success(t('generationSuccess'))
        }
      }
    } catch (error) {
      console.error('Failed to generate structured data:', error)
      showToast.error(t('generationFailed'))
    } finally {
      setIsLoading(false)
    }
  }

  // 验证结构化数据
  const validateStructuredData = async () => {
    if (!generatedCode) return

    setIsValidating(true)
    try {
      const response = await fetch('/api/seo/structured-data/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: generatedCode })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setValidationResult(data.data.result)
          
          if (data.data.result.valid) {
            showToast.success(t('validationSuccess'))
          } else {
            showToast.error(t('validationFailed'))
          }
        }
      }
    } catch (error) {
      console.error('Failed to validate structured data:', error)
      showToast.error(t('validationError'))
    } finally {
      setIsValidating(false)
    }
  }

  // 应用结构化数据
  const applyStructuredData = async () => {
    if (!generatedCode || !targetUrl) return

    try {
      const response = await fetch('/api/seo/structured-data/apply', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code: generatedCode,
          url: targetUrl
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          showToast.success(t('applySuccess'))
        }
      }
    } catch (error) {
      console.error('Failed to apply structured data:', error)
      showToast.error(t('applyFailed'))
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedCode)
      showToast.success(t('codeCopied'))
    } catch (error) {
      console.error('Failed to copy code:', error)
      showToast.error(t('copyFailed'))
    }
  }

  // 预览结构化数据
  const previewStructuredData = () => {
    if (!generatedCode) return

    const previewWindow = window.open('', '_blank')
    if (previewWindow) {
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${t('structuredDataPreview')}</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <script type="application/ld+json">
            ${generatedCode}
          </script>
          <style>
            body { font-family: system-ui, sans-serif; padding: 2rem; }
            pre { background: #f1f1f1; padding: 1rem; border-radius: 0.5rem; overflow: auto; }
          </style>
        </head>
        <body>
          <h1>${t('structuredDataPreview')}</h1>
          <p>${t('previewDescription')}</p>
          <pre><code>${generatedCode.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
        </body>
        </html>
      `)
      previewWindow.document.close()
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Braces className="h-6 w-6" />
            {t('structuredDataGenerator')}
          </h1>
          <p className="text-muted-foreground">{t('structuredDataDescription')}</p>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 表单区域 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('selectTemplate')}</CardTitle>
            </CardHeader>
            <CardContent>
              <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
                <SelectTrigger>
                  <SelectValue placeholder={t('selectTemplatePlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {selectedTemplate && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground">
                    {templates.find(t => t.id === selectedTemplate)?.description}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {selectedTemplate && (
            <Card>
              <CardHeader>
                <CardTitle>{t('fillDetails')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="targetUrl">{t('targetUrl')}</Label>
                    <Input
                      id="targetUrl"
                      value={targetUrl}
                      onChange={(e) => setTargetUrl(e.target.value)}
                      placeholder="https://example.com/page"
                    />
                  </div>
                  
                  {templates.find(t => t.id === selectedTemplate)?.fields.map((field) => (
                    <div key={field.name}>
                      <Label htmlFor={field.name}>
                        {field.name}
                        {field.required && <span className="text-red-500 ml-1">*</span>}
                      </Label>
                      
                      {field.type === 'string' && (
                        <Input
                          id={field.name}
                          value={formData[field.name] || ''}
                          onChange={(e) => updateFormData(field.name, e.target.value)}
                          placeholder={field.description}
                        />
                      )}
                      
                      {field.type === 'number' && (
                        <Input
                          id={field.name}
                          type="number"
                          value={formData[field.name] || 0}
                          onChange={(e) => updateFormData(field.name, parseFloat(e.target.value))}
                          placeholder={field.description}
                        />
                      )}
                      
                      {field.type === 'boolean' && (
                        <Select
                          value={formData[field.name]?.toString() || 'false'}
                          onValueChange={(value) => updateFormData(field.name, value === 'true')}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="true">{t('true')}</SelectItem>
                            <SelectItem value="false">{t('false')}</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                      
                      {field.type === 'date' && (
                        <Input
                          id={field.name}
                          type="date"
                          value={formData[field.name] || ''}
                          onChange={(e) => updateFormData(field.name, e.target.value)}
                        />
                      )}
                      
                      <p className="text-xs text-muted-foreground mt-1">
                        {field.description}
                      </p>
                    </div>
                  ))}
                  
                  <Button
                    onClick={generateStructuredData}
                    disabled={isLoading}
                    className="w-full mt-4"
                  >
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Code className="h-4 w-4 mr-2" />
                    )}
                    {t('generateCode')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 代码和预览区域 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {t('generatedCode')}
                <div className="flex items-center gap-2">
                  {generatedCode && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={copyToClipboard}
                        className="flex items-center gap-1"
                      >
                        <Copy className="h-4 w-4" />
                        {t('copy')}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={validateStructuredData}
                        disabled={isValidating}
                        className="flex items-center gap-1"
                      >
                        {isValidating ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <FileCheck className="h-4 w-4" />
                        )}
                        {t('validate')}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={previewStructuredData}
                        className="flex items-center gap-1"
                      >
                        <Eye className="h-4 w-4" />
                        {t('preview')}
                      </Button>
                    </>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {generatedCode ? (
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto max-h-96">
                    <code className="text-sm font-mono">
                      {generatedCode}
                    </code>
                  </pre>
                  
                  {targetUrl && (
                    <div className="mt-4">
                      <Button onClick={applyStructuredData}>
                        {t('applyToPage')}
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <FileJson className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">{t('noCodeGenerated')}</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    {t('selectTemplateAndFill')}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 验证结果 */}
          {validationResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {validationResult.valid ? (
                    <Check className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  )}
                  {t('validationResults')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {validationResult.valid ? (
                  <Alert className="bg-green-50 border-green-200">
                    <Check className="h-4 w-4 text-green-500" />
                    <AlertTitle>{t('validStructuredData')}</AlertTitle>
                    <AlertDescription>
                      {t('validStructuredDataDescription')}
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="space-y-4">
                    {validationResult.errors.length > 0 && (
                      <Alert className="bg-red-50 border-red-200">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <AlertTitle>{t('errors')}</AlertTitle>
                        <AlertDescription>
                          <ul className="list-disc pl-5 space-y-1 mt-2">
                            {validationResult.errors.map((error, index) => (
                              <li key={index} className="text-sm">{error}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {validationResult.warnings.length > 0 && (
                      <Alert className="bg-yellow-50 border-yellow-200">
                        <Info className="h-4 w-4 text-yellow-500" />
                        <AlertTitle>{t('warnings')}</AlertTitle>
                        <AlertDescription>
                          <ul className="list-disc pl-5 space-y-1 mt-2">
                            {validationResult.warnings.map((warning, index) => (
                              <li key={index} className="text-sm">{warning}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

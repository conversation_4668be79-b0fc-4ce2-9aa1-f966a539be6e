import { Metadata } from 'next'
import { useTranslations } from 'next-intl'
import { getTranslations } from 'next-intl/server'
import { 
  DollarSign, 
  Download, 
  Layout, 
  ArrowRightLeft,
  BarChart3,
  Shield,
  Cloud,
  Zap,
  Target,
  TrendingUp
} from 'lucide-react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { AdManager } from '@/components/ads/ad-manager'
import { AdPlacement } from '@/components/ads/ad-placement'
import { DataExport } from '@/components/backup/data-export'
import { DataMigration } from '@/components/backup/data-migration'

interface BusinessPageProps {
  params: {
    locale: string
  }
}

export async function generateMetadata({ params }: BusinessPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'business' })
  
  return {
    title: t('title'),
    description: t('description')
  }
}

export default function BusinessPage({ params }: BusinessPageProps) {
  const t = useTranslations('business')
  
  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* 页面头部 */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">{t('title')}</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          {t('description')}
        </p>
      </div>

      {/* 功能概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="text-center">
          <CardHeader>
            <DollarSign className="h-12 w-12 mx-auto mb-2 text-primary" />
            <CardTitle className="text-lg">{t('ads.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('ads.description')}
            </p>
            <Badge variant="secondary">{t('ads.status')}</Badge>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Layout className="h-12 w-12 mx-auto mb-2 text-primary" />
            <CardTitle className="text-lg">{t('placements.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('placements.description')}
            </p>
            <Badge variant="secondary">{t('placements.status')}</Badge>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Download className="h-12 w-12 mx-auto mb-2 text-primary" />
            <CardTitle className="text-lg">{t('export.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('export.description')}
            </p>
            <Badge variant="secondary">{t('export.status')}</Badge>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <ArrowRightLeft className="h-12 w-12 mx-auto mb-2 text-primary" />
            <CardTitle className="text-lg">{t('migration.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('migration.description')}
            </p>
            <Badge variant="secondary">{t('migration.status')}</Badge>
          </CardContent>
        </Card>
      </div>

      {/* 商业价值展示 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              {t('revenue.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-3xl font-bold text-green-600">$12,450</div>
              <p className="text-sm text-muted-foreground">{t('revenue.description')}</p>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t('revenue.thisMonth')}</span>
                  <span className="font-medium">$3,200</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t('revenue.lastMonth')}</span>
                  <span className="font-medium">$2,850</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t('revenue.growth')}</span>
                  <span className="font-medium text-green-600">+12.3%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-500" />
              {t('performance.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t('performance.impressions')}</span>
                  <span className="font-medium">2.4M</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t('performance.clicks')}</span>
                  <span className="font-medium">48.2K</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t('performance.ctr')}</span>
                  <span className="font-medium">2.01%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t('performance.cpm')}</span>
                  <span className="font-medium">$5.18</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-500" />
              {t('security.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">{t('security.description')}</p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span>{t('security.encryption')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span>{t('security.backup')}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span>{t('security.compliance')}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容 */}
      <Tabs defaultValue="ads" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="ads" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.ads')}</span>
          </TabsTrigger>
          <TabsTrigger value="placements" className="flex items-center gap-2">
            <Layout className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.placements')}</span>
          </TabsTrigger>
          <TabsTrigger value="export" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.export')}</span>
          </TabsTrigger>
          <TabsTrigger value="migration" className="flex items-center gap-2">
            <ArrowRightLeft className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.migration')}</span>
          </TabsTrigger>
        </TabsList>

        {/* 广告管理 */}
        <TabsContent value="ads" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('ads.title')}</h2>
            <p className="text-muted-foreground">{t('ads.fullDescription')}</p>
          </div>
          
          <AdManager />
        </TabsContent>

        {/* 广告位管理 */}
        <TabsContent value="placements" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('placements.title')}</h2>
            <p className="text-muted-foreground">{t('placements.fullDescription')}</p>
          </div>
          
          <AdPlacement />
        </TabsContent>

        {/* 数据导出 */}
        <TabsContent value="export" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('export.title')}</h2>
            <p className="text-muted-foreground">{t('export.fullDescription')}</p>
          </div>
          
          <DataExport />
        </TabsContent>

        {/* 数据迁移 */}
        <TabsContent value="migration" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('migration.title')}</h2>
            <p className="text-muted-foreground">{t('migration.fullDescription')}</p>
          </div>
          
          <DataMigration />
        </TabsContent>
      </Tabs>

      {/* 企业级功能说明 */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg p-8">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold">{t('enterprise.title')}</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            {t('enterprise.description')}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div className="text-center">
              <Cloud className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <h3 className="font-medium">{t('enterprise.cloud.title')}</h3>
              <p className="text-sm text-muted-foreground">{t('enterprise.cloud.description')}</p>
            </div>
            
            <div className="text-center">
              <Zap className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
              <h3 className="font-medium">{t('enterprise.performance.title')}</h3>
              <p className="text-sm text-muted-foreground">{t('enterprise.performance.description')}</p>
            </div>
            
            <div className="text-center">
              <Shield className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <h3 className="font-medium">{t('enterprise.security.title')}</h3>
              <p className="text-sm text-muted-foreground">{t('enterprise.security.description')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

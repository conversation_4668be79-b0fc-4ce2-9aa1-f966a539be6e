import { Metada<PERSON> } from 'next'
import { getTranslations } from 'next-intl/server'
import { Suspense } from 'react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Skeleton } from '@/components/ui/skeleton'
import { AnalyticsDashboard } from '@/components/analytics/analytics-dashboard'
import { SEOOptimizer } from '@/components/seo/seo-optimizer'
import { SitemapGenerator } from '@/components/seo/sitemap-generator'
import { KeywordResearch } from '@/components/seo/keyword-research'
import { 
  BarChart3, 
  Search, 
  Map, 
  Target,
  TrendingUp,
  Users,
  Eye,
  Globe
} from 'lucide-react'

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'analytics' })
  
  return {
    title: t('title'),
    description: t('description'),
  }
}

// 模拟分析概览数据
const analyticsOverview = {
  totalViews: 125430,
  uniqueVisitors: 89234,
  avgSessionDuration: 245, // seconds
  bounceRate: 32.5,
  topPages: [
    { path: '/articles/nextjs-guide', title: 'Next.js 完整指南', views: 12543 },
    { path: '/articles/react-hooks', title: 'React Hooks 深度解析', views: 9876 },
    { path: '/articles/typescript-tips', title: 'TypeScript 实用技巧', views: 8765 }
  ],
  topCountries: [
    { country: '中国', visitors: 45234 },
    { country: '美国', visitors: 23456 },
    { country: '日本', visitors: 12345 }
  ]
}

export default async function AnalyticsPage({ params }: { params: { locale: string } }) {
  const t = await getTranslations({ locale: params.locale, namespace: 'analytics' })

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* 页面头部 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
        <p className="text-muted-foreground">{t('description')}</p>
      </div>

      {/* 快速概览 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4 text-center">
            <Eye className="h-8 w-8 mx-auto mb-2 text-blue-500" />
            <div className="text-2xl font-bold">{analyticsOverview.totalViews.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">{t('totalViews')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <div className="text-2xl font-bold">{analyticsOverview.uniqueVisitors.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">{t('uniqueVisitors')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-orange-500" />
            <div className="text-2xl font-bold">
              {Math.floor(analyticsOverview.avgSessionDuration / 60)}:{(analyticsOverview.avgSessionDuration % 60).toString().padStart(2, '0')}
            </div>
            <div className="text-sm text-muted-foreground">{t('avgSessionDuration')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Globe className="h-8 w-8 mx-auto mb-2 text-purple-500" />
            <div className="text-2xl font-bold">{analyticsOverview.bounceRate}%</div>
            <div className="text-sm text-muted-foreground">{t('bounceRate')}</div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="dashboard" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            {t('dashboard')}
          </TabsTrigger>
          <TabsTrigger value="seo" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            {t('seoOptimization')}
          </TabsTrigger>
          <TabsTrigger value="keywords" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            {t('keywordResearch')}
          </TabsTrigger>
          <TabsTrigger value="sitemap" className="flex items-center gap-2">
            <Map className="h-4 w-4" />
            {t('sitemap')}
          </TabsTrigger>
        </TabsList>

        {/* 分析仪表板 */}
        <TabsContent value="dashboard">
          <Suspense fallback={<DashboardSkeleton />}>
            <AnalyticsDashboard />
          </Suspense>
        </TabsContent>

        {/* SEO优化 */}
        <TabsContent value="seo">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Suspense fallback={<SEOSkeleton />}>
                <SEOOptimizer />
              </Suspense>
            </div>
            
            <div className="space-y-6">
              {/* SEO快速提示 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5" />
                    {t('seoTips')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                      <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                        {t('tip1Title')}
                      </h4>
                      <p className="text-blue-700 dark:text-blue-300">
                        {t('tip1Description')}
                      </p>
                    </div>
                    
                    <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                      <h4 className="font-medium text-green-900 dark:text-green-100 mb-1">
                        {t('tip2Title')}
                      </h4>
                      <p className="text-green-700 dark:text-green-300">
                        {t('tip2Description')}
                      </p>
                    </div>
                    
                    <div className="p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
                      <h4 className="font-medium text-orange-900 dark:text-orange-100 mb-1">
                        {t('tip3Title')}
                      </h4>
                      <p className="text-orange-700 dark:text-orange-300">
                        {t('tip3Description')}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 热门页面 */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('topPages')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsOverview.topPages.map((page, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm truncate">{page.title}</h4>
                          <p className="text-xs text-muted-foreground truncate">{page.path}</p>
                        </div>
                        <div className="text-sm font-medium">
                          {page.views.toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 地理分布 */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('topCountries')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsOverview.topCountries.map((country, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="font-medium">{country.country}</span>
                        <span className="text-sm text-muted-foreground">
                          {country.visitors.toLocaleString()}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* 关键词研究 */}
        <TabsContent value="keywords">
          <Suspense fallback={<KeywordSkeleton />}>
            <KeywordResearch />
          </Suspense>
        </TabsContent>

        {/* 站点地图 */}
        <TabsContent value="sitemap">
          <Suspense fallback={<SitemapSkeleton />}>
            <SitemapGenerator />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// 骨架屏组件
function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-muted rounded w-1/2 mb-2" />
                <div className="h-8 bg-muted rounded w-3/4 mb-2" />
                <div className="h-3 bg-muted rounded w-1/3" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-muted rounded w-1/4 mb-4" />
            <div className="h-64 bg-muted rounded" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function SEOSkeleton() {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-muted rounded w-1/3 mb-4" />
            <div className="h-10 bg-muted rounded mb-4" />
            <div className="h-8 bg-muted rounded w-1/4" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-32 bg-muted rounded" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function KeywordSkeleton() {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-muted rounded w-1/4 mb-4" />
            <div className="flex gap-2 mb-4">
              <div className="h-10 bg-muted rounded flex-1" />
              <div className="h-10 bg-muted rounded w-32" />
              <div className="h-10 bg-muted rounded w-24" />
            </div>
            <div className="grid grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="animate-pulse">
                <div className="h-4 bg-muted rounded w-1/2 mb-2" />
                <div className="h-3 bg-muted rounded w-3/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

function SitemapSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-muted rounded w-1/2 mb-2" />
                <div className="h-8 bg-muted rounded w-3/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-muted rounded w-1/3 mb-4" />
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

import { getServerSession } from "next-auth/next"
import { authOptions } from "./auth"
import { redirect } from "next/navigation"

// 获取当前用户会话
export async function getCurrentUser() {
  const session = await getServerSession(authOptions)
  return session?.user
}

// 检查用户是否已认证
export async function requireAuth() {
  const user = await getCurrentUser()
  if (!user) {
    redirect('/auth/signin')
  }
  return user
}

// 检查用户是否为管理员
export async function requireAdmin() {
  const user = await requireAuth()
  if (user.role !== 'admin') {
    redirect('/unauthorized')
  }
  return user
}

// 检查用户是否为管理员或协作者
export async function requireCollaborator() {
  const user = await requireAuth()
  if (user.role !== 'admin' && user.role !== 'collaborator') {
    redirect('/unauthorized')
  }
  return user
}

// 检查用户权限
export function hasPermission(
  userRole: 'admin' | 'collaborator' | 'user',
  requiredRole: 'admin' | 'collaborator' | 'user'
): boolean {
  const roleHierarchy = {
    admin: 3,
    collaborator: 2,
    user: 1,
  }
  
  return roleHierarchy[userRole] >= roleHierarchy[requiredRole]
}

// 客户端权限检查 Hook
export function usePermissions() {
  // 这个函数将在客户端组件中使用
  return {
    hasPermission,
  }
}

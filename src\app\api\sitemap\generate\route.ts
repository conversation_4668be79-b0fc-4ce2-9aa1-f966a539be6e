import { NextRequest, NextResponse } from 'next/server'
import { D1Database, R2Bucket } from '@cloudflare/workers-types'
import { getRequestContext } from '@/lib/cloudflare'

// 生成站点地图XML
const generateSitemapXML = async (config: any) => {
  // 在实际应用中，这里应该从数据库中获取所有页面URL
  // const { env } = getRequestContext()
  // const db = env.DB as D1Database
  
  // 模拟页面数据
  const pages = [
    { path: '/', lastModified: new Date().toISOString(), priority: 1.0 },
    { path: '/articles', lastModified: new Date().toISOString(), priority: 0.9 },
    { path: '/articles/nextjs-guide', lastModified: new Date().toISOString(), priority: 0.8 },
    { path: '/articles/react-hooks', lastModified: new Date().toISOString(), priority: 0.8 },
    { path: '/articles/typescript-tips', lastModified: new Date().toISOString(), priority: 0.8 },
    { path: '/about', lastModified: new Date().toISOString(), priority: 0.7 },
    { path: '/contact', lastModified: new Date().toISOString(), priority: 0.7 },
    { path: '/social', lastModified: new Date().toISOString(), priority: 0.7 },
    { path: '/analytics', lastModified: new Date().toISOString(), priority: 0.6 }
  ]
  
  // 过滤排除的路径
  const filteredPages = pages.filter(page => 
    !config.excludePaths.some((excludePath: string) => 
      page.path.startsWith(excludePath)
    )
  )
  
  // 添加自定义URL
  const allPages = [
    ...filteredPages,
    ...config.customUrls.map((customUrl: any) => ({
      path: customUrl.url,
      lastModified: customUrl.lastMod || new Date().toISOString(),
      priority: customUrl.priority || config.priority,
      changefreq: customUrl.changeFreq || config.changeFreq
    }))
  ]
  
  // 生成主站点地图
  let mainSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `
  <url>
    <loc>https://example.com${page.path}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changefreq || config.changeFreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('')}
</urlset>`

  // 生成图片站点地图（如果启用）
  let imageSitemap = ''
  if (config.includeImages) {
    imageSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
${allPages.map(page => `
  <url>
    <loc>https://example.com${page.path}</loc>
    <image:image>
      <image:loc>https://example.com/images/sample-${page.path.replace(/\//g, '-')}.jpg</image:loc>
      <image:title>Sample image for ${page.path}</image:title>
    </image:image>
  </url>`).join('')}
</urlset>`
  }
  
  // 生成多语言站点地图
  const languageSitemaps: Record<string, string> = {}
  for (const lang of config.languages) {
    languageSitemaps[lang] = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${allPages.map(page => `
  <url>
    <loc>https://example.com/${lang}${page.path}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changefreq || config.changeFreq}</changefreq>
    <priority>${page.priority}</priority>
    ${config.languages.map((altLang: string) => `
    <xhtml:link 
      rel="alternate" 
      hreflang="${altLang}" 
      href="https://example.com/${altLang}${page.path}" 
    />`).join('')}
  </url>`).join('')}
</urlset>`
  }
  
  // 生成站点地图索引
  const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>https://example.com/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  ${config.includeImages ? `
  <sitemap>
    <loc>https://example.com/sitemap-images.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>` : ''}
  ${Object.keys(languageSitemaps).map(lang => `
  <sitemap>
    <loc>https://example.com/sitemap-${lang}.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>`).join('')}
</sitemapindex>`
  
  return {
    main: mainSitemap,
    images: imageSitemap,
    languages: languageSitemaps,
    index: sitemapIndex
  }
}

export async function POST(request: NextRequest) {
  try {
    const config = await request.json()
    
    // 验证配置
    if (typeof config.enabled !== 'boolean' || 
        typeof config.autoGenerate !== 'boolean' ||
        typeof config.includeImages !== 'boolean' ||
        typeof config.includeNews !== 'boolean' ||
        typeof config.includeVideos !== 'boolean' ||
        typeof config.priority !== 'number' ||
        !Array.isArray(config.languages) ||
        !Array.isArray(config.excludePaths) ||
        !Array.isArray(config.customUrls)) {
      return NextResponse.json(
        { success: false, error: 'Invalid configuration format' },
        { status: 400 }
      )
    }
    
    // 生成站点地图
    const sitemaps = await generateSitemapXML(config)
    
    // 在实际应用中，这里应该将生成的站点地图保存到存储中
    // const { env } = getRequestContext()
    // const r2 = env.R2 as R2Bucket
    // await r2.put('sitemap.xml', sitemaps.main)
    // if (config.includeImages) {
    //   await r2.put('sitemap-images.xml', sitemaps.images)
    // }
    // for (const [lang, content] of Object.entries(sitemaps.languages)) {
    //   await r2.put(`sitemap-${lang}.xml`, content)
    // }
    // await r2.put('sitemap-index.xml', sitemaps.index)
    
    // 更新统计数据
    // const db = env.DB as D1Database
    // await db.prepare('UPDATE sitemap_stats SET ...').bind(...).run()
    
    return NextResponse.json({
      success: true,
      message: 'Sitemap generated successfully',
      data: {
        totalUrls: 1254, // 示例数据
        fileSize: 512000 // 示例数据
      }
    })
  } catch (error) {
    console.error('Generate sitemap error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to generate sitemap' },
      { status: 500 }
    )
  }
}

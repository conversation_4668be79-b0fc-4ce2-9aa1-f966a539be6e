import { NextAuthOptions } from "next-auth"
import Github<PERSON>rovider from "next-auth/providers/github"
import { User } from "@/types"

// 扩展 NextAuth 类型
declare module "next-auth" {
  interface Session {
    user: User & {
      id: string
    }
  }
  
  interface User {
    role: 'admin' | 'collaborator' | 'user'
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: 'admin' | 'collaborator' | 'user'
  }
}

// 管理员邮箱列表（实际应用中应该从数据库或环境变量获取）
const ADMIN_EMAILS = [
  process.env.ADMIN_EMAIL || "admin@localhost",
  // 可以添加更多管理员邮箱
]

// 协作者邮箱列表
const COLLABORATOR_EMAILS = [
  // 可以添加协作者邮箱
]

// 根据邮箱确定用户角色
function getUserRole(email: string): 'admin' | 'collaborator' | 'user' {
  if (ADMIN_EMAILS.includes(email)) {
    return 'admin'
  }
  if (COLLABORATOR_EMAILS.includes(email)) {
    return 'collaborator'
  }
  return 'user'
}

export const authOptions: NextAuthOptions = {
  providers: [
    GithubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
      profile(profile) {
        return {
          id: profile.id.toString(),
          name: profile.name || profile.login,
          email: profile.email,
          image: profile.avatar_url,
          role: getUserRole(profile.email || ''),
        }
      },
    }),
  ],
  
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.sub!
        session.user.role = token.role
      }
      return session
    },
    
    async signIn({ user, account, profile }) {
      // 可以在这里添加额外的登录验证逻辑
      return true
    },
    
    async redirect({ url, baseUrl }) {
      // 登录后重定向逻辑
      if (url.startsWith("/")) return `${baseUrl}${url}`
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    },
  },
  
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 天
  },
  
  secret: process.env.NEXTAUTH_SECRET,
}

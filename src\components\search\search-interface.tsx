"use client"

import { useState, useEffect, use<PERSON>allback, useRef } from "react"
import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { Search as SearchIcon, Filter, X, Clock, Tag, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArticleCard } from "@/components/ui/article-card"
import { ArticleCardSkeleton } from "@/components/ui/loading"
import { ResponsiveGrid } from "@/components/ui/responsive-grid"
import { SearchResultsStructuredData } from "@/components/seo/structured-data"
import { debounce } from "@/lib/utils"
import type { Article } from "@/types"

interface SearchInterfaceProps {
  initialQuery?: string
  initialPage?: number
  initialCategory?: string
  initialTag?: string
}

interface SearchResults {
  articles: Article[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  query: string
  suggestions: string[]
}

export function SearchInterface({
  initialQuery = "",
  initialPage = 1,
  initialCategory = "",
  initialTag = "",
}: SearchInterfaceProps) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  const [query, setQuery] = useState(initialQuery)
  const [page, setPage] = useState(initialPage)
  const [category, setCategory] = useState(initialCategory)
  const [tag, setTag] = useState(initialTag)
  const [activeTab, setActiveTab] = useState<"all" | "articles" | "tags">("all")
  
  const [results, setResults] = useState<SearchResults | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const searchInputRef = useRef<HTMLInputElement>(null)
  
  // 执行搜索
  const performSearch = useCallback(async () => {
    if (!query.trim()) {
      setResults(null)
      return
    }
    
    setIsLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams()
      params.set("q", query)
      params.set("page", page.toString())
      if (category) params.set("category", category)
      if (tag) params.set("tag", tag)
      
      const response = await fetch(`/api/search?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error("搜索请求失败")
      }
      
      const data = await response.json()
      
      if (data.success) {
        setResults(data.data)
      } else {
        throw new Error(data.error || "搜索失败")
      }
    } catch (err) {
      console.error("Search error:", err)
      setError(err instanceof Error ? err.message : "搜索过程中发生错误")
    } finally {
      setIsLoading(false)
    }
  }, [query, page, category, tag])
  
  // 更新 URL 参数
  const updateUrlParams = useCallback(() => {
    const params = new URLSearchParams(searchParams)
    
    if (query) {
      params.set("q", query)
    } else {
      params.delete("q")
    }
    
    if (page > 1) {
      params.set("page", page.toString())
    } else {
      params.delete("page")
    }
    
    if (category) {
      params.set("category", category)
    } else {
      params.delete("category")
    }
    
    if (tag) {
      params.set("tag", tag)
    } else {
      params.delete("tag")
    }
    
    router.push(`${pathname}?${params.toString()}`)
  }, [query, page, category, tag, pathname, router, searchParams])
  
  // 防抖搜索
  const debouncedSearch = useCallback(
    debounce(() => {
      performSearch()
      updateUrlParams()
    }, 300),
    [performSearch, updateUrlParams]
  )
  
  // 当搜索参数变化时执行搜索
  useEffect(() => {
    if (query.trim()) {
      debouncedSearch()
    } else {
      setResults(null)
      updateUrlParams()
    }
  }, [query, page, category, tag, debouncedSearch, updateUrlParams])
  
  // 处理搜索表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    performSearch()
    updateUrlParams()
  }
  
  // 处理搜索建议点击
  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion)
    setPage(1)
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }
  
  // 处理分页
  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }
  
  // 清除搜索
  const handleClearSearch = () => {
    setQuery("")
    setPage(1)
    setCategory("")
    setTag("")
    setResults(null)
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }
  
  return (
    <div className="space-y-6">
      {/* 搜索表单 */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              ref={searchInputRef}
              type="search"
              placeholder="搜索文章、标签或关键词..."
              className="pl-10 w-full"
              value={query}
              onChange={(e) => {
                setQuery(e.target.value)
                setPage(1)
              }}
              autoFocus
            />
            {query && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
                onClick={handleClearSearch}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <Button type="submit" disabled={!query.trim() || isLoading}>
            <SearchIcon className="w-4 h-4 mr-2" />
            搜索
          </Button>
        </div>
        
        {/* 高级筛选 */}
        <div className="flex flex-wrap gap-2">
          {category && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Filter className="w-3 h-3" />
              分类: {category}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 p-0"
                onClick={() => setCategory("")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {tag && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Tag className="w-3 h-3" />
              标签: {tag}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 p-0"
                onClick={() => setTag("")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      </form>
      
      {/* 搜索结果 */}
      {isLoading ? (
        <div className="space-y-6">
          <div className="animate-pulse h-6 w-48 bg-muted rounded"></div>
          <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>
            {Array.from({ length: 6 }).map((_, i) => (
              <ArticleCardSkeleton key={i} />
            ))}
          </ResponsiveGrid>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="py-8 text-center">
            <div className="text-destructive mb-2">搜索出错</div>
            <p className="text-muted-foreground">{error}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={performSearch}
            >
              重试
            </Button>
          </CardContent>
        </Card>
      ) : results ? (
        <div className="space-y-6">
          {/* 结构化数据 */}
          {results.articles.length > 0 && (
            <SearchResultsStructuredData
              query={results.query}
              results={results.articles}
            />
          )}
          
          {/* 搜索结果标签页 */}
          <Tabs defaultValue="all" value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
            <TabsList>
              <TabsTrigger value="all">
                全部 ({results.pagination.total})
              </TabsTrigger>
              <TabsTrigger value="articles">
                文章 ({results.articles.length})
              </TabsTrigger>
              <TabsTrigger value="tags">
                标签 ({results.suggestions.length})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="space-y-6">
              {/* 搜索建议 */}
              {results.suggestions.length > 0 && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">您可能想搜索:</h3>
                  <div className="flex flex-wrap gap-2">
                    {results.suggestions.map((suggestion) => (
                      <Badge
                        key={suggestion}
                        variant="outline"
                        className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                        onClick={() => handleSuggestionClick(suggestion)}
                      >
                        {suggestion}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {/* 文章结果 */}
              {results.articles.length > 0 ? (
                <>
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold">
                      搜索结果 ({results.pagination.total})
                    </h2>
                    <div className="text-sm text-muted-foreground">
                      第 {results.pagination.page} 页，共 {results.pagination.totalPages} 页
                    </div>
                  </div>
                  
                  <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>
                    {results.articles.map((article) => (
                      <ArticleCard
                        key={article.id}
                        article={article}
                        variant="default"
                      />
                    ))}
                  </ResponsiveGrid>
                  
                  {/* 分页 */}
                  {results.pagination.totalPages > 1 && (
                    <div className="flex justify-center items-center gap-2 mt-6">
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(page - 1)}
                        disabled={page <= 1}
                      >
                        上一页
                      </Button>
                      
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, results.pagination.totalPages) }).map((_, i) => {
                          const pageNum = i + 1
                          return (
                            <Button
                              key={pageNum}
                              variant={pageNum === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                            >
                              {pageNum}
                            </Button>
                          )
                        })}
                      </div>
                      
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(page + 1)}
                        disabled={page >= results.pagination.totalPages}
                      >
                        下一页
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-xl font-semibold mb-2">未找到结果</h3>
                  <p className="text-muted-foreground mb-4">
                    没有找到与 "{query}" 相关的内容
                  </p>
                  <Button variant="outline" onClick={handleClearSearch}>
                    清除搜索
                  </Button>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="articles">
              {results.articles.length > 0 ? (
                <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>
                  {results.articles.map((article) => (
                    <ArticleCard
                      key={article.id}
                      article={article}
                      variant="default"
                    />
                  ))}
                </ResponsiveGrid>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">没有找到相关文章</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="tags">
              {results.suggestions.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {results.suggestions.map((suggestion) => (
                    <Badge
                      key={suggestion}
                      className="text-base py-2 px-4 cursor-pointer"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion}
                    </Badge>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">没有找到相关标签</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      ) : query ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent mx-auto"></div>
          <p className="mt-4 text-muted-foreground">搜索中...</p>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold mb-2">搜索网站内容</h3>
          <p className="text-muted-foreground">
            输入关键词开始搜索文章、标签和分类
          </p>
        </div>
      )}
    </div>
  )
}

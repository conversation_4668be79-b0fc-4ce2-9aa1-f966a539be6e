{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// 格式化日期\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n// 格式化相对时间\nexport function formatRelativeTime(date: Date | string): string {\n  const d = new Date(date);\n  const now = new Date();\n  const diff = now.getTime() - d.getTime();\n\n  const seconds = Math.floor(diff / 1000);\n  const minutes = Math.floor(seconds / 60);\n  const hours = Math.floor(minutes / 60);\n  const days = Math.floor(hours / 24);\n  const months = Math.floor(days / 30);\n  const years = Math.floor(months / 12);\n\n  if (years > 0) return `${years}年前`;\n  if (months > 0) return `${months}个月前`;\n  if (days > 0) return `${days}天前`;\n  if (hours > 0) return `${hours}小时前`;\n  if (minutes > 0) return `${minutes}分钟前`;\n  return '刚刚';\n}\n\n// 生成 slug\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // 移除特殊字符\n    .replace(/[\\s_-]+/g, '-') // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符\n}\n\n// 截取文本\nexport function truncateText(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.slice(0, length) + '...';\n}\n\n// 格式化文件大小\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// 验证邮箱\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 生成随机字符串\nexport function generateRandomString(length: number): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,IAAI,OAAO,KAAK,EAAE,OAAO;IAEtC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;IAClC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,MAAM,SAAS,KAAK,KAAK,CAAC,OAAO;IACjC,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;IAElC,IAAI,QAAQ,GAAG,OAAO,GAAG,MAAM,EAAE,CAAC;IAClC,IAAI,SAAS,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC;IACrC,IAAI,OAAO,GAAG,OAAO,GAAG,KAAK,EAAE,CAAC;IAChC,IAAI,QAAQ,GAAG,OAAO,GAAG,MAAM,GAAG,CAAC;IACnC,IAAI,UAAU,GAAG,OAAO,GAAG,QAAQ,GAAG,CAAC;IACvC,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,SAAS;KAClC,OAAO,CAAC,YAAY,KAAK,eAAe;KACxC,OAAO,CAAC,YAAY,KAAK,cAAc;AAC5C;AAGO,SAAS,aAAa,IAAY,EAAE,MAAc;IACvD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/config/site.ts"], "sourcesContent": ["export const siteConfig = {\n  name: \"现代博客系统\",\n  description: \"基于 Next.js 的现代化博客系统，支持 AI 摘要、多语言、深色模式等功能\",\n  url: \"https://your-domain.com\",\n  ogImage: \"https://your-domain.com/og.jpg\",\n  links: {\n    github: \"https://github.com/yourusername/your-repo\",\n    twitter: \"https://twitter.com/yourusername\",\n  },\n  author: {\n    name: \"Your Name\",\n    email: \"<EMAIL>\",\n    twitter: \"@yourusername\",\n  },\n  // 主题配置\n  theme: {\n    defaultTheme: \"light\" as const,\n    enableSystemTheme: true,\n  },\n  // 语言配置\n  i18n: {\n    defaultLocale: \"zh\" as const,\n    locales: [\"zh\", \"en\"] as const,\n  },\n  // 分页配置\n  pagination: {\n    articlesPerPage: 10,\n    commentsPerPage: 20,\n    filesPerPage: 20,\n  },\n  // 文件上传配置\n  upload: {\n    maxFileSize: 10 * 1024 * 1024, // 10MB\n    allowedTypes: [\n      \"image/jpeg\",\n      \"image/png\",\n      \"image/gif\",\n      \"image/webp\",\n      \"application/pdf\",\n      \"text/plain\",\n      \"text/markdown\",\n    ],\n  },\n  // SEO 配置\n  seo: {\n    titleTemplate: \"%s | 现代博客系统\",\n    defaultTitle: \"现代博客系统\",\n    description: \"基于 Next.js 的现代化博客系统\",\n    openGraph: {\n      type: \"website\",\n      locale: \"zh_CN\",\n      url: \"https://your-domain.com\",\n      siteName: \"现代博客系统\",\n    },\n    twitter: {\n      handle: \"@yourusername\",\n      site: \"@yourusername\",\n      cardType: \"summary_large_image\",\n    },\n  },\n  // 功能开关\n  features: {\n    enableComments: true,\n    enableSearch: true,\n    enableAnalytics: true,\n    enableAISummary: true,\n    enableFileManagement: true,\n    enableMultiLanguage: true,\n    enableDarkMode: true,\n  },\n  // 外部服务配置\n  services: {\n    cloudflareWorker: {\n      authUrl: process.env.CLOUDFLARE_AUTH_URL || \"\",\n      fileUrl: process.env.CLOUDFLARE_FILE_URL || \"\",\n    },\n    github: {\n      clientId: process.env.GITHUB_CLIENT_ID || \"\",\n      clientSecret: process.env.GITHUB_CLIENT_SECRET || \"\",\n    },\n    analytics: {\n      googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID || \"\",\n      plausibleDomain: process.env.PLAUSIBLE_DOMAIN || \"\",\n    },\n  },\n};\n\nexport type SiteConfig = typeof siteConfig;\n"], "names": [], "mappings": ";;;AAyEe;AAzER,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK;IACL,SAAS;IACT,OAAO;QACL,QAAQ;QACR,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,OAAO;IACP,OAAO;QACL,cAAc;QACd,mBAAmB;IACrB;IACA,OAAO;IACP,MAAM;QACJ,eAAe;QACf,SAAS;YAAC;YAAM;SAAK;IACvB;IACA,OAAO;IACP,YAAY;QACV,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;IAChB;IACA,SAAS;IACT,QAAQ;QACN,aAAa,KAAK,OAAO;QACzB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;IACT,KAAK;QACH,eAAe;QACf,cAAc;QACd,aAAa;QACb,WAAW;YACT,MAAM;YACN,QAAQ;YACR,KAAK;YACL,UAAU;QACZ;QACA,SAAS;YACP,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;IACF;IACA,OAAO;IACP,UAAU;QACR,gBAAgB;QAChB,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,sBAAsB;QACtB,qBAAqB;QACrB,gBAAgB;IAClB;IACA,SAAS;IACT,UAAU;QACR,kBAAkB;YAChB,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;YAC5C,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;QAC9C;QACA,QAAQ;YACN,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,WAAW;YACT,mBAAmB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;YACtD,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI;QACnD;IACF;AACF", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/app/auth/signin/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { signIn, getSession } from \"next-auth/react\"\nimport { useRouter, useSearchParams } from \"next/navigation\"\nimport { Gith<PERSON>, Loader2 } from \"lucide-react\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Alert, AlertDescription } from \"@/components/ui/alert\"\nimport { siteConfig } from \"@/config/site\"\n\nexport default function SignInPage() {\n  const router = useRouter()\n  const searchParams = useSearchParams()\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  \n  const callbackUrl = searchParams.get('callbackUrl') || '/'\n  const errorParam = searchParams.get('error')\n\n  useEffect(() => {\n    // 检查是否已经登录\n    getSession().then((session) => {\n      if (session) {\n        router.push(callbackUrl)\n      }\n    })\n  }, [router, callbackUrl])\n\n  useEffect(() => {\n    // 处理登录错误\n    if (errorParam) {\n      switch (errorParam) {\n        case 'OAuthSignin':\n          setError('OAuth 登录失败，请重试')\n          break\n        case 'OAuthCallback':\n          setError('OAuth 回调失败，请重试')\n          break\n        case 'OAuthCreateAccount':\n          setError('创建账户失败，请重试')\n          break\n        case 'EmailCreateAccount':\n          setError('邮箱账户创建失败，请重试')\n          break\n        case 'Callback':\n          setError('回调失败，请重试')\n          break\n        case 'OAuthAccountNotLinked':\n          setError('该邮箱已与其他账户关联')\n          break\n        case 'EmailSignin':\n          setError('邮箱登录失败，请重试')\n          break\n        case 'CredentialsSignin':\n          setError('凭据登录失败，请检查用户名和密码')\n          break\n        case 'SessionRequired':\n          setError('需要登录才能访问此页面')\n          break\n        default:\n          setError('登录失败，请重试')\n      }\n    }\n  }, [errorParam])\n\n  const handleGithubSignIn = async () => {\n    try {\n      setIsLoading(true)\n      setError(null)\n      \n      const result = await signIn('github', {\n        callbackUrl,\n        redirect: false,\n      })\n      \n      if (result?.error) {\n        setError('GitHub 登录失败，请重试')\n      } else if (result?.url) {\n        router.push(result.url)\n      }\n    } catch (error) {\n      console.error('Sign in error:', error)\n      setError('登录过程中发生错误，请重试')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <CardTitle className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n            欢迎回来\n          </CardTitle>\n          <CardDescription>\n            登录到 {siteConfig.name}\n          </CardDescription>\n        </CardHeader>\n        \n        <CardContent className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n          \n          <Button\n            onClick={handleGithubSignIn}\n            disabled={isLoading}\n            className=\"w-full\"\n            size=\"lg\"\n          >\n            {isLoading ? (\n              <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n            ) : (\n              <Github className=\"w-4 h-4 mr-2\" />\n            )}\n            使用 GitHub 登录\n          </Button>\n          \n          <div className=\"text-center text-sm text-muted-foreground\">\n            <p>\n              登录即表示您同意我们的\n              <a href=\"/terms\" className=\"underline hover:text-primary\">\n                服务条款\n              </a>\n              和\n              <a href=\"/privacy\" className=\"underline hover:text-primary\">\n                隐私政策\n              </a>\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;AAEA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc,aAAa,GAAG,CAAC,kBAAkB;IACvD,MAAM,aAAa,aAAa,GAAG,CAAC;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,WAAW;YACX,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,IAAI,IAAI;wCAAC,CAAC;oBACjB,IAAI,SAAS;wBACX,OAAO,IAAI,CAAC;oBACd;gBACF;;QACF;+BAAG;QAAC;QAAQ;KAAY;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,SAAS;YACT,IAAI,YAAY;gBACd,OAAQ;oBACN,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF;wBACE,SAAS;gBACb;YACF;QACF;+BAAG;QAAC;KAAW;IAEf,MAAM,qBAAqB;QACzB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBACpC;gBACA,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;YACX,OAAO,IAAI,QAAQ,KAAK;gBACtB,OAAO,IAAI,CAAC,OAAO,GAAG;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAgG;;;;;;sCAGrH,6LAAC,mIAAA,CAAA,kBAAe;;gCAAC;gCACV,wHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;;;;;;;;8BAIxB,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,uBACC,6LAAC;4BAAM,SAAQ;sCACb,cAAA,6LAAC;0CAAkB;;;;;;;;;;;sCAIvB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,MAAK;;gCAEJ,0BACC,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAEnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAClB;;;;;;;sCAIJ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;oCAAE;kDAED,6LAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA+B;;;;;;oCAEtD;kDAEJ,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E;GA/HwB;;QACP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAFd", "debugId": null}}]}
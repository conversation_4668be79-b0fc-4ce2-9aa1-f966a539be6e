name = "modern-blog-api"
main = "src/index.ts"
compatibility_date = "2024-12-01"
compatibility_flags = ["nodejs_compat"]

# 环境变量配置
[env.production.vars]
ENVIRONMENT = "production"
FRONTEND_URL = "https://your-domain.com"

[env.development.vars]
ENVIRONMENT = "development"
FRONTEND_URL = "http://localhost:3000"

# KV 命名空间 - 用于缓存和会话存储
[[kv_namespaces]]
binding = "CACHE"
id = "your-cache-kv-namespace-id"
preview_id = "your-cache-kv-preview-id"

[[kv_namespaces]]
binding = "SESSIONS"
id = "your-sessions-kv-namespace-id"
preview_id = "your-sessions-kv-preview-id"

# D1 数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "modern-blog-db"
database_id = "your-d1-database-id"

# R2 存储桶绑定
[[r2_buckets]]
binding = "STORAGE"
bucket_name = "modern-blog-storage"
preview_bucket_name = "modern-blog-storage-preview"

# Workers AI 绑定
[ai]
binding = "AI"

# 环境变量 (敏感信息)
# 需要通过 wrangler secret put 命令设置:
# wrangler secret put GITHUB_CLIENT_SECRET
# wrangler secret put JWT_SECRET
# wrangler secret put ADMIN_EMAILS

# 示例命令:
# wrangler secret put GITHUB_CLIENT_SECRET --env development
# wrangler secret put JWT_SECRET --env development  
# wrangler secret put ADMIN_EMAILS --env development

# 开发环境配置
[env.development]
vars = { ENVIRONMENT = "development", FRONTEND_URL = "http://localhost:3000" }

# 生产环境配置  
[env.production]
vars = { ENVIRONMENT = "production", FRONTEND_URL = "https://your-domain.com" }

# 构建配置
[build]
command = "npm run build"

# 部署配置
[triggers]
crons = ["0 0 * * *"] # 每天午夜运行清理任务

"use client"

import { useState } from "react"
import { Plus, Edit, Trash2, ExternalLink, Eye, EyeOff } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { OptimizedImage } from "@/components/ui/optimized-image"
import { getFriendLinks, getFriendLinkCategories } from "@/lib/mock-data"
import type { FriendLink } from "@/types"

export function FriendLinkManager() {
  const [friendLinks, setFriendLinks] = useState(getFriendLinks({ status: "all" }))
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingLink, setEditingLink] = useState<FriendLink | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    url: "",
    description: "",
    avatar: "",
    category: "",
    status: "active" as const,
  })

  const categories = getFriendLinkCategories()

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (editingLink) {
      // 更新友链
      setFriendLinks(links => 
        links.map(link => 
          link.id === editingLink.id 
            ? { ...link, ...formData, updatedAt: new Date() }
            : link
        )
      )
    } else {
      // 添加新友链
      const newLink: FriendLink = {
        id: Date.now().toString(),
        ...formData,
        createdAt: new Date(),
        updatedAt: new Date(),
        order: friendLinks.length + 1,
      }
      setFriendLinks(links => [...links, newLink])
    }
    
    handleCloseDialog()
  }

  // 处理编辑
  const handleEdit = (link: FriendLink) => {
    setEditingLink(link)
    setFormData({
      name: link.name,
      url: link.url,
      description: link.description,
      avatar: link.avatar || "",
      category: link.category,
      status: link.status,
    })
    setIsDialogOpen(true)
  }

  // 处理删除
  const handleDelete = (id: string) => {
    if (confirm("确定要删除这个友情链接吗？")) {
      setFriendLinks(links => links.filter(link => link.id !== id))
    }
  }

  // 切换状态
  const handleToggleStatus = (id: string) => {
    setFriendLinks(links =>
      links.map(link =>
        link.id === id
          ? { 
              ...link, 
              status: link.status === "active" ? "inactive" : "active",
              updatedAt: new Date()
            }
          : link
      )
    )
  }

  // 关闭对话框
  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingLink(null)
    setFormData({
      name: "",
      url: "",
      description: "",
      avatar: "",
      category: "",
      status: "active",
    })
  }

  // 获取状态徽章
  const getStatusBadge = (status: FriendLink["status"]) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">活跃</Badge>
      case "inactive":
        return <Badge variant="secondary">停用</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100">待审核</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>友情链接管理</CardTitle>
            <CardDescription>
              管理网站的友情链接，包括添加、编辑和删除
            </CardDescription>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => setEditingLink(null)}>
                <Plus className="w-4 h-4 mr-2" />
                添加友链
              </Button>
            </DialogTrigger>
            
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingLink ? "编辑友情链接" : "添加友情链接"}
                </DialogTitle>
                <DialogDescription>
                  填写友情链接的基本信息
                </DialogDescription>
              </DialogHeader>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">网站名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="请输入网站名称"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="url">网站地址</Label>
                  <Input
                    id="url"
                    type="url"
                    value={formData.url}
                    onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                    placeholder="https://example.com"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">网站描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="请输入网站描述"
                    rows={3}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="avatar">网站图标</Label>
                  <Input
                    id="avatar"
                    type="url"
                    value={formData.avatar}
                    onChange={(e) => setFormData(prev => ({ ...prev, avatar: e.target.value }))}
                    placeholder="https://example.com/favicon.ico"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="category">分类</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="status">状态</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: any) => setFormData(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">活跃</SelectItem>
                      <SelectItem value="inactive">停用</SelectItem>
                      <SelectItem value="pending">待审核</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={handleCloseDialog}>
                    取消
                  </Button>
                  <Button type="submit">
                    {editingLink ? "更新" : "添加"}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>网站</TableHead>
                <TableHead>分类</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {friendLinks.map((link) => (
                <TableRow key={link.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded overflow-hidden bg-muted flex-shrink-0">
                        {link.avatar ? (
                          <OptimizedImage
                            src={link.avatar}
                            alt={`${link.name} 图标`}
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-xs font-bold">
                            {link.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="font-medium">{link.name}</div>
                        <div className="text-sm text-muted-foreground line-clamp-1">
                          {link.description}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{link.category}</Badge>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(link.status)}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {link.createdAt.toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => window.open(link.url, "_blank")}
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleToggleStatus(link.id)}
                      >
                        {link.status === "active" ? (
                          <EyeOff className="w-4 h-4" />
                        ) : (
                          <Eye className="w-4 h-4" />
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleEdit(link)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDelete(link.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        
        {friendLinks.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            暂无友情链接，点击上方按钮添加第一个友链
          </div>
        )}
      </CardContent>
    </Card>
  )
}

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ap } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MainLayout } from "@/components/layout/main-layout"
import { PageContainer } from "@/components/layout/page-container"
import { ResponsiveGrid } from "@/components/ui/responsive-grid"
import { FadeIn, SlideIn } from "@/components/ui/page-transition"
import { WebsiteStructuredData, OrganizationStructuredData } from "@/components/seo/structured-data"
import { siteConfig } from "@/config/site"
import { generateBaseMetadata } from "@/lib/seo"
import type { Metadata } from "next"

// 生成页面元数据
export function generateMetadata(): Metadata {
  return generateBaseMetadata({
    title: "首页",
    description: siteConfig.description,
    keywords: ["博客", "Next.js", "React", "TypeScript", "Tailwind CSS", "AI", "现代化"],
  })
}

export default function Home() {
  return (
    <>
      {/* 结构化数据 */}
      <WebsiteStructuredData />
      <OrganizationStructuredData />

      <MainLayout>
      <PageContainer maxWidth="full">
        {/* Hero Section */}
        <section className="text-center py-12 md:py-20">
          <div className="max-w-4xl mx-auto">
            <FadeIn delay={100}>
              <Badge variant="secondary" className="mb-4">
                <Sparkles className="w-4 h-4 mr-1" />
                现代化博客系统
              </Badge>
            </FadeIn>
            <FadeIn delay={200}>
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-6">
                {siteConfig.name}
              </h1>
            </FadeIn>
            <FadeIn delay={300}>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                {siteConfig.description}
              </p>
            </FadeIn>
            <FadeIn delay={400}>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <BookOpen className="w-4 h-4 mr-2" />
                  开始阅读
                </Button>
                <Button size="lg" variant="outline">
                  <Users className="w-4 h-4 mr-2" />
                  了解更多
                </Button>
              </div>
            </FadeIn>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-12">
          <SlideIn delay={500}>
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">强大功能</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                集成最新技术，为您提供最佳的博客体验
              </p>
            </div>
          </SlideIn>

          <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>
            <Card className="group hover:shadow-lg transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <CardTitle>AI 智能摘要</CardTitle>
                <CardDescription>
                  自动生成文章摘要，提升阅读体验
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="group hover:shadow-lg transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <CardTitle>深色模式</CardTitle>
                <CardDescription>
                  支持浅色/深色主题切换，保护您的眼睛
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="group hover:shadow-lg transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4">
                  <BookOpen className="w-6 h-6 text-white" />
                </div>
                <CardTitle>响应式设计</CardTitle>
                <CardDescription>
                  完美适配手机、平板和桌面设备
                </CardDescription>
              </CardHeader>
            </Card>
          </ResponsiveGrid>
        </section>

        {/* Latest Articles Section */}
        <section className="py-12">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold">最新文章</h2>
            <Button variant="outline">查看全部</Button>
          </div>

          <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>
            {/* 这里将来会显示真实的文章数据 */}
            {[1, 2, 3].map((i) => (
              <Card key={i} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline">技术</Badge>
                    <Badge variant="secondary">Next.js</Badge>
                  </div>
                  <CardTitle className="line-clamp-2">
                    示例文章标题 {i} - 现代化博客系统的构建之路
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground text-sm line-clamp-3">
                    这是一篇关于如何构建现代化博客系统的文章，涵盖了 Next.js、TypeScript、Tailwind CSS 等技术栈的使用...
                  </p>
                </CardContent>
              </Card>
            ))}
          </ResponsiveGrid>
        </section>
      </PageContainer>
    </MainLayout>
    </>
  )
}

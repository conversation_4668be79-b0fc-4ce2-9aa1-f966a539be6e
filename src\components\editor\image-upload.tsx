'use client'

import { useState, useRef, useCallback } from 'react'
import { useTranslations } from 'next-intl'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Upload, 
  Image as ImageIcon, 
  X, 
  Copy, 
  Check, 
  AlertCircle,
  FileImage,
  Loader2,
  Link,
  Crop,
  RotateCw,
  ZoomIn,
  ZoomOut
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useToast } from '@/components/ui/toast'
import { cn } from '@/lib/utils'

interface ImageUploadProps {
  onImageInsert: (markdown: string) => void
  maxFileSize?: number // MB
  allowedTypes?: string[]
  className?: string
}

interface UploadedImage {
  id: string
  url: string
  filename: string
  size: number
  alt: string
  title?: string
  width?: number
  height?: number
}

interface UploadProgress {
  id: string
  filename: string
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
}

export function ImageUpload({
  onImageInsert,
  maxFileSize = 10,
  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  className
}: ImageUploadProps) {
  const t = useTranslations('imageUpload')
  const { showToast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dropZoneRef = useRef<HTMLDivElement>(null)
  
  const [isDragOver, setIsDragOver] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([])
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([])
  const [selectedImage, setSelectedImage] = useState<UploadedImage | null>(null)
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false)
  const [imageUrl, setImageUrl] = useState('')
  const [imageAlt, setImageAlt] = useState('')
  const [imageTitle, setImageTitle] = useState('')

  // 验证文件
  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return t('invalidFileType')
    }
    
    if (file.size > maxFileSize * 1024 * 1024) {
      return t('fileTooLarge', { size: maxFileSize })
    }
    
    return null
  }

  // 上传文件
  const uploadFile = async (file: File): Promise<UploadedImage | null> => {
    const uploadId = Math.random().toString(36).substr(2, 9)
    
    // 添加上传进度
    setUploadProgress(prev => [...prev, {
      id: uploadId,
      filename: file.name,
      progress: 0,
      status: 'uploading'
    }])

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'article-image')

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          setUploadProgress(prev => prev.map(item => 
            item.id === uploadId ? { ...item, progress } : item
          ))
        }
      })

      if (!response.ok) {
        throw new Error(t('uploadFailed'))
      }

      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || t('uploadFailed'))
      }

      // 创建图片对象
      const img = new Image()
      img.onload = () => {
        const uploadedImage: UploadedImage = {
          id: data.data.id,
          url: data.data.url,
          filename: file.name,
          size: file.size,
          alt: '',
          width: img.width,
          height: img.height
        }

        setUploadedImages(prev => [...prev, uploadedImage])
        
        // 更新上传状态
        setUploadProgress(prev => prev.map(item => 
          item.id === uploadId ? { ...item, status: 'success' as const } : item
        ))

        // 3秒后移除进度条
        setTimeout(() => {
          setUploadProgress(prev => prev.filter(item => item.id !== uploadId))
        }, 3000)
      }
      
      img.src = data.data.url
      
      return null // 将在 onload 中处理
      
    } catch (error) {
      console.error('Upload error:', error)
      
      // 更新上传状态
      setUploadProgress(prev => prev.map(item => 
        item.id === uploadId ? { 
          ...item, 
          status: 'error' as const, 
          error: error instanceof Error ? error.message : t('uploadFailed')
        } : item
      ))

      showToast.error(error instanceof Error ? error.message : t('uploadFailed'))
      return null
    }
  }

  // 处理文件选择
  const handleFileSelect = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files)
    
    for (const file of fileArray) {
      const error = validateFile(file)
      if (error) {
        showToast.error(error)
        continue
      }
      
      await uploadFile(file)
    }
  }, [maxFileSize, allowedTypes, showToast])

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFileSelect(files)
    }
  }, [handleFileSelect])

  // 插入图片
  const insertImage = (image: UploadedImage, alt?: string, title?: string) => {
    const altText = alt || image.alt || image.filename
    const titleText = title || image.title || ''
    
    let markdown = `![${altText}](${image.url})`
    if (titleText) {
      markdown = `![${altText}](${image.url} "${titleText}")`
    }
    
    onImageInsert(markdown)
    setIsImageDialogOpen(false)
  }

  // 插入URL图片
  const insertImageFromUrl = () => {
    if (!imageUrl) {
      showToast.error(t('pleaseEnterUrl'))
      return
    }

    let markdown = `![${imageAlt || t('image')}](${imageUrl})`
    if (imageTitle) {
      markdown = `![${imageAlt || t('image')}](${imageUrl} "${imageTitle}")`
    }
    
    onImageInsert(markdown)
    setImageUrl('')
    setImageAlt('')
    setImageTitle('')
    setIsImageDialogOpen(false)
  }

  // 复制图片URL
  const copyImageUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      showToast.success(t('urlCopied'))
    } catch (error) {
      showToast.error(t('copyFailed'))
    }
  }

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setIsImageDialogOpen(true)}
        className={cn("flex items-center gap-2", className)}
      >
        <ImageIcon className="h-4 w-4" />
        {t('insertImage')}
      </Button>

      <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>{t('insertImage')}</DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="upload">{t('upload')}</TabsTrigger>
              <TabsTrigger value="url">{t('fromUrl')}</TabsTrigger>
              <TabsTrigger value="gallery">{t('gallery')}</TabsTrigger>
            </TabsList>

            {/* 上传标签页 */}
            <TabsContent value="upload" className="space-y-4">
              {/* 拖拽上传区域 */}
              <Card
                ref={dropZoneRef}
                className={cn(
                  "border-2 border-dashed transition-colors cursor-pointer",
                  isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25"
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Upload className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-lg font-medium mb-2">{t('dragDropOrClick')}</p>
                  <p className="text-sm text-muted-foreground mb-4">
                    {t('supportedFormats')}: JPEG, PNG, GIF, WebP
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {t('maxFileSize')}: {maxFileSize}MB
                  </p>
                </CardContent>
              </Card>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={allowedTypes.join(',')}
                onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
                className="hidden"
              />

              {/* 上传进度 */}
              <AnimatePresence>
                {uploadProgress.map((progress) => (
                  <motion.div
                    key={progress.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <FileImage className="h-4 w-4" />
                            <span className="text-sm font-medium">{progress.filename}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {progress.status === 'uploading' && (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            )}
                            {progress.status === 'success' && (
                              <Check className="h-4 w-4 text-green-500" />
                            )}
                            {progress.status === 'error' && (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                            <Badge variant={
                              progress.status === 'success' ? 'default' :
                              progress.status === 'error' ? 'destructive' : 'secondary'
                            }>
                              {progress.status === 'uploading' ? `${progress.progress}%` : 
                               progress.status === 'success' ? t('success') : t('error')}
                            </Badge>
                          </div>
                        </div>
                        {progress.status === 'uploading' && (
                          <Progress value={progress.progress} className="h-2" />
                        )}
                        {progress.status === 'error' && progress.error && (
                          <p className="text-sm text-red-500 mt-2">{progress.error}</p>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </TabsContent>

            {/* URL标签页 */}
            <TabsContent value="url" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="imageUrl">{t('imageUrl')}</Label>
                  <Input
                    id="imageUrl"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                <div>
                  <Label htmlFor="imageAlt">{t('altText')}</Label>
                  <Input
                    id="imageAlt"
                    value={imageAlt}
                    onChange={(e) => setImageAlt(e.target.value)}
                    placeholder={t('altTextPlaceholder')}
                  />
                </div>
                <div>
                  <Label htmlFor="imageTitle">{t('title')} ({t('optional')})</Label>
                  <Input
                    id="imageTitle"
                    value={imageTitle}
                    onChange={(e) => setImageTitle(e.target.value)}
                    placeholder={t('titlePlaceholder')}
                  />
                </div>
                <Button onClick={insertImageFromUrl} className="w-full">
                  <Link className="h-4 w-4 mr-2" />
                  {t('insertFromUrl')}
                </Button>
              </div>
            </TabsContent>

            {/* 图库标签页 */}
            <TabsContent value="gallery" className="space-y-4">
              {uploadedImages.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-lg font-medium mb-2">{t('noImagesYet')}</p>
                    <p className="text-sm text-muted-foreground">
                      {t('uploadFirstImage')}
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                  {uploadedImages.map((image) => (
                    <Card key={image.id} className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow">
                      <div className="aspect-square relative">
                        <img
                          src={image.url}
                          alt={image.alt || image.filename}
                          className="w-full h-full object-cover"
                          onClick={() => setSelectedImage(image)}
                        />
                        <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 hover:opacity-100">
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={(e) => {
                                e.stopPropagation()
                                insertImage(image)
                              }}
                            >
                              {t('insert')}
                            </Button>
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={(e) => {
                                e.stopPropagation()
                                copyImageUrl(image.url)
                              }}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                      <CardContent className="p-2">
                        <p className="text-xs text-muted-foreground truncate">
                          {image.filename}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {image.width} × {image.height}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* 图片详情对话框 */}
      {selectedImage && (
        <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{t('imageDetails')}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="aspect-video relative bg-muted rounded-lg overflow-hidden">
                <img
                  src={selectedImage.url}
                  alt={selectedImage.alt || selectedImage.filename}
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label>{t('filename')}</Label>
                  <p className="text-muted-foreground">{selectedImage.filename}</p>
                </div>
                <div>
                  <Label>{t('dimensions')}</Label>
                  <p className="text-muted-foreground">
                    {selectedImage.width} × {selectedImage.height}
                  </p>
                </div>
                <div>
                  <Label>{t('fileSize')}</Label>
                  <p className="text-muted-foreground">
                    {(selectedImage.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <div>
                  <Label>{t('url')}</Label>
                  <div className="flex items-center gap-2">
                    <p className="text-muted-foreground truncate flex-1">
                      {selectedImage.url}
                    </p>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyImageUrl(selectedImage.url)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={() => insertImage(selectedImage)}
                  className="flex-1"
                >
                  {t('insertImage')}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => copyImageUrl(selectedImage.url)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  {t('copyUrl')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}

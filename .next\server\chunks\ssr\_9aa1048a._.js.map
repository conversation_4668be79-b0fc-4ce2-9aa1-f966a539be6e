{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// 格式化日期\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n// 格式化相对时间\nexport function formatRelativeTime(date: Date | string): string {\n  const d = new Date(date);\n  const now = new Date();\n  const diff = now.getTime() - d.getTime();\n\n  const seconds = Math.floor(diff / 1000);\n  const minutes = Math.floor(seconds / 60);\n  const hours = Math.floor(minutes / 60);\n  const days = Math.floor(hours / 24);\n  const months = Math.floor(days / 30);\n  const years = Math.floor(months / 12);\n\n  if (years > 0) return `${years}年前`;\n  if (months > 0) return `${months}个月前`;\n  if (days > 0) return `${days}天前`;\n  if (hours > 0) return `${hours}小时前`;\n  if (minutes > 0) return `${minutes}分钟前`;\n  return '刚刚';\n}\n\n// 生成 slug\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // 移除特殊字符\n    .replace(/[\\s_-]+/g, '-') // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符\n}\n\n// 截取文本\nexport function truncateText(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.slice(0, length) + '...';\n}\n\n// 格式化文件大小\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// 验证邮箱\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 生成随机字符串\nexport function generateRandomString(length: number): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,IAAI,OAAO,KAAK,EAAE,OAAO;IAEtC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;IAClC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,MAAM,SAAS,KAAK,KAAK,CAAC,OAAO;IACjC,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;IAElC,IAAI,QAAQ,GAAG,OAAO,GAAG,MAAM,EAAE,CAAC;IAClC,IAAI,SAAS,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC;IACrC,IAAI,OAAO,GAAG,OAAO,GAAG,KAAK,EAAE,CAAC;IAChC,IAAI,QAAQ,GAAG,OAAO,GAAG,MAAM,GAAG,CAAC;IACnC,IAAI,UAAU,GAAG,OAAO,GAAG,QAAQ,GAAG,CAAC;IACvC,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,SAAS;KAClC,OAAO,CAAC,YAAY,KAAK,eAAe;KACxC,OAAO,CAAC,YAAY,KAAK,cAAc;AAC5C;AAGO,SAAS,aAAa,IAAY,EAAE,MAAc;IACvD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx <module evaluation>\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/layout/page-container.tsx"], "sourcesContent": ["import { ReactNode } from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface PageContainerProps {\n  children: ReactNode\n  className?: string\n  maxWidth?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"full\"\n}\n\nexport function PageContainer({ \n  children, \n  className,\n  maxWidth = \"2xl\" \n}: PageContainerProps) {\n  const maxWidthClasses = {\n    sm: \"max-w-sm\",\n    md: \"max-w-md\", \n    lg: \"max-w-lg\",\n    xl: \"max-w-xl\",\n    \"2xl\": \"max-w-2xl\",\n    full: \"max-w-full\"\n  }\n\n  return (\n    <div className={cn(\n      \"mx-auto w-full px-4 sm:px-6 lg:px-8\",\n      maxWidthClasses[maxWidth],\n      className\n    )}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,cAAc,EAC5B,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EACG;IACnB,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uCACA,eAAe,CAAC,SAAS,EACzB;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/responsive-grid.tsx"], "sourcesContent": ["import { ReactNode } from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ResponsiveGridProps {\n  children: ReactNode\n  className?: string\n  cols?: {\n    default?: number\n    sm?: number\n    md?: number\n    lg?: number\n    xl?: number\n  }\n  gap?: number\n}\n\nexport function ResponsiveGrid({ \n  children, \n  className,\n  cols = { default: 1, md: 2, lg: 3 },\n  gap = 6\n}: ResponsiveGridProps) {\n  const gridClasses = [\n    `grid`,\n    `gap-${gap}`,\n    cols.default && `grid-cols-${cols.default}`,\n    cols.sm && `sm:grid-cols-${cols.sm}`,\n    cols.md && `md:grid-cols-${cols.md}`,\n    cols.lg && `lg:grid-cols-${cols.lg}`,\n    cols.xl && `xl:grid-cols-${cols.xl}`,\n  ].filter(Boolean).join(' ')\n\n  return (\n    <div className={cn(gridClasses, className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface ResponsiveContainerProps {\n  children: ReactNode\n  className?: string\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"full\"\n}\n\nexport function ResponsiveContainer({ \n  children, \n  className,\n  size = \"2xl\" \n}: ResponsiveContainerProps) {\n  const sizeClasses = {\n    sm: \"max-w-sm\",\n    md: \"max-w-md\",\n    lg: \"max-w-lg\", \n    xl: \"max-w-xl\",\n    \"2xl\": \"max-w-2xl\",\n    full: \"max-w-full\"\n  }\n\n  return (\n    <div className={cn(\n      \"mx-auto w-full px-4 sm:px-6 lg:px-8\",\n      sizeClasses[size],\n      className\n    )}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAeO,SAAS,eAAe,EAC7B,QAAQ,EACR,SAAS,EACT,OAAO;IAAE,SAAS;IAAG,IAAI;IAAG,IAAI;AAAE,CAAC,EACnC,MAAM,CAAC,EACa;IACpB,MAAM,cAAc;QAClB,CAAC,IAAI,CAAC;QACN,CAAC,IAAI,EAAE,KAAK;QACZ,KAAK,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAC3C,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACpC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACpC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACpC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;KACrC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAQO,SAAS,oBAAoB,EAClC,QAAQ,EACR,SAAS,EACT,OAAO,KAAK,EACa;IACzB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uCACA,WAAW,CAAC,KAAK,EACjB;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/page-transition.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FadeIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call FadeIn() from the server but FadeIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/page-transition.tsx <module evaluation>\",\n    \"FadeIn\",\n);\nexport const PageTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call PageTransition() from the server but PageTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/page-transition.tsx <module evaluation>\",\n    \"PageTransition\",\n);\nexport const SlideIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call SlideIn() from the server but SlideIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/page-transition.tsx <module evaluation>\",\n    \"SlideIn\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,uEACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uEACA", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/page-transition.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FadeIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call FadeIn() from the server but FadeIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/page-transition.tsx\",\n    \"FadeIn\",\n);\nexport const PageTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call PageTransition() from the server but PageTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/page-transition.tsx\",\n    \"PageTransition\",\n);\nexport const SlideIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call SlideIn() from the server but SlideIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/page-transition.tsx\",\n    \"SlideIn\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,mDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mDACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,mDACA", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/seo/structured-data.tsx"], "sourcesContent": ["import { siteConfig } from \"@/config/site\"\nimport { Article, User } from \"@/types\"\n\ninterface StructuredDataProps {\n  type: \"website\" | \"article\" | \"breadcrumb\" | \"organization\" | \"person\"\n  data?: any\n}\n\n/**\n * 网站结构化数据\n */\nexport function WebsiteStructuredData() {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"WebSite\",\n    name: siteConfig.name,\n    description: siteConfig.description,\n    url: siteConfig.url,\n    potentialAction: {\n      \"@type\": \"SearchAction\",\n      target: {\n        \"@type\": \"EntryPoint\",\n        urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,\n      },\n      \"query-input\": \"required name=search_term_string\",\n    },\n    publisher: {\n      \"@type\": \"Organization\",\n      name: siteConfig.name,\n      logo: {\n        \"@type\": \"ImageObject\",\n        url: `${siteConfig.url}/logo.png`,\n        width: 512,\n        height: 512,\n      },\n    },\n    sameAs: [\n      siteConfig.links.github,\n      siteConfig.links.twitter,\n    ].filter(Boolean),\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 文章结构化数据\n */\nexport function ArticleStructuredData({ article }: { article: Article }) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BlogPosting\",\n    headline: article.title,\n    description: article.summary || article.excerpt,\n    image: article.coverImage ? [article.coverImage] : [],\n    datePublished: article.publishedAt?.toISOString() || article.createdAt.toISOString(),\n    dateModified: article.updatedAt.toISOString(),\n    author: {\n      \"@type\": \"Person\",\n      name: article.author.name,\n      url: `${siteConfig.url}/authors/${article.author.id}`,\n    },\n    publisher: {\n      \"@type\": \"Organization\",\n      name: siteConfig.name,\n      logo: {\n        \"@type\": \"ImageObject\",\n        url: `${siteConfig.url}/logo.png`,\n        width: 512,\n        height: 512,\n      },\n    },\n    mainEntityOfPage: {\n      \"@type\": \"WebPage\",\n      \"@id\": `${siteConfig.url}/articles/${article.slug}`,\n    },\n    keywords: article.tags.join(\", \"),\n    articleSection: article.category,\n    wordCount: article.content.length,\n    commentCount: 0, // 可以根据实际评论数量更新\n    interactionStatistic: [\n      {\n        \"@type\": \"InteractionCounter\",\n        interactionType: \"https://schema.org/ReadAction\",\n        userInteractionCount: article.viewCount,\n      },\n      {\n        \"@type\": \"InteractionCounter\",\n        interactionType: \"https://schema.org/LikeAction\",\n        userInteractionCount: article.likeCount,\n      },\n    ],\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 面包屑导航结构化数据\n */\nexport function BreadcrumbStructuredData({ items }: { \n  items: Array<{ name: string; url: string }> \n}) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BreadcrumbList\",\n    itemListElement: items.map((item, index) => ({\n      \"@type\": \"ListItem\",\n      position: index + 1,\n      name: item.name,\n      item: `${siteConfig.url}${item.url}`,\n    })),\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 组织结构化数据\n */\nexport function OrganizationStructuredData() {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    name: siteConfig.name,\n    description: siteConfig.description,\n    url: siteConfig.url,\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: `${siteConfig.url}/logo.png`,\n      width: 512,\n      height: 512,\n    },\n    contactPoint: {\n      \"@type\": \"ContactPoint\",\n      email: siteConfig.author.email,\n      contactType: \"customer service\",\n    },\n    sameAs: [\n      siteConfig.links.github,\n      siteConfig.links.twitter,\n    ].filter(Boolean),\n    founder: {\n      \"@type\": \"Person\",\n      name: siteConfig.author.name,\n    },\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 作者结构化数据\n */\nexport function PersonStructuredData({ author }: { author: User }) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Person\",\n    name: author.name,\n    email: author.email,\n    image: author.avatar,\n    url: `${siteConfig.url}/authors/${author.id}`,\n    jobTitle: author.role === 'admin' ? '管理员' : author.role === 'collaborator' ? '协作者' : '用户',\n    worksFor: {\n      \"@type\": \"Organization\",\n      name: siteConfig.name,\n    },\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * FAQ 结构化数据\n */\nexport function FAQStructuredData({ faqs }: { \n  faqs: Array<{ question: string; answer: string }> \n}) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"FAQPage\",\n    mainEntity: faqs.map(faq => ({\n      \"@type\": \"Question\",\n      name: faq.question,\n      acceptedAnswer: {\n        \"@type\": \"Answer\",\n        text: faq.answer,\n      },\n    })),\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 搜索结果结构化数据\n */\nexport function SearchResultsStructuredData({ \n  query, \n  results \n}: { \n  query: string\n  results: Article[] \n}) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"SearchResultsPage\",\n    mainEntity: {\n      \"@type\": \"ItemList\",\n      numberOfItems: results.length,\n      itemListElement: results.map((article, index) => ({\n        \"@type\": \"ListItem\",\n        position: index + 1,\n        item: {\n          \"@type\": \"Article\",\n          headline: article.title,\n          description: article.summary || article.excerpt,\n          url: `${siteConfig.url}/articles/${article.slug}`,\n          datePublished: article.publishedAt?.toISOString() || article.createdAt.toISOString(),\n          author: {\n            \"@type\": \"Person\",\n            name: article.author.name,\n          },\n        },\n      })),\n    },\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAWO,SAAS;IACd,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;QACrB,aAAa,qHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,KAAK,qHAAA,CAAA,aAAU,CAAC,GAAG;QACnB,iBAAiB;YACf,SAAS;YACT,QAAQ;gBACN,SAAS;gBACT,aAAa,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC;YAChE;YACA,eAAe;QACjB;QACA,WAAW;YACT,SAAS;YACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;YACrB,MAAM;gBACJ,SAAS;gBACT,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,CAAC;gBACjC,OAAO;gBACP,QAAQ;YACV;QACF;QACA,QAAQ;YACN,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;YACvB,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO;SACzB,CAAC,MAAM,CAAC;IACX;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,sBAAsB,EAAE,OAAO,EAAwB;IACrE,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,UAAU,QAAQ,KAAK;QACvB,aAAa,QAAQ,OAAO,IAAI,QAAQ,OAAO;QAC/C,OAAO,QAAQ,UAAU,GAAG;YAAC,QAAQ,UAAU;SAAC,GAAG,EAAE;QACrD,eAAe,QAAQ,WAAW,EAAE,iBAAiB,QAAQ,SAAS,CAAC,WAAW;QAClF,cAAc,QAAQ,SAAS,CAAC,WAAW;QAC3C,QAAQ;YACN,SAAS;YACT,MAAM,QAAQ,MAAM,CAAC,IAAI;YACzB,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,EAAE,EAAE;QACvD;QACA,WAAW;YACT,SAAS;YACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;YACrB,MAAM;gBACJ,SAAS;gBACT,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,CAAC;gBACjC,OAAO;gBACP,QAAQ;YACV;QACF;QACA,kBAAkB;YAChB,SAAS;YACT,OAAO,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;QACrD;QACA,UAAU,QAAQ,IAAI,CAAC,IAAI,CAAC;QAC5B,gBAAgB,QAAQ,QAAQ;QAChC,WAAW,QAAQ,OAAO,CAAC,MAAM;QACjC,cAAc;QACd,sBAAsB;YACpB;gBACE,SAAS;gBACT,iBAAiB;gBACjB,sBAAsB,QAAQ,SAAS;YACzC;YACA;gBACE,SAAS;gBACT,iBAAiB;gBACjB,sBAAsB,QAAQ,SAAS;YACzC;SACD;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,yBAAyB,EAAE,KAAK,EAE/C;IACC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC3C,SAAS;gBACT,UAAU,QAAQ;gBAClB,MAAM,KAAK,IAAI;gBACf,MAAM,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,GAAG,KAAK,GAAG,EAAE;YACtC,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS;IACd,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;QACrB,aAAa,qHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,KAAK,qHAAA,CAAA,aAAU,CAAC,GAAG;QACnB,MAAM;YACJ,SAAS;YACT,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,CAAC;YACjC,OAAO;YACP,QAAQ;QACV;QACA,cAAc;YACZ,SAAS;YACT,OAAO,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK;YAC9B,aAAa;QACf;QACA,QAAQ;YACN,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;YACvB,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO;SACzB,CAAC,MAAM,CAAC;QACT,SAAS;YACP,SAAS;YACT,MAAM,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,IAAI;QAC9B;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,qBAAqB,EAAE,MAAM,EAAoB;IAC/D,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,KAAK;QACnB,OAAO,OAAO,MAAM;QACpB,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;QAC7C,UAAU,OAAO,IAAI,KAAK,UAAU,QAAQ,OAAO,IAAI,KAAK,iBAAiB,QAAQ;QACrF,UAAU;YACR,SAAS;YACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;QACvB;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,kBAAkB,EAAE,IAAI,EAEvC;IACC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,YAAY,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,SAAS;gBACT,MAAM,IAAI,QAAQ;gBAClB,gBAAgB;oBACd,SAAS;oBACT,MAAM,IAAI,MAAM;gBAClB;YACF,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,4BAA4B,EAC1C,KAAK,EACL,OAAO,EAIR;IACC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,YAAY;YACV,SAAS;YACT,eAAe,QAAQ,MAAM;YAC7B,iBAAiB,QAAQ,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;oBAChD,SAAS;oBACT,UAAU,QAAQ;oBAClB,MAAM;wBACJ,SAAS;wBACT,UAAU,QAAQ,KAAK;wBACvB,aAAa,QAAQ,OAAO,IAAI,QAAQ,OAAO;wBAC/C,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;wBACjD,eAAe,QAAQ,WAAW,EAAE,iBAAiB,QAAQ,SAAS,CAAC,WAAW;wBAClF,QAAQ;4BACN,SAAS;4BACT,MAAM,QAAQ,MAAM,CAAC,IAAI;wBAC3B;oBACF;gBACF,CAAC;QACH;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/app/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ap } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { MainLayout } from \"@/components/layout/main-layout\"\nimport { PageContainer } from \"@/components/layout/page-container\"\nimport { ResponsiveGrid } from \"@/components/ui/responsive-grid\"\nimport { FadeIn, SlideIn } from \"@/components/ui/page-transition\"\nimport { WebsiteStructuredData, OrganizationStructuredData } from \"@/components/seo/structured-data\"\nimport { siteConfig } from \"@/config/site\"\nimport { generateBaseMetadata } from \"@/lib/seo\"\nimport type { Metadata } from \"next\"\n\n// 生成页面元数据\nexport function generateMetadata(): Metadata {\n  return generateBaseMetadata({\n    title: \"首页\",\n    description: siteConfig.description,\n    keywords: [\"博客\", \"Next.js\", \"React\", \"TypeScript\", \"Tailwind CSS\", \"AI\", \"现代化\"],\n  })\n}\n\nexport default function Home() {\n  return (\n    <>\n      {/* 结构化数据 */}\n      <WebsiteStructuredData />\n      <OrganizationStructuredData />\n\n      <MainLayout>\n      <PageContainer maxWidth=\"full\">\n        {/* Hero Section */}\n        <section className=\"text-center py-12 md:py-20\">\n          <div className=\"max-w-4xl mx-auto\">\n            <FadeIn delay={100}>\n              <Badge variant=\"secondary\" className=\"mb-4\">\n                <Sparkles className=\"w-4 h-4 mr-1\" />\n                现代化博客系统\n              </Badge>\n            </FadeIn>\n            <FadeIn delay={200}>\n              <h1 className=\"text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-6\">\n                {siteConfig.name}\n              </h1>\n            </FadeIn>\n            <FadeIn delay={300}>\n              <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\n                {siteConfig.description}\n              </p>\n            </FadeIn>\n            <FadeIn delay={400}>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button size=\"lg\" className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\">\n                  <BookOpen className=\"w-4 h-4 mr-2\" />\n                  开始阅读\n                </Button>\n                <Button size=\"lg\" variant=\"outline\">\n                  <Users className=\"w-4 h-4 mr-2\" />\n                  了解更多\n                </Button>\n              </div>\n            </FadeIn>\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section className=\"py-12\">\n          <SlideIn delay={500}>\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold mb-4\">强大功能</h2>\n              <p className=\"text-muted-foreground max-w-2xl mx-auto\">\n                集成最新技术，为您提供最佳的博客体验\n              </p>\n            </div>\n          </SlideIn>\n\n          <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>\n            <Card className=\"group hover:shadow-lg transition-all duration-300\">\n              <CardHeader>\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4\">\n                  <Sparkles className=\"w-6 h-6 text-white\" />\n                </div>\n                <CardTitle>AI 智能摘要</CardTitle>\n                <CardDescription>\n                  自动生成文章摘要，提升阅读体验\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"group hover:shadow-lg transition-all duration-300\">\n              <CardHeader>\n                <div className=\"w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center mb-4\">\n                  <Zap className=\"w-6 h-6 text-white\" />\n                </div>\n                <CardTitle>深色模式</CardTitle>\n                <CardDescription>\n                  支持浅色/深色主题切换，保护您的眼睛\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"group hover:shadow-lg transition-all duration-300\">\n              <CardHeader>\n                <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4\">\n                  <BookOpen className=\"w-6 h-6 text-white\" />\n                </div>\n                <CardTitle>响应式设计</CardTitle>\n                <CardDescription>\n                  完美适配手机、平板和桌面设备\n                </CardDescription>\n              </CardHeader>\n            </Card>\n          </ResponsiveGrid>\n        </section>\n\n        {/* Latest Articles Section */}\n        <section className=\"py-12\">\n          <div className=\"flex items-center justify-between mb-8\">\n            <h2 className=\"text-3xl font-bold\">最新文章</h2>\n            <Button variant=\"outline\">查看全部</Button>\n          </div>\n\n          <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>\n            {/* 这里将来会显示真实的文章数据 */}\n            {[1, 2, 3].map((i) => (\n              <Card key={i} className=\"group hover:shadow-lg transition-all duration-300\">\n                <CardHeader>\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <Badge variant=\"outline\">技术</Badge>\n                    <Badge variant=\"secondary\">Next.js</Badge>\n                  </div>\n                  <CardTitle className=\"line-clamp-2\">\n                    示例文章标题 {i} - 现代化博客系统的构建之路\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-muted-foreground text-sm line-clamp-3\">\n                    这是一篇关于如何构建现代化博客系统的文章，涵盖了 Next.js、TypeScript、Tailwind CSS 等技术栈的使用...\n                  </p>\n                </CardContent>\n              </Card>\n            ))}\n          </ResponsiveGrid>\n        </section>\n      </PageContainer>\n    </MainLayout>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAIO,SAAS;IACd,OAAO,CAAA,GAAA,iHAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1B,OAAO;QACP,aAAa,qHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,UAAU;YAAC;YAAM;YAAW;YAAS;YAAc;YAAgB;YAAM;SAAM;IACjF;AACF;AAEe,SAAS;IACtB,qBACE;;0BAEE,8OAAC,+IAAA,CAAA,wBAAqB;;;;;0BACtB,8OAAC,+IAAA,CAAA,6BAA0B;;;;;0BAE3B,8OAAC,8IAAA,CAAA,aAAU;0BACX,cAAA,8OAAC,iJAAA,CAAA,gBAAa;oBAAC,UAAS;;sCAEtB,8OAAC;4BAAQ,WAAU;sCACjB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8IAAA,CAAA,SAAM;wCAAC,OAAO;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;8DACnC,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIzC,8OAAC,8IAAA,CAAA,SAAM;wCAAC,OAAO;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDACX,qHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;;;;;;kDAGpB,8OAAC,8IAAA,CAAA,SAAM;wCAAC,OAAO;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV,qHAAA,CAAA,aAAU,CAAC,WAAW;;;;;;;;;;;kDAG3B,8OAAC,8IAAA,CAAA,SAAM;wCAAC,OAAO;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;;sEAC1B,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;;sEACxB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS5C,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC,8IAAA,CAAA,UAAO;oCAAC,OAAO;8CACd,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAA0C;;;;;;;;;;;;;;;;;8CAM3D,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,MAAM;wCAAE,SAAS;wCAAG,IAAI;wCAAG,IAAI;oCAAE;oCAAG,KAAK;;sDACvD,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;;;;;;sDAMrB,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;;;;;;sDAMrB,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASzB,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;8CAG5B,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,MAAM;wCAAE,SAAS;wCAAG,IAAI;wCAAG,IAAI;oCAAE;oCAAG,KAAK;8CAEtD;wCAAC;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,gIAAA,CAAA,OAAI;4CAAS,WAAU;;8DACtB,8OAAC,gIAAA,CAAA,aAAU;;sEACT,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAU;;;;;;8EACzB,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAY;;;;;;;;;;;;sEAE7B,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;gEAAe;gEAC1B;gEAAE;;;;;;;;;;;;;8DAGd,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAE,WAAU;kEAA6C;;;;;;;;;;;;2CAXnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBzB", "debugId": null}}]}
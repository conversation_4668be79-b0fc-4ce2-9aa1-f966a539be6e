module.exports = {

"[project]/node_modules/prismjs/components/prism-javascript.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-javascript_e07f1135.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-javascript.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-typescript.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-typescript_9a631141.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-typescript.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-jsx.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-jsx_f3c14256.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-jsx.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-tsx.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-tsx_d51d201c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-tsx.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-css.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-css_750ff7ba.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-css.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-scss.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-scss_88af2b77.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-scss.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-json.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-json_a007a07c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-json.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-bash.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-bash_c38fb1c3.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-bash.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-python.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-python_2ce9bb59.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-python.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-sql.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_prism-sql_2ac439ab.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-sql.js [app-ssr] (ecmascript)");
    });
});
}}),

};
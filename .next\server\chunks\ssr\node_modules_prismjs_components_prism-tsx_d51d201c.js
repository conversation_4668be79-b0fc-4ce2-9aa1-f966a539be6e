module.exports = {

"[project]/node_modules/prismjs/components/prism-tsx.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
(function(Prism1) {
    var typescript = Prism1.util.clone(Prism1.languages.typescript);
    Prism1.languages.tsx = Prism1.languages.extend('jsx', typescript);
    // doesn't work with TS because TS is too complex
    delete Prism1.languages.tsx['parameter'];
    delete Prism1.languages.tsx['literal-property'];
    // This will prevent collisions between TSX tags and TS generic types.
    // Idea by https://github.com/karlhorky
    // Discussion: https://github.com/PrismJS/prism/issues/2594#issuecomment-710666928
    var tag = Prism1.languages.tsx.tag;
    tag.pattern = RegExp(/(^|[^\w$]|(?=<\/))/.source + '(?:' + tag.pattern.source + ')', tag.pattern.flags);
    tag.lookbehind = true;
})(Prism);
}}),

};

//# sourceMappingURL=node_modules_prismjs_components_prism-tsx_d51d201c.js.map
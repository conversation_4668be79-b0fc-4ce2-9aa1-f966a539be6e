import { Article, User, Category, Tag } from "@/types"

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: "1",
    name: "张三",
    email: "<PERSON><PERSON><PERSON>@example.com",
    avatar: "https://avatars.githubusercontent.com/u/1?v=4",
    role: "admin",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-01"),
  },
  {
    id: "2", 
    name: "李四",
    email: "<EMAIL>",
    avatar: "https://avatars.githubusercontent.com/u/2?v=4",
    role: "collaborator",
    createdAt: new Date("2024-01-02"),
    updatedAt: new Date("2024-01-02"),
  },
]

// 模拟分类数据
export const mockCategories: Category[] = [
  {
    id: "1",
    name: "技术",
    slug: "tech",
    description: "技术相关文章",
    color: "#3b82f6",
    articleCount: 8,
  },
  {
    id: "2",
    name: "生活",
    slug: "life", 
    description: "生活感悟和经验分享",
    color: "#10b981",
    articleCount: 3,
  },
  {
    id: "3",
    name: "教程",
    slug: "tutorial",
    description: "各种教程和指南",
    color: "#f59e0b",
    articleCount: 4,
  },
]

// 模拟标签数据
export const mockTags: Tag[] = [
  { id: "1", name: "Next.js", slug: "nextjs", color: "#000000", articleCount: 5 },
  { id: "2", name: "React", slug: "react", color: "#61dafb", articleCount: 6 },
  { id: "3", name: "TypeScript", slug: "typescript", color: "#3178c6", articleCount: 4 },
  { id: "4", name: "Tailwind CSS", slug: "tailwindcss", color: "#06b6d4", articleCount: 3 },
  { id: "5", name: "JavaScript", slug: "javascript", color: "#f7df1e", articleCount: 7 },
  { id: "6", name: "CSS", slug: "css", color: "#1572b6", articleCount: 2 },
  { id: "7", name: "HTML", slug: "html", color: "#e34f26", articleCount: 2 },
  { id: "8", name: "Node.js", slug: "nodejs", color: "#339933", articleCount: 3 },
]

// 模拟文章数据
export const mockArticles: Article[] = [
  {
    id: "1",
    title: "Next.js 14 完整指南：从入门到精通",
    slug: "nextjs-14-complete-guide",
    content: `# Next.js 14 完整指南

Next.js 14 是一个强大的 React 框架，提供了许多开箱即用的功能...

## 主要特性

- App Router
- Server Components  
- Streaming
- 内置优化

## 安装和设置

\`\`\`bash
npx create-next-app@latest my-app
cd my-app
npm run dev
\`\`\`

这是一个非常详细的教程，涵盖了 Next.js 14 的所有重要概念。`,
    excerpt: "深入了解 Next.js 14 的新特性和最佳实践，从基础概念到高级应用。",
    summary: "本文全面介绍了 Next.js 14 框架的核心特性，包括 App Router、Server Components 等新功能，适合想要学习现代 React 开发的开发者。",
    coverImage: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop",
    tags: ["Next.js", "React", "TypeScript"],
    category: "技术",
    status: "published",
    authorId: "1",
    author: mockUsers[0],
    publishedAt: new Date("2024-01-15"),
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-15"),
    viewCount: 1234,
    likeCount: 89,
  },
  {
    id: "2",
    title: "现代化 CSS 布局技巧",
    slug: "modern-css-layout-techniques",
    content: `# 现代化 CSS 布局技巧

CSS 布局已经发生了巨大的变化...

## Grid 布局

\`\`\`css
.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}
\`\`\`

## Flexbox 布局

\`\`\`css
.flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
\`\`\``,
    excerpt: "探索现代 CSS 布局技术，包括 Grid、Flexbox 和容器查询。",
    summary: "介绍了现代 CSS 布局的最佳实践，重点讲解 Grid 和 Flexbox 的使用场景和技巧。",
    coverImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop",
    tags: ["CSS", "HTML"],
    category: "技术",
    status: "published",
    authorId: "1",
    author: mockUsers[0],
    publishedAt: new Date("2024-01-12"),
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-12"),
    viewCount: 856,
    likeCount: 67,
  },
  {
    id: "3",
    title: "TypeScript 高级类型系统详解",
    slug: "typescript-advanced-types",
    content: `# TypeScript 高级类型系统详解

TypeScript 的类型系统非常强大...

## 泛型

\`\`\`typescript
function identity<T>(arg: T): T {
  return arg;
}
\`\`\`

## 条件类型

\`\`\`typescript
type NonNullable<T> = T extends null | undefined ? never : T;
\`\`\``,
    excerpt: "深入理解 TypeScript 的高级类型特性，提升代码质量和开发效率。",
    summary: "详细讲解了 TypeScript 的高级类型特性，包括泛型、条件类型、映射类型等概念。",
    coverImage: "https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop",
    tags: ["TypeScript", "JavaScript"],
    category: "技术",
    status: "published",
    authorId: "2",
    author: mockUsers[1],
    publishedAt: new Date("2024-01-10"),
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-10"),
    viewCount: 723,
    likeCount: 54,
  },
  {
    id: "4",
    title: "我的编程学习之路",
    slug: "my-programming-journey",
    content: `# 我的编程学习之路

回顾我从零开始学习编程的经历...

## 起步阶段

最开始接触的是 HTML 和 CSS...

## 进阶阶段

后来开始学习 JavaScript...

## 现在

现在主要专注于 React 和 Node.js 开发...`,
    excerpt: "分享我从编程小白到全栈开发者的成长历程和心得体会。",
    summary: "作者分享了自己学习编程的完整历程，从基础的 HTML/CSS 到现在的全栈开发，给初学者很多启发。",
    coverImage: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=400&fit=crop",
    tags: ["JavaScript", "React", "Node.js"],
    category: "生活",
    status: "published",
    authorId: "1",
    author: mockUsers[0],
    publishedAt: new Date("2024-01-08"),
    createdAt: new Date("2024-01-03"),
    updatedAt: new Date("2024-01-08"),
    viewCount: 445,
    likeCount: 32,
  },
  {
    id: "5",
    title: "Tailwind CSS 实用技巧集合",
    slug: "tailwind-css-tips-and-tricks",
    content: `# Tailwind CSS 实用技巧集合

收集了一些 Tailwind CSS 的实用技巧...

## 自定义配置

\`\`\`javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#3b82f6',
      }
    }
  }
}
\`\`\`

## 响应式设计

\`\`\`html
<div class="w-full md:w-1/2 lg:w-1/3">
  响应式容器
</div>
\`\`\``,
    excerpt: "整理了 Tailwind CSS 开发中的实用技巧和最佳实践。",
    summary: "汇总了 Tailwind CSS 在实际项目中的使用技巧，包括自定义配置、响应式设计等内容。",
    coverImage: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=400&fit=crop",
    tags: ["Tailwind CSS", "CSS"],
    category: "教程",
    status: "published",
    authorId: "2",
    author: mockUsers[1],
    publishedAt: new Date("2024-01-05"),
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-01-05"),
    viewCount: 612,
    likeCount: 43,
  },
]

// 获取文章列表的函数
export function getArticles(options?: {
  page?: number
  limit?: number
  category?: string
  tag?: string
  search?: string
  status?: string
}) {
  const {
    page = 1,
    limit = 10,
    category,
    tag,
    search,
    status = "published"
  } = options || {}

  let filteredArticles = mockArticles.filter(article => 
    article.status === status
  )

  // 分类筛选
  if (category) {
    filteredArticles = filteredArticles.filter(article => 
      article.category === category
    )
  }

  // 标签筛选
  if (tag) {
    filteredArticles = filteredArticles.filter(article => 
      article.tags.includes(tag)
    )
  }

  // 搜索筛选
  if (search) {
    const searchLower = search.toLowerCase()
    filteredArticles = filteredArticles.filter(article => 
      article.title.toLowerCase().includes(searchLower) ||
      article.content.toLowerCase().includes(searchLower) ||
      article.excerpt?.toLowerCase().includes(searchLower)
    )
  }

  // 排序（按发布时间倒序）
  filteredArticles.sort((a, b) => {
    const dateA = a.publishedAt || a.createdAt
    const dateB = b.publishedAt || b.createdAt
    return dateB.getTime() - dateA.getTime()
  })

  // 分页
  const total = filteredArticles.length
  const totalPages = Math.ceil(total / limit)
  const offset = (page - 1) * limit
  const articles = filteredArticles.slice(offset, offset + limit)

  return {
    articles,
    pagination: {
      page,
      limit,
      total,
      totalPages,
    }
  }
}

// 根据 slug 获取文章
export function getArticleBySlug(slug: string) {
  return mockArticles.find(article => article.slug === slug)
}

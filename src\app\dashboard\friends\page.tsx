"use client"

import { <PERSON>, Link2 } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { PageHeader } from "@/components/dashboard/page-header"
import { FriendLinkManager } from "@/components/friends/friend-link-manager"
import { getFriendLinks } from "@/lib/mock-data"

export default function FriendLinksDashboardPage() {
  const friendLinks = getFriendLinks({ status: "all" })
  const activeLinks = getFriendLinks({ status: "active" })
  const inactiveLinks = getFriendLinks({ status: "inactive" })
  const pendingLinks = getFriendLinks({ status: "pending" })

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <PageHeader
          title="友情链接管理"
          description="管理网站的友情链接，包括添加、编辑和删除"
          icon={<Heart className="h-6 w-6" />}
        />

        {/* 统计卡片 */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                总链接数
              </CardTitle>
              <Link2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{friendLinks.length}</div>
              <p className="text-xs text-muted-foreground">
                所有友情链接总数
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                活跃链接
              </CardTitle>
              <div className="h-4 w-4 rounded-full bg-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeLinks.length}</div>
              <p className="text-xs text-muted-foreground">
                当前显示在前台的链接
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                停用链接
              </CardTitle>
              <div className="h-4 w-4 rounded-full bg-gray-300 dark:bg-gray-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inactiveLinks.length}</div>
              <p className="text-xs text-muted-foreground">
                已停用的友情链接
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                待审核
              </CardTitle>
              <div className="h-4 w-4 rounded-full bg-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pendingLinks.length}</div>
              <p className="text-xs text-muted-foreground">
                等待审核的申请
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 友链管理 */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="active">活跃</TabsTrigger>
            <TabsTrigger value="inactive">停用</TabsTrigger>
            <TabsTrigger value="pending">待审核</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all">
            <FriendLinkManager />
          </TabsContent>
          
          <TabsContent value="active">
            <Card>
              <CardHeader>
                <CardTitle>活跃友链</CardTitle>
                <CardDescription>
                  当前显示在前台的友情链接
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  这些链接当前对所有访问者可见。您可以在"全部"标签页中管理这些链接。
                </p>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="inactive">
            <Card>
              <CardHeader>
                <CardTitle>停用友链</CardTitle>
                <CardDescription>
                  已停用的友情链接
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  这些链接当前不会显示在前台。您可以在"全部"标签页中重新激活这些链接。
                </p>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="pending">
            <Card>
              <CardHeader>
                <CardTitle>待审核友链</CardTitle>
                <CardDescription>
                  等待审核的友链申请
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  这些链接正在等待您的审核。您可以在"全部"标签页中审核这些申请。
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 帮助信息 */}
        <Card>
          <CardHeader>
            <CardTitle>友情链接管理指南</CardTitle>
            <CardDescription>
              如何有效管理友情链接
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h3 className="font-medium">友链管理最佳实践</h3>
              <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                <li>定期检查友链的有效性，移除已失效的链接</li>
                <li>优先展示高质量、更新频繁的网站</li>
                <li>分类组织友链，提高用户体验</li>
                <li>设置明确的友链申请条件，保证质量</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium">友链交换的好处</h3>
              <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
                <li>增加网站流量和曝光度</li>
                <li>提高搜索引擎排名</li>
                <li>建立行业内的人脉和资源</li>
                <li>为用户提供更多有价值的资源</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

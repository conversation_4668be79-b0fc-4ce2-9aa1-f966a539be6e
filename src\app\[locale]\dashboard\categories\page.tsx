import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { CategoryTagManager } from '@/components/admin/category-tag-manager'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('admin.categoryTag')
  
  return {
    title: t('title'),
    description: t('subtitle'),
  }
}

export default function CategoriesPage() {
  return (
    <div className="container mx-auto py-6">
      <CategoryTagManager />
    </div>
  )
}

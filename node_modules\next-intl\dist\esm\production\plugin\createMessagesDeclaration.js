import e from"fs";import t from"path";import{throwError as s}from"./utils.js";import o from"./watchFile.js";function n(o){var n;n=()=>{for(const n of o){const o=t.resolve(n);e.existsSync(o)||s(`\`createMessagesDeclaration\` points to a non-existent file: ${o}`),o.endsWith(".json")||s(`\`createMessagesDeclaration\` needs to point to a JSON file. Received: ${o}`);const c=process.env["NODE_ENV".trim()];r(n),"development"===c&&i(n)}},"1"!==process.env._NEXT_INTL_COMPILE_MESSAGES&&(process.env._NEXT_INTL_COMPILE_MESSAGES="1",n())}function i(e){const t=o(e,(()=>{r(e,!0)}));process.on("exit",(()=>{t.close()}))}function r(t,s=!1){const o=t.replace(/\.json$/,".d.json.ts");function n(e){return`// This file is auto-generated by next-intl, do not edit directly.\n// See: https://next-intl.dev/docs/workflows/typescript#messages-arguments\n\ndeclare const messages: ${e.trim()};\nexport default messages;`}if(s)return e.promises.readFile(t,"utf-8").then((t=>e.promises.writeFile(o,n(t))));const i=e.readFileSync(t,"utf-8");e.writeFileSync(o,n(i))}export{n as default};

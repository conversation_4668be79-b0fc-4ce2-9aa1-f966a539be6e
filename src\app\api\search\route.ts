import { NextRequest, NextResponse } from "next/server"
import { getArticles } from "@/lib/mock-data"

/**
 * 搜索 API
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get("q") || ""
  const page = parseInt(searchParams.get("page") || "1")
  const limit = parseInt(searchParams.get("limit") || "10")
  const category = searchParams.get("category") || ""
  const tag = searchParams.get("tag") || ""
  
  try {
    // 如果没有搜索查询，返回空结果
    if (!query.trim()) {
      return NextResponse.json({
        success: true,
        data: {
          articles: [],
          pagination: {
            page: 1,
            limit,
            total: 0,
            totalPages: 0,
          },
          query: "",
          suggestions: [],
        },
      })
    }
    
    // 执行搜索
    const searchResult = getArticles({
      search: query,
      category,
      tag,
      page,
      limit,
      status: "published",
    })
    
    // 生成搜索建议
    const suggestions = generateSearchSuggestions(query)
    
    return NextResponse.json({
      success: true,
      data: {
        ...searchResult,
        query,
        suggestions,
      },
    })
  } catch (error) {
    console.error("Search error:", error)
    return NextResponse.json(
      {
        success: false,
        error: "搜索失败",
      },
      { status: 500 }
    )
  }
}

/**
 * 生成搜索建议
 */
function generateSearchSuggestions(query: string): string[] {
  const suggestions: string[] = []
  
  // 获取所有文章用于生成建议
  const { articles } = getArticles({ status: "published" })
  
  // 收集所有标题词汇
  const titleWords = new Set<string>()
  articles.forEach(article => {
    const words = article.title.toLowerCase().split(/\s+/)
    words.forEach(word => {
      if (word.length > 2) {
        titleWords.add(word)
      }
    })
  })
  
  // 收集所有标签
  const allTags = new Set<string>()
  articles.forEach(article => {
    article.tags.forEach(tag => allTags.add(tag.toLowerCase()))
  })
  
  // 收集所有分类
  const allCategories = new Set<string>()
  articles.forEach(article => {
    allCategories.add(article.category.toLowerCase())
  })
  
  const queryLower = query.toLowerCase()
  
  // 基于标题词汇生成建议
  Array.from(titleWords).forEach(word => {
    if (word.includes(queryLower) && word !== queryLower) {
      suggestions.push(word)
    }
  })
  
  // 基于标签生成建议
  Array.from(allTags).forEach(tag => {
    if (tag.includes(queryLower) && tag !== queryLower) {
      suggestions.push(tag)
    }
  })
  
  // 基于分类生成建议
  Array.from(allCategories).forEach(category => {
    if (category.includes(queryLower) && category !== queryLower) {
      suggestions.push(category)
    }
  })
  
  // 返回前5个建议
  return Array.from(new Set(suggestions)).slice(0, 5)
}

/**
 * 高级搜索 API
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      query = "",
      categories = [],
      tags = [],
      dateRange = null,
      author = "",
      sortBy = "relevance", // relevance, date, views
      page = 1,
      limit = 10,
    } = body
    
    // 获取基础搜索结果
    let { articles, pagination } = getArticles({
      search: query,
      page,
      limit,
      status: "published",
    })
    
    // 应用高级筛选
    if (categories.length > 0) {
      articles = articles.filter(article => 
        categories.includes(article.category)
      )
    }
    
    if (tags.length > 0) {
      articles = articles.filter(article => 
        article.tags.some(tag => tags.includes(tag))
      )
    }
    
    if (author) {
      articles = articles.filter(article => 
        article.author.name.toLowerCase().includes(author.toLowerCase())
      )
    }
    
    if (dateRange) {
      const { start, end } = dateRange
      articles = articles.filter(article => {
        const publishDate = article.publishedAt || article.createdAt
        return publishDate >= new Date(start) && publishDate <= new Date(end)
      })
    }
    
    // 应用排序
    switch (sortBy) {
      case "date":
        articles.sort((a, b) => {
          const dateA = a.publishedAt || a.createdAt
          const dateB = b.publishedAt || b.createdAt
          return dateB.getTime() - dateA.getTime()
        })
        break
      case "views":
        articles.sort((a, b) => b.viewCount - a.viewCount)
        break
      case "relevance":
      default:
        // 相关性排序（简单实现）
        if (query) {
          articles.sort((a, b) => {
            const scoreA = calculateRelevanceScore(a, query)
            const scoreB = calculateRelevanceScore(b, query)
            return scoreB - scoreA
          })
        }
        break
    }
    
    // 重新计算分页
    const total = articles.length
    const totalPages = Math.ceil(total / limit)
    const offset = (page - 1) * limit
    const paginatedArticles = articles.slice(offset, offset + limit)
    
    return NextResponse.json({
      success: true,
      data: {
        articles: paginatedArticles,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
        query,
        filters: {
          categories,
          tags,
          dateRange,
          author,
          sortBy,
        },
      },
    })
  } catch (error) {
    console.error("Advanced search error:", error)
    return NextResponse.json(
      {
        success: false,
        error: "高级搜索失败",
      },
      { status: 500 }
    )
  }
}

/**
 * 计算相关性分数
 */
function calculateRelevanceScore(article: any, query: string): number {
  const queryLower = query.toLowerCase()
  let score = 0
  
  // 标题匹配权重最高
  if (article.title.toLowerCase().includes(queryLower)) {
    score += 10
  }
  
  // 摘要匹配
  if (article.excerpt?.toLowerCase().includes(queryLower)) {
    score += 5
  }
  
  // 内容匹配
  if (article.content.toLowerCase().includes(queryLower)) {
    score += 3
  }
  
  // 标签匹配
  if (article.tags.some((tag: string) => tag.toLowerCase().includes(queryLower))) {
    score += 7
  }
  
  // 分类匹配
  if (article.category.toLowerCase().includes(queryLower)) {
    score += 6
  }
  
  return score
}

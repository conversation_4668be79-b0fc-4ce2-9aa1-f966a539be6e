(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/providers/theme-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
"use client";
;
;
function ThemeProvider({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/theme-provider.tsx",
        lineNumber: 8,
        columnNumber: 10
    }, this);
}
_c = ThemeProvider;
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/session-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-client] (ecmascript)");
"use client";
;
;
function AuthProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SessionProvider"], {
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/session-provider.tsx",
        lineNumber: 11,
        columnNumber: 10
    }, this);
}
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/performance/performance-monitor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LazyComponents": (()=>LazyComponents),
    "OptimizedImage": (()=>OptimizedImage),
    "PerformanceMonitor": (()=>PerformanceMonitor),
    "useResourcePreload": (()=>useResourcePreload)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// 需要在文件顶部导入 dynamic
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
// 需要在文件顶部导入 Image
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
function PerformanceMonitor() {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PerformanceMonitor.useEffect": ()=>{
            // 监控 Core Web Vitals
            if ("object" !== "undefined" && "performance" in window) {
                // 监控 LCP (Largest Contentful Paint)
                const observer = new PerformanceObserver({
                    "PerformanceMonitor.useEffect": (list)=>{
                        for (const entry of list.getEntries()){
                            if (entry.entryType === "largest-contentful-paint") {
                                console.log("LCP:", entry.startTime);
                            // 在实际应用中，这里应该上报到分析服务
                            }
                        }
                    }
                }["PerformanceMonitor.useEffect"]);
                try {
                    observer.observe({
                        entryTypes: [
                            "largest-contentful-paint"
                        ]
                    });
                } catch (e) {
                // 浏览器不支持
                }
                // 监控 FID (First Input Delay)
                const fidObserver = new PerformanceObserver({
                    "PerformanceMonitor.useEffect": (list)=>{
                        for (const entry of list.getEntries()){
                            if (entry.entryType === "first-input") {
                                const fid = entry.processingStart - entry.startTime;
                                console.log("FID:", fid);
                            // 在实际应用中，这里应该上报到分析服务
                            }
                        }
                    }
                }["PerformanceMonitor.useEffect"]);
                try {
                    fidObserver.observe({
                        entryTypes: [
                            "first-input"
                        ]
                    });
                } catch (e) {
                // 浏览器不支持
                }
                // 监控 CLS (Cumulative Layout Shift)
                let clsValue = 0;
                const clsObserver = new PerformanceObserver({
                    "PerformanceMonitor.useEffect": (list)=>{
                        for (const entry of list.getEntries()){
                            if (!entry.hadRecentInput) {
                                clsValue += entry.value;
                            }
                        }
                        console.log("CLS:", clsValue);
                    }
                }["PerformanceMonitor.useEffect"]);
                try {
                    clsObserver.observe({
                        entryTypes: [
                            "layout-shift"
                        ]
                    });
                } catch (e) {
                // 浏览器不支持
                }
                // 监控页面加载时间
                window.addEventListener("load", {
                    "PerformanceMonitor.useEffect": ()=>{
                        const navigation = performance.getEntriesByType("navigation")[0];
                        if (navigation) {
                            const loadTime = navigation.loadEventEnd - navigation.fetchStart;
                            const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
                            const firstByte = navigation.responseStart - navigation.fetchStart;
                            console.log("Page Load Time:", loadTime);
                            console.log("DOM Content Loaded:", domContentLoaded);
                            console.log("Time to First Byte:", firstByte);
                        }
                    }
                }["PerformanceMonitor.useEffect"]);
                // 清理函数
                return ({
                    "PerformanceMonitor.useEffect": ()=>{
                        observer.disconnect();
                        fidObserver.disconnect();
                        clsObserver.disconnect();
                    }
                })["PerformanceMonitor.useEffect"];
            }
        }
    }["PerformanceMonitor.useEffect"], []);
    return null // 这是一个监控组件，不渲染任何内容
    ;
}
_s(PerformanceMonitor, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = PerformanceMonitor;
function useResourcePreload() {
    _s1();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useResourcePreload.useEffect": ()=>{
            // 预加载关键资源
            const preloadResources = [
                {
                    href: "/fonts/inter.woff2",
                    as: "font",
                    type: "font/woff2"
                }
            ];
            preloadResources.forEach({
                "useResourcePreload.useEffect": ({ href, as, type })=>{
                    const link = document.createElement("link");
                    link.rel = "preload";
                    link.href = href;
                    link.as = as;
                    if (type) link.type = type;
                    link.crossOrigin = "anonymous";
                    document.head.appendChild(link);
                }
            }["useResourcePreload.useEffect"]);
        }
    }["useResourcePreload.useEffect"], []);
}
_s1(useResourcePreload, "OD7bBpZva5O2jO+Puf00hKivP7c=");
const LazyComponents = {
    // 懒加载文章编辑器
    ArticleEditor: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/src/components/article/article-editor.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((mod)=>({
                default: mod.ArticleEditor
            })), {
        loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse h-96 bg-muted rounded-lg"
            }, void 0, false, {
                fileName: "[project]/src/components/performance/performance-monitor.tsx",
                lineNumber: 118,
                columnNumber: 20
            }, this),
        ssr: false
    }),
    // 懒加载文件管理器
    FileManager: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/src/components/files/file-manager.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((mod)=>({
                default: mod.FileManager
            })), {
        loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse h-96 bg-muted rounded-lg"
            }, void 0, false, {
                fileName: "[project]/src/components/performance/performance-monitor.tsx",
                lineNumber: 124,
                columnNumber: 20
            }, this),
        ssr: false
    }),
    // 懒加载搜索界面
    SearchInterface: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/src/components/search/search-interface.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((mod)=>({
                default: mod.SearchInterface
            })), {
        loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse h-64 bg-muted rounded-lg"
            }, void 0, false, {
                fileName: "[project]/src/components/performance/performance-monitor.tsx",
                lineNumber: 130,
                columnNumber: 20
            }, this),
        ssr: false
    }),
    // 懒加载 AI 助手
    AIAssistant: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/src/components/article/ai-assistant.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then((mod)=>({
                default: mod.AIAssistant
            })), {
        loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse h-48 bg-muted rounded-lg"
            }, void 0, false, {
                fileName: "[project]/src/components/performance/performance-monitor.tsx",
                lineNumber: 136,
                columnNumber: 20
            }, this),
        ssr: false
    })
};
;
function OptimizedImage({ src, alt, width, height, className, priority = false, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        src: src,
        alt: alt,
        width: width,
        height: height,
        className: className,
        priority: priority,
        placeholder: "blur",
        blurDataURL: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==",
        loading: priority ? "eager" : "lazy",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/performance/performance-monitor.tsx",
        lineNumber: 165,
        columnNumber: 5
    }, this);
}
_c1 = OptimizedImage;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "PerformanceMonitor");
__turbopack_context__.k.register(_c1, "OptimizedImage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "formatDate": (()=>formatDate),
    "formatFileSize": (()=>formatFileSize),
    "formatRelativeTime": (()=>formatRelativeTime),
    "generateRandomString": (()=>generateRandomString),
    "generateSlug": (()=>generateSlug),
    "isValidEmail": (()=>isValidEmail),
    "throttle": (()=>throttle),
    "truncateText": (()=>truncateText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date) {
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
function formatRelativeTime(date) {
    const d = new Date(date);
    const now = new Date();
    const diff = now.getTime() - d.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);
    const years = Math.floor(months / 12);
    if (years > 0) return `${years}年前`;
    if (months > 0) return `${months}个月前`;
    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
}
function generateSlug(title) {
    return title.toLowerCase().replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/[\s_-]+/g, '-') // 替换空格和下划线为连字符
    .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符
}
function truncateText(text, length) {
    if (text.length <= length) return text;
    return text.slice(0, length) + '...';
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB',
        'TB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for(let i = 0; i < length; i++){
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/accessibility.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AccessibleDialog": (()=>AccessibleDialog),
    "AccessibleLabel": (()=>AccessibleLabel),
    "FocusTrap": (()=>FocusTrap),
    "LiveAnnouncer": (()=>LiveAnnouncer),
    "SkipToContent": (()=>SkipToContent),
    "VisuallyHidden": (()=>VisuallyHidden)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
function SkipToContent() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
        href: "#main-content",
        className: "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:p-4 focus:bg-background focus:border focus:rounded-md",
        children: "跳到主要内容"
    }, void 0, false, {
        fileName: "[project]/src/components/ui/accessibility.tsx",
        lineNumber: 12,
        columnNumber: 5
    }, this);
}
_c = SkipToContent;
function VisuallyHidden({ children, as: Component = "span", ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
        className: "sr-only",
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/accessibility.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
}
_c1 = VisuallyHidden;
function FocusTrap({ children, active = true, className, ...props }) {
    _s();
    const startRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const endRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // 处理焦点陷阱
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FocusTrap.useEffect": ()=>{
            if (!active) return;
            const handleKeyDown = {
                "FocusTrap.useEffect.handleKeyDown": (e)=>{
                    if (e.key !== "Tab") return;
                    if (!containerRef.current) return;
                    const focusableElements = containerRef.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];
                    // 如果按下 Shift+Tab 并且当前焦点在第一个元素上，则将焦点移到最后一个元素
                    if (e.shiftKey && document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    } else if (!e.shiftKey && document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }["FocusTrap.useEffect.handleKeyDown"];
            document.addEventListener("keydown", handleKeyDown);
            return ({
                "FocusTrap.useEffect": ()=>document.removeEventListener("keydown", handleKeyDown)
            })["FocusTrap.useEffect"];
        }
    }["FocusTrap.useEffect"], [
        active
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: containerRef,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("outline-none", className),
        tabIndex: -1,
        ...props,
        children: [
            active && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: startRef,
                tabIndex: 0,
                onFocus: ()=>{
                    const focusableElements = containerRef.current?.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                    const lastElement = focusableElements?.[focusableElements.length - 1];
                    lastElement?.focus();
                },
                className: "sr-only",
                "aria-hidden": "true"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/accessibility.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this),
            children,
            active && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: endRef,
                tabIndex: 0,
                onFocus: ()=>{
                    const focusableElements = containerRef.current?.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                    const firstElement = focusableElements?.[0];
                    firstElement?.focus();
                },
                className: "sr-only",
                "aria-hidden": "true"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/accessibility.tsx",
                lineNumber: 121,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/accessibility.tsx",
        lineNumber: 96,
        columnNumber: 5
    }, this);
}
_s(FocusTrap, "IpaO3exWRlkVU2quHZiFpy/Jr8A=");
_c2 = FocusTrap;
function AccessibleLabel({ id, label, children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: id,
                className: "sr-only",
                children: label
            }, void 0, false, {
                fileName: "[project]/src/components/ui/accessibility.tsx",
                lineNumber: 154,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/accessibility.tsx",
        lineNumber: 153,
        columnNumber: 5
    }, this);
}
_c3 = AccessibleLabel;
function LiveAnnouncer({ message, politeness = "polite" }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "aria-live": politeness,
        "aria-atomic": "true",
        className: "sr-only",
        children: message
    }, void 0, false, {
        fileName: "[project]/src/components/ui/accessibility.tsx",
        lineNumber: 177,
        columnNumber: 5
    }, this);
}
_c4 = LiveAnnouncer;
function AccessibleDialog({ isOpen, onClose, title, description, children, className }) {
    _s1();
    const dialogRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // 处理 ESC 键关闭对话框
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AccessibleDialog.useEffect": ()=>{
            if (!isOpen) return;
            const handleKeyDown = {
                "AccessibleDialog.useEffect.handleKeyDown": (e)=>{
                    if (e.key === "Escape") {
                        onClose();
                    }
                }
            }["AccessibleDialog.useEffect.handleKeyDown"];
            document.addEventListener("keydown", handleKeyDown);
            return ({
                "AccessibleDialog.useEffect": ()=>document.removeEventListener("keydown", handleKeyDown)
            })["AccessibleDialog.useEffect"];
        }
    }["AccessibleDialog.useEffect"], [
        isOpen,
        onClose
    ]);
    // 当对话框打开时，将焦点移到对话框
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AccessibleDialog.useEffect": ()=>{
            if (isOpen && dialogRef.current) {
                dialogRef.current.focus();
            }
        }
    }["AccessibleDialog.useEffect"], [
        isOpen
    ]);
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FocusTrap, {
        active: isOpen,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            ref: dialogRef,
            role: "dialog",
            "aria-modal": "true",
            "aria-labelledby": "dialog-title",
            "aria-describedby": description ? "dialog-description" : undefined,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("fixed inset-0 z-50 flex items-center justify-center bg-black/50", className),
            tabIndex: -1,
            onClick: (e)=>{
                if (e.target === e.currentTarget) {
                    onClose();
                }
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-background rounded-lg shadow-lg p-6 max-w-md w-full max-h-[90vh] overflow-auto",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        id: "dialog-title",
                        className: "text-xl font-bold mb-2",
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/accessibility.tsx",
                        lineNumber: 251,
                        columnNumber: 11
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        id: "dialog-description",
                        className: "text-muted-foreground mb-4",
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/accessibility.tsx",
                        lineNumber: 256,
                        columnNumber: 13
                    }, this),
                    children
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/accessibility.tsx",
                lineNumber: 250,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/accessibility.tsx",
            lineNumber: 233,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/accessibility.tsx",
        lineNumber: 232,
        columnNumber: 5
    }, this);
}
_s1(AccessibleDialog, "U+/X/qeN55EDExe8kuNWsEWL/IA=");
_c5 = AccessibleDialog;
var _c, _c1, _c2, _c3, _c4, _c5;
__turbopack_context__.k.register(_c, "SkipToContent");
__turbopack_context__.k.register(_c1, "VisuallyHidden");
__turbopack_context__.k.register(_c2, "FocusTrap");
__turbopack_context__.k.register(_c3, "AccessibleLabel");
__turbopack_context__.k.register(_c4, "LiveAnnouncer");
__turbopack_context__.k.register(_c5, "AccessibleDialog");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_1bf16b33._.js.map
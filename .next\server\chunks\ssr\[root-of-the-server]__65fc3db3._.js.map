{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/providers/session-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport { SessionProvider } from \"next-auth/react\"\nimport { ReactNode } from \"react\"\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  return <SessionProvider>{children}</SessionProvider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBAAO,8OAAC,8IAAA,CAAA,kBAAe;kBAAE;;;;;;AAC3B", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/performance/performance-monitor.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect } from \"react\"\n\n/**\n * 性能监控组件\n * 监控页面性能指标并上报\n */\nexport function PerformanceMonitor() {\n  useEffect(() => {\n    // 监控 Core Web Vitals\n    if (typeof window !== \"undefined\" && \"performance\" in window) {\n      // 监控 LCP (Largest Contentful Paint)\n      const observer = new PerformanceObserver((list) => {\n        for (const entry of list.getEntries()) {\n          if (entry.entryType === \"largest-contentful-paint\") {\n            console.log(\"LCP:\", entry.startTime)\n            // 在实际应用中，这里应该上报到分析服务\n          }\n        }\n      })\n\n      try {\n        observer.observe({ entryTypes: [\"largest-contentful-paint\"] })\n      } catch (e) {\n        // 浏览器不支持\n      }\n\n      // 监控 FID (First Input Delay)\n      const fidObserver = new PerformanceObserver((list) => {\n        for (const entry of list.getEntries()) {\n          if (entry.entryType === \"first-input\") {\n            const fid = entry.processingStart - entry.startTime\n            console.log(\"FID:\", fid)\n            // 在实际应用中，这里应该上报到分析服务\n          }\n        }\n      })\n\n      try {\n        fidObserver.observe({ entryTypes: [\"first-input\"] })\n      } catch (e) {\n        // 浏览器不支持\n      }\n\n      // 监控 CLS (Cumulative Layout Shift)\n      let clsValue = 0\n      const clsObserver = new PerformanceObserver((list) => {\n        for (const entry of list.getEntries()) {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value\n          }\n        }\n        console.log(\"CLS:\", clsValue)\n      })\n\n      try {\n        clsObserver.observe({ entryTypes: [\"layout-shift\"] })\n      } catch (e) {\n        // 浏览器不支持\n      }\n\n      // 监控页面加载时间\n      window.addEventListener(\"load\", () => {\n        const navigation = performance.getEntriesByType(\"navigation\")[0] as PerformanceNavigationTiming\n        if (navigation) {\n          const loadTime = navigation.loadEventEnd - navigation.fetchStart\n          const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart\n          const firstByte = navigation.responseStart - navigation.fetchStart\n\n          console.log(\"Page Load Time:\", loadTime)\n          console.log(\"DOM Content Loaded:\", domContentLoaded)\n          console.log(\"Time to First Byte:\", firstByte)\n        }\n      })\n\n      // 清理函数\n      return () => {\n        observer.disconnect()\n        fidObserver.disconnect()\n        clsObserver.disconnect()\n      }\n    }\n  }, [])\n\n  return null // 这是一个监控组件，不渲染任何内容\n}\n\n/**\n * 资源预加载 Hook\n */\nexport function useResourcePreload() {\n  useEffect(() => {\n    // 预加载关键资源\n    const preloadResources = [\n      { href: \"/fonts/inter.woff2\", as: \"font\", type: \"font/woff2\" },\n      // 可以添加更多需要预加载的资源\n    ]\n\n    preloadResources.forEach(({ href, as, type }) => {\n      const link = document.createElement(\"link\")\n      link.rel = \"preload\"\n      link.href = href\n      link.as = as\n      if (type) link.type = type\n      link.crossOrigin = \"anonymous\"\n      document.head.appendChild(link)\n    })\n  }, [])\n}\n\n/**\n * 代码分割和懒加载工具\n */\nexport const LazyComponents = {\n  // 懒加载文章编辑器\n  ArticleEditor: dynamic(() => import(\"@/components/article/article-editor\").then(mod => ({ default: mod.ArticleEditor })), {\n    loading: () => <div className=\"animate-pulse h-96 bg-muted rounded-lg\"></div>,\n    ssr: false,\n  }),\n\n  // 懒加载文件管理器\n  FileManager: dynamic(() => import(\"@/components/files/file-manager\").then(mod => ({ default: mod.FileManager })), {\n    loading: () => <div className=\"animate-pulse h-96 bg-muted rounded-lg\"></div>,\n    ssr: false,\n  }),\n\n  // 懒加载搜索界面\n  SearchInterface: dynamic(() => import(\"@/components/search/search-interface\").then(mod => ({ default: mod.SearchInterface })), {\n    loading: () => <div className=\"animate-pulse h-64 bg-muted rounded-lg\"></div>,\n    ssr: false,\n  }),\n\n  // 懒加载 AI 助手\n  AIAssistant: dynamic(() => import(\"@/components/article/ai-assistant\").then(mod => ({ default: mod.AIAssistant })), {\n    loading: () => <div className=\"animate-pulse h-48 bg-muted rounded-lg\"></div>,\n    ssr: false,\n  }),\n}\n\n// 需要在文件顶部导入 dynamic\nimport dynamic from \"next/dynamic\"\n\n/**\n * 图片优化组件\n */\nexport function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className,\n  priority = false,\n  ...props\n}: {\n  src: string\n  alt: string\n  width?: number\n  height?: number\n  className?: string\n  priority?: boolean\n  [key: string]: any\n}) {\n  return (\n    <Image\n      src={src}\n      alt={alt}\n      width={width}\n      height={height}\n      className={className}\n      priority={priority}\n      placeholder=\"blur\"\n      blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n      loading={priority ? \"eager\" : \"lazy\"}\n      {...props}\n    />\n  )\n}\n\n// 需要在文件顶部导入 Image\nimport Image from \"next/image\"\n"], "names": [], "mappings": ";;;;;;;AAEA;AA0IA,oBAAoB;AACpB;AAsCA,kBAAkB;AAClB;AApLA;;;AAQO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;QACrB,IAAI,gBAAkB,eAAe,iBAAiB,QAAQ;;QAuE9D;IACF,GAAG,EAAE;IAEL,OAAO,KAAK,mBAAmB;;AACjC;AAKO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;QACV,MAAM,mBAAmB;YACvB;gBAAE,MAAM;gBAAsB,IAAI;gBAAQ,MAAM;YAAa;SAE9D;QAED,iBAAiB,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE;YAC1C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,GAAG,GAAG;YACX,KAAK,IAAI,GAAG;YACZ,KAAK,EAAE,GAAG;YACV,IAAI,MAAM,KAAK,IAAI,GAAG;YACtB,KAAK,WAAW,GAAG;YACnB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF,GAAG,EAAE;AACP;AAKO,MAAM,iBAAiB;IAC5B,WAAW;IACX,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,6IAA8C,IAAI,CAAC,CAAA,MAAO,CAAC;gBAAE,SAAS,IAAI,aAAa;YAAC,CAAC,IAAI;QACxH,SAAS,kBAAM,8OAAC;gBAAI,WAAU;;;;;;QAC9B,KAAK;IACP;IAEA,WAAW;IACX,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,yIAA0C,IAAI,CAAC,CAAA,MAAO,CAAC;gBAAE,SAAS,IAAI,WAAW;YAAC,CAAC,IAAI;QAChH,SAAS,kBAAM,8OAAC;gBAAI,WAAU;;;;;;QAC9B,KAAK;IACP;IAEA,UAAU;IACV,iBAAiB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,8IAA+C,IAAI,CAAC,CAAA,MAAO,CAAC;gBAAE,SAAS,IAAI,eAAe;YAAC,CAAC,IAAI;QAC7H,SAAS,kBAAM,8OAAC;gBAAI,WAAU;;;;;;QAC9B,KAAK;IACP;IAEA,YAAY;IACZ,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,IAAM,2IAA4C,IAAI,CAAC,CAAA,MAAO,CAAC;gBAAE,SAAS,IAAI,WAAW;YAAC,CAAC,IAAI;QAClH,SAAS,kBAAM,8OAAC;gBAAI,WAAU;;;;;;QAC9B,KAAK;IACP;AACF;;AAQO,SAAS,eAAe,EAC7B,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OASJ;IACC,qBACE,8OAAC,6HAAA,CAAA,UAAK;QACJ,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX,UAAU;QACV,aAAY;QACZ,aAAY;QACZ,SAAS,WAAW,UAAU;QAC7B,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// 格式化日期\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n// 格式化相对时间\nexport function formatRelativeTime(date: Date | string): string {\n  const d = new Date(date);\n  const now = new Date();\n  const diff = now.getTime() - d.getTime();\n\n  const seconds = Math.floor(diff / 1000);\n  const minutes = Math.floor(seconds / 60);\n  const hours = Math.floor(minutes / 60);\n  const days = Math.floor(hours / 24);\n  const months = Math.floor(days / 30);\n  const years = Math.floor(months / 12);\n\n  if (years > 0) return `${years}年前`;\n  if (months > 0) return `${months}个月前`;\n  if (days > 0) return `${days}天前`;\n  if (hours > 0) return `${hours}小时前`;\n  if (minutes > 0) return `${minutes}分钟前`;\n  return '刚刚';\n}\n\n// 生成 slug\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // 移除特殊字符\n    .replace(/[\\s_-]+/g, '-') // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符\n}\n\n// 截取文本\nexport function truncateText(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.slice(0, length) + '...';\n}\n\n// 格式化文件大小\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// 验证邮箱\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 生成随机字符串\nexport function generateRandomString(length: number): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,IAAI,OAAO,KAAK,EAAE,OAAO;IAEtC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;IAClC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,MAAM,SAAS,KAAK,KAAK,CAAC,OAAO;IACjC,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;IAElC,IAAI,QAAQ,GAAG,OAAO,GAAG,MAAM,EAAE,CAAC;IAClC,IAAI,SAAS,GAAG,OAAO,GAAG,OAAO,GAAG,CAAC;IACrC,IAAI,OAAO,GAAG,OAAO,GAAG,KAAK,EAAE,CAAC;IAChC,IAAI,QAAQ,GAAG,OAAO,GAAG,MAAM,GAAG,CAAC;IACnC,IAAI,UAAU,GAAG,OAAO,GAAG,QAAQ,GAAG,CAAC;IACvC,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,SAAS;KAClC,OAAO,CAAC,YAAY,KAAK,eAAe;KACxC,OAAO,CAAC,YAAY,KAAK,cAAc;AAC5C;AAGO,SAAS,aAAa,IAAY,EAAE,MAAc;IACvD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,qBAAqB,MAAc;IACjD,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/accessibility.tsx"], "sourcesContent": ["\"use client\"\n\nimport { ReactNode, useRef, useEffect } from \"react\"\nimport { cn } from \"@/lib/utils\"\n\n/**\n * 跳过导航链接组件\n * 允许键盘用户跳过导航直接到主要内容\n */\nexport function SkipToContent() {\n  return (\n    <a\n      href=\"#main-content\"\n      className=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:p-4 focus:bg-background focus:border focus:rounded-md\"\n    >\n      跳到主要内容\n    </a>\n  )\n}\n\n/**\n * 可视化隐藏组件\n * 对屏幕阅读器可见，但视觉上隐藏\n */\nexport function VisuallyHidden({\n  children,\n  as: Component = \"span\",\n  ...props\n}: {\n  children: ReactNode\n  as?: React.ElementType\n  [key: string]: any\n}) {\n  return (\n    <Component\n      className=\"sr-only\"\n      {...props}\n    >\n      {children}\n    </Component>\n  )\n}\n\n/**\n * 焦点陷阱组件\n * 将键盘焦点限制在组件内部\n */\nexport function FocusTrap({\n  children,\n  active = true,\n  className,\n  ...props\n}: {\n  children: ReactNode\n  active?: boolean\n  className?: string\n  [key: string]: any\n}) {\n  const startRef = useRef<HTMLDivElement>(null)\n  const endRef = useRef<HTMLDivElement>(null)\n  const containerRef = useRef<HTMLDivElement>(null)\n\n  // 处理焦点陷阱\n  useEffect(() => {\n    if (!active) return\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key !== \"Tab\") return\n\n      if (!containerRef.current) return\n\n      const focusableElements = containerRef.current.querySelectorAll(\n        'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n      )\n\n      const firstElement = focusableElements[0] as HTMLElement\n      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement\n\n      // 如果按下 Shift+Tab 并且当前焦点在第一个元素上，则将焦点移到最后一个元素\n      if (e.shiftKey && document.activeElement === firstElement) {\n        e.preventDefault()\n        lastElement.focus()\n      }\n      // 如果按下 Tab 并且当前焦点在最后一个元素上，则将焦点移到第一个元素\n      else if (!e.shiftKey && document.activeElement === lastElement) {\n        e.preventDefault()\n        firstElement.focus()\n      }\n    }\n\n    document.addEventListener(\"keydown\", handleKeyDown)\n    return () => document.removeEventListener(\"keydown\", handleKeyDown)\n  }, [active])\n\n  return (\n    <div\n      ref={containerRef}\n      className={cn(\"outline-none\", className)}\n      tabIndex={-1}\n      {...props}\n    >\n      {active && (\n        <div\n          ref={startRef}\n          tabIndex={0}\n          onFocus={() => {\n            const focusableElements = containerRef.current?.querySelectorAll(\n              'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n            )\n            const lastElement = focusableElements?.[focusableElements.length - 1] as HTMLElement\n            lastElement?.focus()\n          }}\n          className=\"sr-only\"\n          aria-hidden=\"true\"\n        />\n      )}\n\n      {children}\n\n      {active && (\n        <div\n          ref={endRef}\n          tabIndex={0}\n          onFocus={() => {\n            const focusableElements = containerRef.current?.querySelectorAll(\n              'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n            )\n            const firstElement = focusableElements?.[0] as HTMLElement\n            firstElement?.focus()\n          }}\n          className=\"sr-only\"\n          aria-hidden=\"true\"\n        />\n      )}\n    </div>\n  )\n}\n\n/**\n * 无障碍标签组件\n * 为元素提供可访问的标签\n */\nexport function AccessibleLabel({\n  id,\n  label,\n  children,\n}: {\n  id: string\n  label: string\n  children: ReactNode\n}) {\n  return (\n    <div>\n      <label\n        htmlFor={id}\n        className=\"sr-only\"\n      >\n        {label}\n      </label>\n      {children}\n    </div>\n  )\n}\n\n/**\n * 无障碍公告组件\n * 向屏幕阅读器用户宣布动态内容变化\n */\nexport function LiveAnnouncer({\n  message,\n  politeness = \"polite\",\n}: {\n  message: string\n  politeness?: \"polite\" | \"assertive\"\n}) {\n  return (\n    <div\n      aria-live={politeness}\n      aria-atomic=\"true\"\n      className=\"sr-only\"\n    >\n      {message}\n    </div>\n  )\n}\n\n/**\n * 无障碍对话框组件\n * 确保对话框对屏幕阅读器用户可访问\n */\nexport function AccessibleDialog({\n  isOpen,\n  onClose,\n  title,\n  description,\n  children,\n  className,\n}: {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  description?: string\n  children: ReactNode\n  className?: string\n}) {\n  const dialogRef = useRef<HTMLDivElement>(null)\n\n  // 处理 ESC 键关闭对话框\n  useEffect(() => {\n    if (!isOpen) return\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === \"Escape\") {\n        onClose()\n      }\n    }\n\n    document.addEventListener(\"keydown\", handleKeyDown)\n    return () => document.removeEventListener(\"keydown\", handleKeyDown)\n  }, [isOpen, onClose])\n\n  // 当对话框打开时，将焦点移到对话框\n  useEffect(() => {\n    if (isOpen && dialogRef.current) {\n      dialogRef.current.focus()\n    }\n  }, [isOpen])\n\n  if (!isOpen) return null\n\n  return (\n    <FocusTrap active={isOpen}>\n      <div\n        ref={dialogRef}\n        role=\"dialog\"\n        aria-modal=\"true\"\n        aria-labelledby=\"dialog-title\"\n        aria-describedby={description ? \"dialog-description\" : undefined}\n        className={cn(\n          \"fixed inset-0 z-50 flex items-center justify-center bg-black/50\",\n          className\n        )}\n        tabIndex={-1}\n        onClick={(e) => {\n          if (e.target === e.currentTarget) {\n            onClose()\n          }\n        }}\n      >\n        <div className=\"bg-background rounded-lg shadow-lg p-6 max-w-md w-full max-h-[90vh] overflow-auto\">\n          <h2 id=\"dialog-title\" className=\"text-xl font-bold mb-2\">\n            {title}\n          </h2>\n          \n          {description && (\n            <p id=\"dialog-description\" className=\"text-muted-foreground mb-4\">\n              {description}\n            </p>\n          )}\n          \n          {children}\n        </div>\n      </div>\n    </FocusTrap>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AASO,SAAS;IACd,qBACE,8OAAC;QACC,MAAK;QACL,WAAU;kBACX;;;;;;AAIL;AAMO,SAAS,eAAe,EAC7B,QAAQ,EACR,IAAI,YAAY,MAAM,EACtB,GAAG,OAKJ;IACC,qBACE,8OAAC;QACC,WAAU;QACT,GAAG,KAAK;kBAER;;;;;;AAGP;AAMO,SAAS,UAAU,EACxB,QAAQ,EACR,SAAS,IAAI,EACb,SAAS,EACT,GAAG,OAMJ;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACxC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACtC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,OAAO;YAErB,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,MAAM,oBAAoB,aAAa,OAAO,CAAC,gBAAgB,CAC7D;YAGF,MAAM,eAAe,iBAAiB,CAAC,EAAE;YACzC,MAAM,cAAc,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;YAEnE,4CAA4C;YAC5C,IAAI,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,cAAc;gBACzD,EAAE,cAAc;gBAChB,YAAY,KAAK;YACnB,OAEK,IAAI,CAAC,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,aAAa;gBAC9D,EAAE,cAAc;gBAChB,aAAa,KAAK;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC9B,UAAU,CAAC;QACV,GAAG,KAAK;;YAER,wBACC,8OAAC;gBACC,KAAK;gBACL,UAAU;gBACV,SAAS;oBACP,MAAM,oBAAoB,aAAa,OAAO,EAAE,iBAC9C;oBAEF,MAAM,cAAc,mBAAmB,CAAC,kBAAkB,MAAM,GAAG,EAAE;oBACrE,aAAa;gBACf;gBACA,WAAU;gBACV,eAAY;;;;;;YAIf;YAEA,wBACC,8OAAC;gBACC,KAAK;gBACL,UAAU;gBACV,SAAS;oBACP,MAAM,oBAAoB,aAAa,OAAO,EAAE,iBAC9C;oBAEF,MAAM,eAAe,mBAAmB,CAAC,EAAE;oBAC3C,cAAc;gBAChB;gBACA,WAAU;gBACV,eAAY;;;;;;;;;;;;AAKtB;AAMO,SAAS,gBAAgB,EAC9B,EAAE,EACF,KAAK,EACL,QAAQ,EAKT;IACC,qBACE,8OAAC;;0BACC,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;YAEF;;;;;;;AAGP;AAMO,SAAS,cAAc,EAC5B,OAAO,EACP,aAAa,QAAQ,EAItB;IACC,qBACE,8OAAC;QACC,aAAW;QACX,eAAY;QACZ,WAAU;kBAET;;;;;;AAGP;AAMO,SAAS,iBAAiB,EAC/B,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW,EACX,QAAQ,EACR,SAAS,EAQV;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAQ;KAAQ;IAEpB,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,UAAU,OAAO,EAAE;YAC/B,UAAU,OAAO,CAAC,KAAK;QACzB;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAU,QAAQ;kBACjB,cAAA,8OAAC;YACC,KAAK;YACL,MAAK;YACL,cAAW;YACX,mBAAgB;YAChB,oBAAkB,cAAc,uBAAuB;YACvD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA;YAEF,UAAU,CAAC;YACX,SAAS,CAAC;gBACR,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;oBAChC;gBACF;YACF;sBAEA,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,IAAG;wBAAe,WAAU;kCAC7B;;;;;;oBAGF,6BACC,8OAAC;wBAAE,IAAG;wBAAqB,WAAU;kCAClC;;;;;;oBAIJ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}
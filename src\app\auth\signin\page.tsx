"use client"

import { useState, useEffect } from "react"
import { signIn, getSession } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import { Gith<PERSON>, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { siteConfig } from "@/config/site"

export default function SignInPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const callbackUrl = searchParams.get('callbackUrl') || '/'
  const errorParam = searchParams.get('error')

  useEffect(() => {
    // 检查是否已经登录
    getSession().then((session) => {
      if (session) {
        router.push(callbackUrl)
      }
    })
  }, [router, callbackUrl])

  useEffect(() => {
    // 处理登录错误
    if (errorParam) {
      switch (errorParam) {
        case 'OAuthSignin':
          setError('OAuth 登录失败，请重试')
          break
        case 'OAuthCallback':
          setError('OAuth 回调失败，请重试')
          break
        case 'OAuthCreateAccount':
          setError('创建账户失败，请重试')
          break
        case 'EmailCreateAccount':
          setError('邮箱账户创建失败，请重试')
          break
        case 'Callback':
          setError('回调失败，请重试')
          break
        case 'OAuthAccountNotLinked':
          setError('该邮箱已与其他账户关联')
          break
        case 'EmailSignin':
          setError('邮箱登录失败，请重试')
          break
        case 'CredentialsSignin':
          setError('凭据登录失败，请检查用户名和密码')
          break
        case 'SessionRequired':
          setError('需要登录才能访问此页面')
          break
        default:
          setError('登录失败，请重试')
      }
    }
  }, [errorParam])

  const handleGithubSignIn = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const result = await signIn('github', {
        callbackUrl,
        redirect: false,
      })
      
      if (result?.error) {
        setError('GitHub 登录失败，请重试')
      } else if (result?.url) {
        router.push(result.url)
      }
    } catch (error) {
      console.error('Sign in error:', error)
      setError('登录过程中发生错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            欢迎回来
          </CardTitle>
          <CardDescription>
            登录到 {siteConfig.name}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <Button
            onClick={handleGithubSignIn}
            disabled={isLoading}
            className="w-full"
            size="lg"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Github className="w-4 h-4 mr-2" />
            )}
            使用 GitHub 登录
          </Button>
          
          <div className="text-center text-sm text-muted-foreground">
            <p>
              登录即表示您同意我们的
              <a href="/terms" className="underline hover:text-primary">
                服务条款
              </a>
              和
              <a href="/privacy" className="underline hover:text-primary">
                隐私政策
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import React, { Component, ErrorInfo, ReactNode } from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCcw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

/**
 * 错误边界组件
 * 捕获子组件中的 JavaScript 错误，并显示备用 UI
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // 更新 state，下次渲染时显示备用 UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 记录错误信息
    this.setState({ errorInfo })
    
    // 调用错误处理回调
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
    
    // 在实际应用中，这里应该上报错误到监控服务
    console.error("Error caught by ErrorBoundary:", error, errorInfo)
  }

  handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // 如果提供了自定义的 fallback，则使用它
      if (this.props.fallback) {
        return this.props.fallback
      }
      
      // 默认的错误 UI
      return (
        <Card className="border-destructive">
          <CardHeader className="bg-destructive/10">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <CardTitle>出错了</CardTitle>
            </div>
            <CardDescription>
              组件渲染过程中发生错误
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <p className="text-muted-foreground">
                很抱歉，页面的这一部分遇到了问题。您可以尝试刷新页面或返回首页。
              </p>
              
              {process.env.NODE_ENV === "development" && this.state.error && (
                <div className="p-4 bg-muted rounded-lg overflow-auto max-h-40">
                  <p className="font-mono text-sm text-destructive">
                    {this.state.error.toString()}
                  </p>
                  {this.state.errorInfo && (
                    <pre className="mt-2 text-xs text-muted-foreground">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  )}
                </div>
              )}
              
              <div className="flex gap-4">
                <Button onClick={this.handleReset}>
                  <RefreshCcw className="w-4 h-4 mr-2" />
                  重试
                </Button>
                <Button variant="outline" onClick={() => window.location.reload()}>
                  刷新页面
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

/**
 * 错误页面组件
 * 用于全局错误处理
 */
export function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <CardTitle>出错了</CardTitle>
          </div>
          <CardDescription>
            页面加载过程中发生错误
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <p className="text-muted-foreground">
              很抱歉，页面加载过程中遇到了问题。您可以尝试刷新页面或返回首页。
            </p>
            
            {process.env.NODE_ENV === "development" && (
              <div className="p-4 bg-muted rounded-lg overflow-auto max-h-40">
                <p className="font-mono text-sm text-destructive">
                  {error.message}
                </p>
                {error.stack && (
                  <pre className="mt-2 text-xs text-muted-foreground">
                    {error.stack}
                  </pre>
                )}
                {error.digest && (
                  <p className="mt-2 text-xs">
                    错误 ID: {error.digest}
                  </p>
                )}
              </div>
            )}
            
            <div className="flex gap-4">
              <Button onClick={reset}>
                <RefreshCcw className="w-4 h-4 mr-2" />
                重试
              </Button>
              <Button variant="outline" onClick={() => window.location.href = "/"}>
                返回首页
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

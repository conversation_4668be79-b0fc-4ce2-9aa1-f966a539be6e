{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/node_modules/prismjs/components/prism-tsx.js"], "sourcesContent": ["(function (Prism) {\n\tvar typescript = Prism.util.clone(Prism.languages.typescript);\n\tPrism.languages.tsx = Prism.languages.extend('jsx', typescript);\n\n\t// doesn't work with TS because TS is too complex\n\tdelete Prism.languages.tsx['parameter'];\n\tdelete Prism.languages.tsx['literal-property'];\n\n\t// This will prevent collisions between TSX tags and TS generic types.\n\t// Idea by https://github.com/karlhorky\n\t// Discussion: https://github.com/PrismJS/prism/issues/2594#issuecomment-710666928\n\tvar tag = Prism.languages.tsx.tag;\n\ttag.pattern = RegExp(/(^|[^\\w$]|(?=<\\/))/.source + '(?:' + tag.pattern.source + ')', tag.pattern.flags);\n\ttag.lookbehind = true;\n}(Prism));\n"], "names": [], "mappings": "AAAC,CAAA,SAAU,MAAK;IACf,IAAI,aAAa,OAAM,IAAI,CAAC,KAAK,CAAC,OAAM,SAAS,CAAC,UAAU;IAC5D,OAAM,SAAS,CAAC,GAAG,GAAG,OAAM,SAAS,CAAC,MAAM,CAAC,OAAO;IAEpD,iDAAiD;IACjD,OAAO,OAAM,SAAS,CAAC,GAAG,CAAC,YAAY;IACvC,OAAO,OAAM,SAAS,CAAC,GAAG,CAAC,mBAAmB;IAE9C,sEAAsE;IACtE,uCAAuC;IACvC,kFAAkF;IAClF,IAAI,MAAM,OAAM,SAAS,CAAC,GAAG,CAAC,GAAG;IACjC,IAAI,OAAO,GAAG,OAAO,qBAAqB,MAAM,GAAG,QAAQ,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,IAAI,OAAO,CAAC,KAAK;IACtG,IAAI,UAAU,GAAG;AAClB,CAAA,EAAE", "ignoreList": [0], "debugId": null}}]}
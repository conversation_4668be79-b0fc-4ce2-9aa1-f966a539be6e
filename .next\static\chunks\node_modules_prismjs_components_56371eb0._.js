(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/prismjs/components/prism-javascript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-javascript_f2f676f7.js",
  "static/chunks/node_modules_prismjs_components_prism-javascript_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-javascript.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-typescript.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-typescript_4585186b.js",
  "static/chunks/node_modules_prismjs_components_prism-typescript_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-typescript.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-jsx.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-jsx_5645e6be.js",
  "static/chunks/node_modules_prismjs_components_prism-jsx_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-jsx.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-tsx.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-tsx_98321d08.js",
  "static/chunks/node_modules_prismjs_components_prism-tsx_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-tsx.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-css.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-css_afbdf811.js",
  "static/chunks/node_modules_prismjs_components_prism-css_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-css.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-scss.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-scss_75b4de24.js",
  "static/chunks/node_modules_prismjs_components_prism-scss_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-scss.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-json.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-json_b4af9161.js",
  "static/chunks/node_modules_prismjs_components_prism-json_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-json.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-bash.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-bash_7871d1fb.js",
  "static/chunks/node_modules_prismjs_components_prism-bash_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-bash.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-python.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-python_078c766a.js",
  "static/chunks/node_modules_prismjs_components_prism-python_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-python.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/prismjs/components/prism-sql.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_prism-sql_39097fb3.js",
  "static/chunks/node_modules_prismjs_components_prism-sql_cba4e627.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/prismjs/components/prism-sql.js [app-client] (ecmascript)");
    });
});
}}),
}]);
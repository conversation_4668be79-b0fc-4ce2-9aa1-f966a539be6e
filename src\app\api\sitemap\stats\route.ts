import { NextRequest, NextResponse } from 'next/server'
import { D1Database } from '@cloudflare/workers-types'
import { getRequestContext } from '@/lib/cloudflare'

// 模拟站点地图统计数据
const mockSitemapStats = {
  totalUrls: 1254,
  lastGenerated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
  fileSize: 1024 * 1024 * 0.5, // 0.5MB
  status: 'success',
  errors: [],
  warnings: []
}

export async function GET(request: NextRequest) {
  try {
    // 在实际应用中，这里应该从数据库中获取统计数据
    // const { env } = getRequestContext()
    // const db = env.DB as D1Database
    // const result = await db.prepare('SELECT * FROM sitemap_stats LIMIT 1').first()
    // const stats = result || mockSitemapStats
    
    // 目前使用模拟数据
    const stats = mockSitemapStats
    
    return NextResponse.json({
      success: true,
      data: { stats }
    })
  } catch (error) {
    console.error('Sitemap stats API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch sitemap statistics' },
      { status: 500 }
    )
  }
}

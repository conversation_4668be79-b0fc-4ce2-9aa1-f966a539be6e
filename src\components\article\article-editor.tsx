"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Save, Eye, Send, ArrowLeft, <PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { MarkdownRenderer } from "@/components/ui/markdown-renderer"
import { SEOAnalyzer } from "./seo-analyzer"
import { AIAssistant } from "./ai-assistant"
import { mockCategories, mockTags } from "@/lib/mock-data"
import { generateSlug } from "@/lib/utils"
import type { User } from "@/types"
import { articlesAPI } from "@/lib/api/articles"

interface ArticleEditorProps {
  user: User
  articleId?: string
}

interface ArticleForm {
  title: string
  slug: string
  content: string
  excerpt: string
  category: string
  tags: string[]
  coverImage: string
  status: 'draft' | 'published'
}

export function ArticleEditor({ user, articleId }: ArticleEditorProps) {
  const router = useRouter()
  const [isPreview, setIsPreview] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isGeneratingAI, setIsGeneratingAI] = useState(false)
  
  const [form, setForm] = useState<ArticleForm>({
    title: "",
    slug: "",
    content: "",
    excerpt: "",
    category: "",
    tags: [],
    coverImage: "",
    status: "draft",
  })

  // 自动生成 slug
  useEffect(() => {
    if (form.title && !articleId) {
      setForm(prev => ({
        ...prev,
        slug: generateSlug(form.title)
      }))
    }
  }, [form.title, articleId])

  // 自动保存草稿
  useEffect(() => {
    const timer = setTimeout(() => {
      if (form.title || form.content) {
        handleSaveDraft()
      }
    }, 30000) // 30秒自动保存

    return () => clearTimeout(timer)
  }, [form])

  const handleInputChange = (field: keyof ArticleForm, value: string | string[]) => {
    setForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleTagToggle = (tag: string) => {
    setForm(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }))
  }

  const handleSaveDraft = async () => {
    setIsSaving(true)
    try {
      const articleData = {
        title: form.title,
        content: form.content,
        excerpt: form.excerpt,
        category: form.category,
        tags: form.tags,
        status: 'draft' as const,
        cover_image: form.coverImage
      }

      const result = await articlesAPI.createArticle(articleData)

      if (result.success) {
        console.log("草稿保存成功:", result.data)
        // 可以显示成功提示
        // 可以更新 URL 为编辑模式
        if (result.data?.id) {
          router.push(`/dashboard/articles/edit/${result.data.id}`)
        }
      } else {
        throw new Error(result.error || '保存失败')
      }
    } catch (error) {
      console.error("保存失败:", error)
      // 可以显示错误提示
    } finally {
      setIsSaving(false)
    }
  }

  const handlePublish = async () => {
    setIsSaving(true)
    try {
      const articleData = {
        title: form.title,
        content: form.content,
        excerpt: form.excerpt,
        category: form.category,
        tags: form.tags,
        status: 'published' as const,
        cover_image: form.coverImage
      }

      const result = await articlesAPI.createArticle(articleData)

      if (result.success) {
        console.log("文章发布成功:", result.data)
        router.push("/dashboard/articles")
      } else {
        throw new Error(result.error || '发布失败')
      }
    } catch (error) {
      console.error("发布失败:", error)
      // 可以显示错误提示
    } finally {
      setIsSaving(false)
    }
  }

  const handleGenerateAISummary = async () => {
    if (!form.content) return

    setIsGeneratingAI(true)
    try {
      const response = await fetch("/api/ai/summary", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          content: form.content,
          options: {
            maxLength: 150,
            language: "zh",
            style: "formal",
          },
        }),
      })

      const data = await response.json()

      if (data.success) {
        setForm(prev => ({
          ...prev,
          excerpt: data.data
        }))
      } else {
        console.error("AI 生成失败:", data.error)
      }
    } catch (error) {
      console.error("AI 生成失败:", error)
    } finally {
      setIsGeneratingAI(false)
    }
  }

  // 处理 AI 摘要生成
  const handleAISummaryGenerated = (summary: string) => {
    setForm(prev => ({
      ...prev,
      excerpt: summary
    }))
  }

  // 处理 AI 标签推荐
  const handleAITagsRecommended = (tags: string[]) => {
    setForm(prev => ({
      ...prev,
      tags: [...new Set([...prev.tags, ...tags])]
    }))
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* 编辑器主体 */}
      <div className="lg:col-span-2 space-y-6">
        {/* 工具栏 */}
        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回
          </Button>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => setIsPreview(!isPreview)}
            >
              <Eye className="w-4 h-4 mr-2" />
              {isPreview ? "编辑" : "预览"}
            </Button>
            <Button
              variant="outline"
              onClick={handleSaveDraft}
              disabled={isSaving}
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? "保存中..." : "保存草稿"}
            </Button>
            <Button onClick={handlePublish} disabled={isSaving}>
              <Send className="w-4 h-4 mr-2" />
              发布文章
            </Button>
          </div>
        </div>

        {/* 文章标题 */}
        <Card>
          <CardHeader>
            <CardTitle>文章信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">标题</Label>
              <Input
                id="title"
                placeholder="输入文章标题..."
                value={form.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="slug">URL 别名</Label>
              <Input
                id="slug"
                placeholder="article-slug"
                value={form.slug}
                onChange={(e) => handleInputChange("slug", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="excerpt">文章摘要</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleGenerateAISummary}
                  disabled={isGeneratingAI || !form.content}
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  {isGeneratingAI ? "生成中..." : "AI 生成"}
                </Button>
              </div>
              <Textarea
                id="excerpt"
                placeholder="输入文章摘要..."
                rows={3}
                value={form.excerpt}
                onChange={(e) => handleInputChange("excerpt", e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* 内容编辑器 */}
        <Card>
          <CardHeader>
            <CardTitle>文章内容</CardTitle>
            <CardDescription>
              使用 Markdown 语法编写文章内容
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isPreview ? (
              <div className="min-h-[400px] border rounded-lg p-4">
                <MarkdownRenderer content={form.content} />
              </div>
            ) : (
              <Textarea
                placeholder="开始编写您的文章内容..."
                className="min-h-[400px] font-mono"
                value={form.content}
                onChange={(e) => handleInputChange("content", e.target.value)}
              />
            )}
          </CardContent>
        </Card>
      </div>

      {/* 侧边栏设置 */}
      <div className="space-y-6">
        {/* 发布设置 */}
        <Card>
          <CardHeader>
            <CardTitle>发布设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="category">分类</Label>
              <Select value={form.category} onValueChange={(value) => handleInputChange("category", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  {mockCategories.map((category) => (
                    <SelectItem key={category.id} value={category.name}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>标签</Label>
              <div className="flex flex-wrap gap-2">
                {mockTags.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant={form.tags.includes(tag.name) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => handleTagToggle(tag.name)}
                  >
                    {tag.name}
                  </Badge>
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="coverImage">封面图片 URL</Label>
              <Input
                id="coverImage"
                placeholder="https://example.com/image.jpg"
                value={form.coverImage}
                onChange={(e) => handleInputChange("coverImage", e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* 文章统计 */}
        <Card>
          <CardHeader>
            <CardTitle>文章统计</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>字符数:</span>
              <span>{form.content.length}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>预计阅读时间:</span>
              <span>{Math.ceil(form.content.length / 500)} 分钟</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>状态:</span>
              <Badge variant={form.status === 'published' ? 'default' : 'secondary'}>
                {form.status === 'published' ? '已发布' : '草稿'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* SEO 分析 */}
        <SEOAnalyzer article={form} />

        {/* AI 助手 */}
        <AIAssistant
          title={form.title}
          content={form.content}
          onSummaryGenerated={handleAISummaryGenerated}
          onTagsRecommended={handleAITagsRecommended}
        />
      </div>
    </div>
  )
}

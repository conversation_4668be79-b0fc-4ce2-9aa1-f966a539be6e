@echo off
setlocal enabledelayedexpansion

REM 设置 Cloudflare Workers 环境变量脚本
REM 使用方法: setup-secrets.bat [dev|prod]

set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=dev

if not "%ENVIRONMENT%"=="dev" if not "%ENVIRONMENT%"=="prod" (
    echo 错误: 环境参数必须是 'dev' 或 'prod'
    echo 使用方法: %0 [dev^|prod]
    exit /b 1
)

echo 🔐 设置 %ENVIRONMENT% 环境的 secrets

REM 检查 wrangler
where wrangler >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: wrangler CLI 未安装
    echo 请运行: npm install -g wrangler
    exit /b 1
)

REM 检查登录状态
wrangler whoami >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未登录 Cloudflare
    echo 请运行: wrangler login
    exit /b 1
)

echo ✓ Cloudflare 认证通过
echo.

REM 设置 GitHub Client Secret
echo 设置 GitHub Client Secret...
echo 提供的 GitHub Client Secret: e52ad4d7a6a07a326666f2a3cd9e29ec6997c363
set /p use_provided="使用提供的 Client Secret? (y/N): "
if /i "%use_provided%"=="y" (
    set GITHUB_SECRET=e52ad4d7a6a07a326666f2a3cd9e29ec6997c363
) else (
    set /p GITHUB_SECRET="请输入 GitHub Client Secret: "
)

if "%GITHUB_SECRET%"=="" (
    echo 错误: GitHub Client Secret 不能为空
    exit /b 1
)

echo %GITHUB_SECRET% | wrangler secret put GITHUB_CLIENT_SECRET --env %ENVIRONMENT%
if %errorlevel% neq 0 (
    echo 错误: 设置 GITHUB_CLIENT_SECRET 失败
    exit /b 1
)
echo ✓ GITHUB_CLIENT_SECRET 设置成功

REM 设置 JWT Secret
echo.
echo 设置 JWT Secret...
echo 建议使用强随机密钥，至少 32 个字符
set /p JWT_SECRET="请输入 JWT Secret (留空自动生成): "

if "%JWT_SECRET%"=="" (
    REM 生成随机 JWT Secret
    set JWT_SECRET=modern-blog-jwt-secret-%RANDOM%-%RANDOM%-%RANDOM%
    echo 自动生成的 JWT Secret: !JWT_SECRET!
)

echo %JWT_SECRET% | wrangler secret put JWT_SECRET --env %ENVIRONMENT%
if %errorlevel% neq 0 (
    echo 错误: 设置 JWT_SECRET 失败
    exit /b 1
)
echo ✓ JWT_SECRET 设置成功

REM 设置管理员邮箱
echo.
echo 设置管理员邮箱列表...
echo 格式: ["<EMAIL>", "<EMAIL>"]
set /p ADMIN_EMAILS="请输入管理员邮箱列表 (JSON 格式): "

if "%ADMIN_EMAILS%"=="" (
    set ADMIN_EMAILS=["<EMAIL>"]
    echo 使用默认管理员邮箱: !ADMIN_EMAILS!
)

echo %ADMIN_EMAILS% | wrangler secret put ADMIN_EMAILS --env %ENVIRONMENT%
if %errorlevel% neq 0 (
    echo 错误: 设置 ADMIN_EMAILS 失败
    exit /b 1
)
echo ✓ ADMIN_EMAILS 设置成功

echo.
echo 🎉 所有 secrets 设置完成!
echo.
echo 已设置的 secrets:
echo - GITHUB_CLIENT_SECRET: ✓
echo - JWT_SECRET: ✓
echo - ADMIN_EMAILS: ✓
echo.
echo 环境: %ENVIRONMENT%
echo.
echo 下一步:
echo 1. 创建 Cloudflare 资源 (运行 setup-resources.bat)
echo 2. 部署 Worker (运行 deploy.bat %ENVIRONMENT%)

endlocal

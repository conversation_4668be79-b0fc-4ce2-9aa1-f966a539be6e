module.exports = {

"[project]/node_modules/prismjs/components/prism-typescript.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
(function(Prism1) {
    Prism1.languages.typescript = Prism1.languages.extend('javascript', {
        'class-name': {
            pattern: /(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,
            lookbehind: true,
            greedy: true,
            inside: null // see below
        },
        'builtin': /\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/
    });
    // The keywords TypeScript adds to JavaScript
    Prism1.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/, // keywords that have to be followed by an identifier
    /\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/, // This is for `import type *, {}`
    /\btype\b(?=\s*(?:[\{*]|$))/);
    // doesn't work with TS because TS is too complex
    delete Prism1.languages.typescript['parameter'];
    delete Prism1.languages.typescript['literal-property'];
    // a version of typescript specifically for highlighting types
    var typeInside = Prism1.languages.extend('typescript', {});
    delete typeInside['class-name'];
    Prism1.languages.typescript['class-name'].inside = typeInside;
    Prism1.languages.insertBefore('typescript', 'function', {
        'decorator': {
            pattern: /@[$\w\xA0-\uFFFF]+/,
            inside: {
                'at': {
                    pattern: /^@/,
                    alias: 'operator'
                },
                'function': /^[\s\S]+/
            }
        },
        'generic-function': {
            // e.g. foo<T extends "bar" | "baz">( ...
            pattern: /#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,
            greedy: true,
            inside: {
                'function': /^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,
                'generic': {
                    pattern: /<[\s\S]+/,
                    alias: 'class-name',
                    inside: typeInside
                }
            }
        }
    });
    Prism1.languages.ts = Prism1.languages.typescript;
})(Prism);
}}),

};

//# sourceMappingURL=node_modules_prismjs_components_prism-typescript_9a631141.js.map
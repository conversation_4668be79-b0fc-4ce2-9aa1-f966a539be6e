"use client"

import { ReactNode } from "react"
import { Head<PERSON> } from "./header"
import { Footer } from "./footer"
import { BottomNav } from "./bottom-nav"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLinks } from "@/components/accessibility/skip-links"
import { KeyboardShortcuts } from "@/components/accessibility/keyboard-shortcuts"

interface MainLayoutProps {
  children: ReactNode
  showBottomNav?: boolean
  enableTransition?: boolean
}

export function MainLayout({
  children,
  showBottomNav = true,
  enableTransition = true
}: MainLayoutProps) {
  const content = (
    <div className="min-h-screen flex flex-col">
      <SkipLinks />
      <Header />
      <main id="main-content" className="flex-1 container mx-auto px-4 py-8 pb-20 md:pb-8" tabIndex={-1}>
        {children}
      </main>
      <Footer />
      {showBottomNav && <BottomNav />}
      <KeyboardShortcuts />
    </div>
  )

  if (enableTransition) {
    return <PageTransition>{content}</PageTransition>
  }

  return content
}

'use client'

import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  FileText, 
  Users, 
  Eye, 
  Image, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  Globe,
  MessageCircle,
  Heart
} from 'lucide-react'

interface DashboardStatsData {
  articles: {
    total: number
    published: number
    draft: number
    recent: number
  }
  users: {
    total: number
    admins: number
    collaborators: number
    recent: number
  }
  views: {
    total_views: number
    unique_visitors: number
    viewed_articles: number
    recent_views: number
  }
  files: {
    total: number
    total_size: number
    images: number
    recent: number
  }
  view_trend: Array<{
    date: string
    views: number
    unique_visitors: number
  }>
  popular_articles: Array<{
    id: string
    title: string
    slug: string
    views: number
  }>
}

export function DashboardStats() {
  const t = useTranslations('dashboard.stats')
  const [data, setData] = useState<DashboardStatsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadStats = async () => {
      setLoading(true)
      try {
        // 这里应该调用实际的 API
        const mockData: DashboardStatsData = {
          articles: {
            total: 156,
            published: 142,
            draft: 14,
            recent: 8,
          },
          users: {
            total: 1247,
            admins: 2,
            collaborators: 5,
            recent: 23,
          },
          views: {
            total_views: 45678,
            unique_visitors: 12543,
            viewed_articles: 134,
            recent_views: 3456,
          },
          files: {
            total: 234,
            total_size: 125678900, // bytes
            images: 198,
            recent: 12,
          },
          view_trend: [
            { date: '2024-01-01', views: 1200, unique_visitors: 800 },
            { date: '2024-01-02', views: 1350, unique_visitors: 900 },
            { date: '2024-01-03', views: 1100, unique_visitors: 750 },
            { date: '2024-01-04', views: 1450, unique_visitors: 950 },
            { date: '2024-01-05', views: 1600, unique_visitors: 1100 },
            { date: '2024-01-06', views: 1300, unique_visitors: 850 },
            { date: '2024-01-07', views: 1500, unique_visitors: 1000 },
          ],
          popular_articles: [
            { id: '1', title: 'Next.js 13 新特性详解', slug: 'nextjs-13-features', views: 2543 },
            { id: '2', title: 'React 18 并发特性', slug: 'react-18-concurrent', views: 2156 },
            { id: '3', title: 'TypeScript 5.0 更新', slug: 'typescript-5-updates', views: 1987 },
            { id: '4', title: 'Tailwind CSS 最佳实践', slug: 'tailwind-best-practices', views: 1765 },
            { id: '5', title: 'Cloudflare Workers 入门', slug: 'cloudflare-workers-intro', views: 1543 },
          ],
        }
        setData(mockData)
      } catch (error) {
        console.error('Failed to load dashboard stats:', error)
      } finally {
        setLoading(false)
      }
    }

    loadStats()
  }, [])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-8 bg-muted rounded mb-4"></div>
              <div className="h-3 bg-muted rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t('noData')}</p>
      </div>
    )
  }

  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const calculateTrend = (current: number, previous: number): { value: number; isPositive: boolean } => {
    if (previous === 0) return { value: 0, isPositive: true }
    const trend = ((current - previous) / previous) * 100
    return { value: Math.abs(trend), isPositive: trend >= 0 }
  }

  // 计算趋势（简化版，实际应该比较不同时间段）
  const viewsTrend = calculateTrend(data.views.recent_views, 2800)
  const articlesTrend = calculateTrend(data.articles.recent, 5)
  const usersTrend = calculateTrend(data.users.recent, 18)
  const filesTrend = calculateTrend(data.files.recent, 8)

  return (
    <div className="space-y-6">
      {/* 主要统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={t('articles')}
          value={data.articles.total}
          subtitle={`${data.articles.published} ${t('published')}, ${data.articles.draft} ${t('draft')}`}
          icon={<FileText className="h-4 w-4" />}
          trend={articlesTrend}
          color="blue"
        />
        
        <StatCard
          title={t('totalViews')}
          value={data.views.total_views}
          subtitle={`${data.views.unique_visitors.toLocaleString()} ${t('uniqueVisitors')}`}
          icon={<Eye className="h-4 w-4" />}
          trend={viewsTrend}
          color="green"
        />
        
        <StatCard
          title={t('users')}
          value={data.users.total}
          subtitle={`${data.users.admins} ${t('admins')}, ${data.users.collaborators} ${t('collaborators')}`}
          icon={<Users className="h-4 w-4" />}
          trend={usersTrend}
          color="purple"
        />
        
        <StatCard
          title={t('files')}
          value={data.files.total}
          subtitle={`${formatFileSize(data.files.total_size)}, ${data.files.images} ${t('images')}`}
          icon={<Image className="h-4 w-4" />}
          trend={filesTrend}
          color="orange"
        />
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 内容统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t('contentStats')}
            </CardTitle>
            <CardDescription>{t('contentStatsDesc')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{t('publishedArticles')}</span>
                <span>{data.articles.published}/{data.articles.total}</span>
              </div>
              <Progress 
                value={(data.articles.published / data.articles.total) * 100} 
                className="h-2"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4 pt-2">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <p className="text-2xl font-bold text-green-600">{data.articles.published}</p>
                <p className="text-sm text-muted-foreground">{t('published')}</p>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <p className="text-2xl font-bold text-yellow-600">{data.articles.draft}</p>
                <p className="text-sm text-muted-foreground">{t('draft')}</p>
              </div>
            </div>
            
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('recentArticles')}</span>
                <Badge variant="secondary">{data.articles.recent}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 访问统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              {t('viewStats')}
            </CardTitle>
            <CardDescription>{t('viewStatsDesc')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <p className="text-2xl font-bold text-blue-600">{data.views.total_views.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">{t('totalViews')}</p>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <p className="text-2xl font-bold text-purple-600">{data.views.unique_visitors.toLocaleString()}</p>
                <p className="text-sm text-muted-foreground">{t('uniqueVisitors')}</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{t('articlesViewed')}</span>
                <span>{data.views.viewed_articles}/{data.articles.published}</span>
              </div>
              <Progress 
                value={(data.views.viewed_articles / data.articles.published) * 100} 
                className="h-2"
              />
            </div>
            
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('recentViews')}</span>
                <Badge variant="secondary">{data.views.recent_views.toLocaleString()}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 热门文章 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {t('popularArticles')}
          </CardTitle>
          <CardDescription>{t('popularArticlesDesc')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.popular_articles.map((article, index) => (
              <div key={article.id} className="flex items-center justify-between p-3 rounded-lg border">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium line-clamp-1">{article.title}</p>
                    <p className="text-sm text-muted-foreground">{article.slug}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{article.views.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">{t('views')}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function StatCard({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = 'blue',
}: {
  title: string
  value: number
  subtitle: string
  icon: React.ReactNode
  trend: { value: number; isPositive: boolean }
  color?: 'blue' | 'green' | 'purple' | 'orange'
}) {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
    green: 'text-green-600 bg-green-100 dark:bg-green-900/20',
    purple: 'text-purple-600 bg-purple-100 dark:bg-purple-900/20',
    orange: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
            {icon}
          </div>
          <div className="flex items-center gap-1">
            {trend.isPositive ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
            <span className={`text-sm font-medium ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {trend.value.toFixed(1)}%
            </span>
          </div>
        </div>
        
        <div>
          <p className="text-sm font-medium text-muted-foreground mb-1">{title}</p>
          <p className="text-2xl font-bold mb-1">{value.toLocaleString()}</p>
          <p className="text-sm text-muted-foreground">{subtitle}</p>
        </div>
      </CardContent>
    </Card>
  )
}

# 现代化博客系统 - 部署指南

本文档提供了现代化博客系统的完整部署指南，包括开发环境、测试环境和生产环境的配置。

## 📋 目录

- [系统要求](#系统要求)
- [环境准备](#环境准备)
- [Cloudflare 配置](#cloudflare-配置)
- [本地开发环境](#本地开发环境)
- [测试环境部署](#测试环境部署)
- [生产环境部署](#生产环境部署)
- [CI/CD 配置](#cicd-配置)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 🔧 系统要求

### 开发环境
- Node.js 18+ 
- pnpm 8+
- Git
- VS Code (推荐)

### 云服务要求
- Cloudflare 账户
- GitHub 账户 (用于 OAuth 和 CI/CD)
- 域名 (可选，用于生产环境)

## 🚀 环境准备

### 1. 克隆项目

```bash
git clone https://github.com/your-username/modern-blog-system.git
cd modern-blog-system
```

### 2. 安装依赖

```bash
# 安装前端依赖
pnpm install

# 安装 Workers 依赖
cd workers
pnpm install
cd ..
```

### 3. 安装 Wrangler CLI

```bash
npm install -g wrangler
```

## ☁️ Cloudflare 配置

### 1. 登录 Cloudflare

```bash
wrangler login
```

### 2. 创建必要的资源

运行自动化设置脚本：

```bash
chmod +x scripts/setup-secrets.sh
./scripts/setup-secrets.sh development
```

或手动创建：

#### D1 数据库

```bash
# 开发环境
wrangler d1 create modern-blog-db-dev

# 测试环境
wrangler d1 create modern-blog-db-staging

# 生产环境
wrangler d1 create modern-blog-db-prod
```

#### R2 存储桶

```bash
# 开发环境
wrangler r2 bucket create modern-blog-storage-dev

# 测试环境
wrangler r2 bucket create modern-blog-storage-staging

# 生产环境
wrangler r2 bucket create modern-blog-storage-prod
```

#### KV 命名空间

```bash
# 缓存命名空间
wrangler kv:namespace create "modern-blog-cache-dev"
wrangler kv:namespace create "modern-blog-cache-staging"
wrangler kv:namespace create "modern-blog-cache-prod"

# 会话命名空间
wrangler kv:namespace create "modern-blog-session-dev"
wrangler kv:namespace create "modern-blog-session-staging"
wrangler kv:namespace create "modern-blog-session-prod"

# 速率限制命名空间
wrangler kv:namespace create "modern-blog-rate-limit-dev"
wrangler kv:namespace create "modern-blog-rate-limit-staging"
wrangler kv:namespace create "modern-blog-rate-limit-prod"
```

### 3. 更新 wrangler.toml

将创建的资源 ID 更新到 `workers/wrangler.toml` 文件中对应的环境配置。

### 4. 设置环境变量

```bash
# JWT 密钥
echo "your-super-secret-jwt-key" | wrangler secret put JWT_SECRET --env development

# GitHub OAuth 密钥
echo "your-github-client-secret" | wrangler secret put GITHUB_CLIENT_SECRET --env development

# 管理员邮箱
echo "<EMAIL>" | wrangler secret put ADMIN_EMAILS --env development
```

## 💻 本地开发环境

### 1. 配置环境变量

复制环境变量模板：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，填入必要的配置：

```env
# 基础配置
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8787

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# 管理员配置
ADMIN_EMAILS=<EMAIL>
```

### 2. 运行数据库迁移

```bash
cd workers
wrangler d1 migrations apply modern-blog-db-dev --local
cd ..
```

### 3. 启动开发服务器

```bash
# 启动前端开发服务器
pnpm dev

# 在新终端启动 Workers 开发服务器
cd workers
pnpm dev
```

访问 http://localhost:3000 查看应用。

## 🧪 测试环境部署

### 1. 运行数据库迁移

```bash
cd workers
wrangler d1 migrations apply modern-blog-db-staging --env staging
cd ..
```

### 2. 部署 Workers

```bash
cd workers
wrangler deploy --env staging
cd ..
```

### 3. 部署前端

```bash
# 构建前端
pnpm build

# 部署到 Vercel/Netlify 或其他平台
# 具体命令取决于你选择的平台
```

## 🚀 生产环境部署

### 1. 环境变量配置

设置生产环境的所有必要环境变量：

```bash
# 使用脚本批量设置
GITHUB_CLIENT_SECRET=your-secret \
ADMIN_EMAILS=<EMAIL> \
./scripts/setup-secrets.sh production
```

### 2. 数据库迁移

```bash
cd workers
wrangler d1 migrations apply modern-blog-db-prod --env production
cd ..
```

### 3. 部署

```bash
# 部署 Workers
cd workers
wrangler deploy --env production
cd ..

# 部署前端
pnpm build
# 部署到你的生产环境
```

### 4. 域名配置

在 Cloudflare Dashboard 中：

1. 添加你的域名
2. 配置 DNS 记录
3. 设置 Workers 路由

## 🔄 CI/CD 配置

### 1. GitHub Secrets

在 GitHub 仓库设置中添加以下 Secrets：

```
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
GITHUB_CLIENT_SECRET=your-github-oauth-secret
ADMIN_EMAILS=<EMAIL>
```

### 2. 自动部署

推送到不同分支会触发不同环境的部署：

- `develop` → 开发环境
- `staging` → 测试环境  
- `main` → 生产环境

### 3. 部署流程

1. 代码质量检查 (ESLint, TypeScript)
2. 安全扫描
3. 构建测试
4. 数据库迁移
5. 应用部署
6. 健康检查
7. 通知

## 📊 监控和维护

### 1. 健康检查

访问以下端点检查系统状态：

```
GET /health                    # 基础健康检查
GET /api/system/status         # 详细系统状态
GET /api/monitoring/metrics    # 性能指标
```

### 2. 日志监控

- Cloudflare Workers 日志
- 应用程序日志
- 错误监控 (Sentry)

### 3. 性能监控

- 响应时间监控
- 数据库查询性能
- 缓存命中率
- 错误率统计

### 4. 定期维护

```bash
# 数据库清理
curl -X POST https://api.yourdomain.com/api/monitoring/cleanup

# 备份创建
curl -X POST https://api.yourdomain.com/api/system/backup

# 指标导出
curl https://api.yourdomain.com/api/monitoring/export?format=csv
```

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败

```bash
# 检查数据库状态
wrangler d1 info modern-blog-db-prod

# 重新运行迁移
wrangler d1 migrations apply modern-blog-db-prod --env production
```

#### 2. 环境变量未设置

```bash
# 列出所有 secrets
wrangler secret list --env production

# 重新设置 secret
echo "new-value" | wrangler secret put SECRET_NAME --env production
```

#### 3. 部署失败

```bash
# 检查 wrangler 配置
wrangler whoami
wrangler deploy --dry-run --env production

# 查看详细错误
wrangler tail --env production
```

#### 4. 性能问题

```bash
# 查看性能指标
curl https://api.yourdomain.com/api/monitoring/metrics

# 查看慢查询
curl https://api.yourdomain.com/api/monitoring/stats
```

### 日志查看

```bash
# 实时日志
wrangler tail --env production

# 特定时间范围的日志
wrangler tail --env production --since 2024-01-01T00:00:00Z
```

### 回滚部署

```bash
# 查看部署历史
wrangler deployments list --env production

# 回滚到特定版本
wrangler rollback --env production --version-id <version-id>
```

## 📚 相关文档

- [API 文档](./API.md)
- [开发指南](./DEVELOPMENT.md)
- [配置参考](./CONFIGURATION.md)
- [故障排除](./TROUBLESHOOTING.md)

## 🆘 获取帮助

如果遇到问题：

1. 查看 [故障排除文档](./TROUBLESHOOTING.md)
2. 检查 [GitHub Issues](https://github.com/your-username/modern-blog-system/issues)
3. 提交新的 Issue
4. 联系维护团队

---

**注意**: 请确保在生产环境中使用强密码和安全的配置。定期更新依赖和监控系统安全。

#!/bin/bash

# Modern Blog Cloudflare Workers 部署脚本
# 使用方法: ./deploy.sh [dev|prod]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_success() {
    print_message "$1" "$GREEN"
}

print_error() {
    print_message "$1" "$RED"
}

print_warning() {
    print_message "$1" "$YELLOW"
}

print_info() {
    print_message "$1" "$BLUE"
}

# 检查参数
ENVIRONMENT=${1:-dev}

if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    print_error "错误: 环境参数必须是 'dev' 或 'prod'"
    echo "使用方法: $0 [dev|prod]"
    exit 1
fi

print_info "开始部署到 $ENVIRONMENT 环境..."

# 检查必要的工具
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v wrangler &> /dev/null; then
        print_error "错误: wrangler CLI 未安装"
        print_info "请运行: npm install -g wrangler"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        print_error "错误: Node.js 未安装"
        exit 1
    fi
    
    print_success "✓ 依赖检查通过"
}

# 检查登录状态
check_auth() {
    print_info "检查 Cloudflare 认证状态..."
    
    if ! wrangler whoami &> /dev/null; then
        print_error "错误: 未登录 Cloudflare"
        print_info "请运行: wrangler login"
        exit 1
    fi
    
    print_success "✓ Cloudflare 认证通过"
}

# 安装依赖
install_dependencies() {
    print_info "安装 npm 依赖..."
    npm install
    print_success "✓ 依赖安装完成"
}

# 类型检查
type_check() {
    print_info "执行 TypeScript 类型检查..."
    npm run type-check
    print_success "✓ 类型检查通过"
}

# 检查环境变量
check_secrets() {
    print_info "检查环境变量..."
    
    # 检查必要的 secrets
    REQUIRED_SECRETS=("GITHUB_CLIENT_SECRET" "JWT_SECRET" "ADMIN_EMAILS")
    
    for secret in "${REQUIRED_SECRETS[@]}"; do
        print_info "检查 secret: $secret"
        # 注意: wrangler secret list 可能不会显示具体值，这里只是提醒
    done
    
    print_warning "请确保已设置所有必要的 secrets:"
    print_warning "- GITHUB_CLIENT_SECRET"
    print_warning "- JWT_SECRET" 
    print_warning "- ADMIN_EMAILS"
    
    read -p "是否已设置所有必要的 secrets? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "请先设置必要的 secrets"
        print_info "示例:"
        print_info "wrangler secret put GITHUB_CLIENT_SECRET --env $ENVIRONMENT"
        print_info "wrangler secret put JWT_SECRET --env $ENVIRONMENT"
        print_info "wrangler secret put ADMIN_EMAILS --env $ENVIRONMENT"
        exit 1
    fi
}

# 检查数据库
check_database() {
    print_info "检查数据库状态..."
    
    # 这里可以添加数据库连接检查
    print_warning "请确保 D1 数据库已创建并执行了 schema.sql"
    
    read -p "是否已设置数据库? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "请先设置数据库"
        print_info "运行以下命令:"
        print_info "wrangler d1 create modern-blog-db"
        print_info "wrangler d1 execute modern-blog-db --file=./schema.sql --env $ENVIRONMENT"
        exit 1
    fi
}

# 检查 KV 和 R2
check_resources() {
    print_info "检查 Cloudflare 资源..."
    
    print_warning "请确保已创建以下资源:"
    print_warning "- KV 命名空间: CACHE, SESSIONS"
    print_warning "- R2 存储桶: modern-blog-storage"
    print_warning "- D1 数据库: modern-blog-db"
    
    read -p "是否已创建所有资源? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "请先创建必要的资源"
        print_info "运行以下命令:"
        print_info "wrangler kv:namespace create CACHE"
        print_info "wrangler kv:namespace create SESSIONS"
        print_info "wrangler r2 bucket create modern-blog-storage"
        exit 1
    fi
}

# 部署
deploy() {
    print_info "开始部署到 $ENVIRONMENT 环境..."
    
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        wrangler deploy --env production
    else
        wrangler deploy --env development
    fi
    
    print_success "✓ 部署完成!"
}

# 部署后测试
post_deploy_test() {
    print_info "执行部署后测试..."
    
    # 获取 Worker URL
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        WORKER_URL="https://modern-blog-api.your-account.workers.dev"
    else
        WORKER_URL="https://modern-blog-api-dev.your-account.workers.dev"
    fi
    
    print_info "测试健康检查端点..."
    
    # 测试健康检查
    if curl -s -f "$WORKER_URL/api/health" > /dev/null; then
        print_success "✓ 健康检查通过"
    else
        print_warning "⚠ 健康检查失败，请检查部署状态"
    fi
}

# 显示部署信息
show_deployment_info() {
    print_success "🎉 部署完成!"
    print_info "环境: $ENVIRONMENT"
    
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        print_info "Worker URL: https://modern-blog-api.your-account.workers.dev"
    else
        print_info "Worker URL: https://modern-blog-api-dev.your-account.workers.dev"
    fi
    
    print_info "API 端点:"
    print_info "- 健康检查: /api/health"
    print_info "- GitHub OAuth: /api/auth/github/url"
    print_info "- 文件上传: /api/files/upload"
    print_info "- AI 摘要: /api/ai/summary"
    
    print_warning "下一步:"
    print_warning "1. 更新前端的 API_BASE_URL"
    print_warning "2. 测试 GitHub OAuth 流程"
    print_warning "3. 验证文件上传功能"
}

# 主函数
main() {
    print_info "🚀 Modern Blog Cloudflare Workers 部署脚本"
    print_info "环境: $ENVIRONMENT"
    echo
    
    check_dependencies
    check_auth
    install_dependencies
    type_check
    check_secrets
    check_database
    check_resources
    
    echo
    print_warning "准备部署到 $ENVIRONMENT 环境"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        deploy
        post_deploy_test
        show_deployment_info
    else
        print_info "部署已取消"
        exit 0
    fi
}

# 执行主函数
main

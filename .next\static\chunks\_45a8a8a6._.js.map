{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/page-transition.tsx"], "sourcesContent": ["\"use client\"\n\nimport { ReactNode, useEffect, useState } from \"react\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\n\ninterface PageTransitionProps {\n  children: ReactNode\n  className?: string\n}\n\nexport function PageTransition({ children, className }: PageTransitionProps) {\n  const pathname = usePathname()\n  const [isLoading, setIsLoading] = useState(false)\n\n  useEffect(() => {\n    setIsLoading(true)\n    const timer = setTimeout(() => {\n      setIsLoading(false)\n    }, 150)\n\n    return () => clearTimeout(timer)\n  }, [pathname])\n\n  return (\n    <div className={cn(\"relative\", className)}>\n      {/* 页面过渡遮罩 */}\n      <div\n        className={cn(\n          \"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm transition-all duration-300\",\n          isLoading ? \"opacity-100\" : \"opacity-0 pointer-events-none\"\n        )}\n      >\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n            <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n            <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\"></div>\n          </div>\n        </div>\n      </div>\n\n      {/* 页面内容 */}\n      <div\n        className={cn(\n          \"transition-all duration-300\",\n          isLoading ? \"opacity-50 scale-95\" : \"opacity-100 scale-100\"\n        )}\n      >\n        {children}\n      </div>\n    </div>\n  )\n}\n\n// 简单的淡入动画组件\nexport function FadeIn({ \n  children, \n  delay = 0, \n  duration = 500,\n  className \n}: {\n  children: ReactNode\n  delay?: number\n  duration?: number\n  className?: string\n}) {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(true)\n    }, delay)\n\n    return () => clearTimeout(timer)\n  }, [delay])\n\n  return (\n    <div\n      className={cn(\n        \"transition-all ease-out\",\n        isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\",\n        className\n      )}\n      style={{\n        transitionDuration: `${duration}ms`,\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\n// 滑入动画组件\nexport function SlideIn({\n  children,\n  direction = \"up\",\n  delay = 0,\n  duration = 500,\n  className\n}: {\n  children: ReactNode\n  direction?: \"up\" | \"down\" | \"left\" | \"right\"\n  delay?: number\n  duration?: number\n  className?: string\n}) {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(true)\n    }, delay)\n\n    return () => clearTimeout(timer)\n  }, [delay])\n\n  const getTransform = () => {\n    if (isVisible) return \"translate-x-0 translate-y-0\"\n    \n    switch (direction) {\n      case \"up\":\n        return \"translate-y-8\"\n      case \"down\":\n        return \"-translate-y-8\"\n      case \"left\":\n        return \"translate-x-8\"\n      case \"right\":\n        return \"-translate-x-8\"\n      default:\n        return \"translate-y-8\"\n    }\n  }\n\n  return (\n    <div\n      className={cn(\n        \"transition-all ease-out\",\n        isVisible ? \"opacity-100\" : \"opacity-0\",\n        getTransform(),\n        className\n      )}\n      style={{\n        transitionDuration: `${duration}ms`,\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAuB;;IACzE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;YACb,MAAM,QAAQ;kDAAW;oBACvB,aAAa;gBACf;iDAAG;YAEH;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAE7B,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA,YAAY,gBAAgB;0BAG9B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+BACA,YAAY,wBAAwB;0BAGrC;;;;;;;;;;;;AAIT;GA1CgB;;QACG,qIAAA,CAAA,cAAW;;;KADd;AA6CT,SAAS,OAAO,EACrB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,SAAS,EAMV;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,QAAQ;0CAAW;oBACvB,aAAa;gBACf;yCAAG;YAEH;oCAAO,IAAM,aAAa;;QAC5B;2BAAG;QAAC;KAAM;IAEV,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2BACA,YAAY,8BAA8B,2BAC1C;QAEF,OAAO;YACL,oBAAoB,GAAG,SAAS,EAAE,CAAC;QACrC;kBAEC;;;;;;AAGP;IAnCgB;MAAA;AAsCT,SAAS,QAAQ,EACtB,QAAQ,EACR,YAAY,IAAI,EAChB,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,SAAS,EAOV;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,QAAQ;2CAAW;oBACvB,aAAa;gBACf;0CAAG;YAEH;qCAAO,IAAM,aAAa;;QAC5B;4BAAG;QAAC;KAAM;IAEV,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO;QAEtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2BACA,YAAY,gBAAgB,aAC5B,gBACA;QAEF,OAAO;YACL,oBAAoB,GAAG,SAAS,EAAE,CAAC;QACrC;kBAEC;;;;;;AAGP;IAvDgB;MAAA", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}
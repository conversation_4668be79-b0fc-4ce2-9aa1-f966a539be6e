import { NextResponse } from "next/server"
import { getArticles, mockCategories, mockTags } from "@/lib/mock-data"
import { siteConfig } from "@/config/site"

/**
 * 生成 XML 网站地图
 */
export async function GET() {
  // 获取所有已发布的文章
  const { articles } = getArticles({ status: "published" })
  
  // 获取所有分类和标签
  const categories = mockCategories
  const tags = mockTags
  
  // 创建网站地图 XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
  <!-- 主页 -->
  <url>
    <loc>${siteConfig.url}</loc>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
    <lastmod>${new Date().toISOString()}</lastmod>
  </url>
  
  <!-- 文章列表页 -->
  <url>
    <loc>${siteConfig.url}/articles</loc>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
    <lastmod>${new Date().toISOString()}</lastmod>
  </url>
  
  <!-- 归档页 -->
  <url>
    <loc>${siteConfig.url}/archive</loc>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
    <lastmod>${new Date().toISOString()}</lastmod>
  </url>
  
  <!-- 此刻页 -->
  <url>
    <loc>${siteConfig.url}/now</loc>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
    <lastmod>${new Date().toISOString()}</lastmod>
  </url>
  
  <!-- 留言板 -->
  <url>
    <loc>${siteConfig.url}/guestbook</loc>
    <changefreq>daily</changefreq>
    <priority>0.7</priority>
    <lastmod>${new Date().toISOString()}</lastmod>
  </url>
  
  <!-- 文章详情页 -->
  ${articles.map(article => {
    const lastmod = article.updatedAt.toISOString()
    const publishedAt = article.publishedAt?.toISOString() || article.createdAt.toISOString()
    
    return `
  <url>
    <loc>${siteConfig.url}/articles/${article.slug}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
    <news:news>
      <news:publication>
        <news:name>${siteConfig.name}</news:name>
        <news:language>zh</news:language>
      </news:publication>
      <news:publication_date>${publishedAt}</news:publication_date>
      <news:title>${article.title}</news:title>
    </news:news>
    ${article.coverImage ? `
    <image:image>
      <image:loc>${article.coverImage}</image:loc>
      <image:title>${article.title}</image:title>
      <image:caption>${article.excerpt || ''}</image:caption>
    </image:image>` : ''}
  </url>`
  }).join('')}
  
  <!-- 分类页 -->
  ${categories.map(category => `
  <url>
    <loc>${siteConfig.url}/categories/${category.slug}</loc>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
    <lastmod>${new Date().toISOString()}</lastmod>
  </url>`).join('')}
  
  <!-- 标签页 -->
  ${tags.map(tag => `
  <url>
    <loc>${siteConfig.url}/tags/${tag.slug}</loc>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
    <lastmod>${new Date().toISOString()}</lastmod>
  </url>`).join('')}
</urlset>`

  // 返回 XML 响应
  return new NextResponse(sitemap, {
    headers: {
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=3600, s-maxage=3600",
    },
  })
}

'use client'

import { Toaster as Sonner } from 'sonner'
import { useTheme } from 'next-themes'
import { useTranslations } from 'next-intl'

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',
          description: 'group-[.toast]:text-muted-foreground',
          actionButton:
            'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',
          cancelButton:
            'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',
        },
      }}
      {...props}
    />
  )
}

export { Toaster }

/**
 * Toast 工具函数
 */
import { toast } from 'sonner'

export const showToast = {
  success: (message: string, options?: any) => {
    toast.success(message, {
      duration: 4000,
      ...options,
    })
  },
  
  error: (message: string, options?: any) => {
    toast.error(message, {
      duration: 6000,
      ...options,
    })
  },
  
  warning: (message: string, options?: any) => {
    toast.warning(message, {
      duration: 5000,
      ...options,
    })
  },
  
  info: (message: string, options?: any) => {
    toast.info(message, {
      duration: 4000,
      ...options,
    })
  },
  
  loading: (message: string, options?: any) => {
    return toast.loading(message, {
      duration: Infinity,
      ...options,
    })
  },
  
  promise: function<T>(
    promise: Promise<T>,
    messages: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    },
    options?: any
  ): string | number {
    return toast.promise(promise, messages, {
      duration: 4000,
      ...options,
    })
  },
  
  dismiss: (toastId?: string | number) => {
    toast.dismiss(toastId)
  },
  
  custom: (jsx: React.ReactNode, options?: any) => {
    return toast.custom(jsx, options)
  },
}

/**
 * 多语言 Toast Hook
 */
export function useToast() {
  const t = useTranslations('toast')

  return {
    success: (key: string, values?: any) => showToast.success(t(key, values)),
    error: (key: string, values?: any) => showToast.error(t(key, values)),
    warning: (key: string, values?: any) => showToast.warning(t(key, values)),
    info: (key: string, values?: any) => showToast.info(t(key, values)),
    loading: (key: string, values?: any) => showToast.loading(t(key, values)),
    promise: function<T>(
      promise: Promise<T>,
      messages: {
        loading: string
        success: string | ((data: T) => string)
        error: string | ((error: any) => string)
      }
    ): string | number {
      return showToast.promise(promise, {
        loading: t(messages.loading),
        success: typeof messages.success === 'string' ? t(messages.success) : messages.success,
        error: typeof messages.error === 'string' ? t(messages.error) : messages.error,
      })
    },
    dismiss: showToast.dismiss,
    custom: showToast.custom,
  }
}

/**
 * API 请求 Toast Hook
 */
export function useApiToast() {
  const t = useTranslations('api')

  const handleApiCall = async function<T>(
    apiCall: () => Promise<T>,
    options: {
      loadingMessage?: string
      successMessage?: string | ((data: T) => string)
      errorMessage?: string | ((error: any) => string)
      showLoading?: boolean
      showSuccess?: boolean
      showError?: boolean
    } = {}
  ): Promise<T> {
    const {
      loadingMessage = t('loading'),
      successMessage = t('success'),
      errorMessage = t('error'),
      showLoading = true,
      showSuccess = true,
      showError = true,
    } = options

    let loadingToast: string | number | undefined

    try {
      if (showLoading) {
        loadingToast = showToast.loading(loadingMessage)
      }

      const result = await apiCall()

      if (loadingToast) {
        showToast.dismiss(loadingToast)
      }

      if (showSuccess) {
        const message = typeof successMessage === 'function' 
          ? successMessage(result) 
          : successMessage
        showToast.success(message)
      }

      return result
    } catch (error) {
      if (loadingToast) {
        showToast.dismiss(loadingToast)
      }

      if (showError) {
        const message = typeof errorMessage === 'function' 
          ? errorMessage(error) 
          : errorMessage
        showToast.error(message)
      }

      throw error
    }
  }

  return { handleApiCall }
}

/**
 * 表单提交 Toast Hook
 */
export function useFormToast() {
  const t = useTranslations('form')

  const handleFormSubmit = async function<T>(
    submitFn: () => Promise<T>,
    options: {
      successMessage?: string
      errorMessage?: string
      onSuccess?: (data: T) => void
      onError?: (error: any) => void
    } = {}
  ) {
    const {
      successMessage = t('submitSuccess'),
      errorMessage = t('submitError'),
      onSuccess,
      onError,
    } = options

    try {
      const result = await submitFn()
      showToast.success(successMessage)
      onSuccess?.(result)
      return result
    } catch (error) {
      const message = error instanceof Error ? error.message : errorMessage
      showToast.error(message)
      onError?.(error)
      throw error
    }
  }

  return { handleFormSubmit }
}

/**
 * 文件上传 Toast Hook
 */
export function useFileUploadToast() {
  const t = useTranslations('fileUpload')

  const handleFileUpload = async (
    uploadFn: (onProgress?: (progress: number) => void) => Promise<any>,
    options: {
      successMessage?: string
      errorMessage?: string
      showProgress?: boolean
    } = {}
  ) => {
    const {
      successMessage = t('uploadSuccess'),
      errorMessage = t('uploadError'),
      showProgress = true,
    } = options

    let progressToast: string | number | undefined

    try {
      if (showProgress) {
        progressToast = showToast.loading(t('uploading'))
      }

      const result = await uploadFn((progress) => {
        if (progressToast && showProgress) {
          showToast.dismiss(progressToast)
          progressToast = showToast.loading(`${t('uploading')} ${Math.round(progress)}%`)
        }
      })

      if (progressToast) {
        showToast.dismiss(progressToast)
      }

      showToast.success(successMessage)
      return result
    } catch (error) {
      if (progressToast) {
        showToast.dismiss(progressToast)
      }

      const message = error instanceof Error ? error.message : errorMessage
      showToast.error(message)
      throw error
    }
  }

  return { handleFileUpload }
}

/**
 * 复制到剪贴板 Toast
 */
export function useCopyToast() {
  const t = useTranslations('copy')

  const copyToClipboard = async (text: string, successMessage?: string) => {
    try {
      await navigator.clipboard.writeText(text)
      showToast.success(successMessage || t('success'))
    } catch (error) {
      showToast.error(t('error'))
      throw error
    }
  }

  return { copyToClipboard }
}

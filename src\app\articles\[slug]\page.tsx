import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { Calendar, Clock, Eye, Heart, ArrowLeft, Share2, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { MainLayout } from "@/components/layout/main-layout"
import { PageContainer } from "@/components/layout/page-container"
import { MarkdownRenderer } from "@/components/ui/markdown-renderer"
import { TableOfContents, ReadingProgress } from "@/components/ui/table-of-contents"
import { ArticleStructuredData, BreadcrumbStructuredData } from "@/components/seo/structured-data"
import { SimpleGiscusComments } from "@/components/comments/giscus-comments"
import { getArticleBySlug } from "@/lib/mock-data"
import { generateArticleMetadata } from "@/lib/seo"
import { formatDate, formatRelativeTime } from "@/lib/utils"
import type { Metadata } from "next"

interface ArticlePageProps {
  params: {
    slug: string
  }
}

// 生成页面元数据
export async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {
  const article = getArticleBySlug(params.slug)

  if (!article) {
    return {
      title: "文章未找到",
    }
  }

  // 使用 SEO 工具生成文章元数据
  return generateArticleMetadata(article)
}

export default function ArticlePage({ params }: ArticlePageProps) {
  const article = getArticleBySlug(params.slug)

  if (!article) {
    notFound()
  }

  const readingTime = Math.ceil(article.content.length / 500)
  const initials = article.author.name
    ?.split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase() || '?'

  // 面包屑导航数据
  const breadcrumbItems = [
    { name: "首页", url: "/" },
    { name: "文章", url: "/articles" },
    { name: article.category, url: `/categories/${article.category}` },
    { name: article.title, url: `/articles/${article.slug}` },
  ]

  return (
    <>
      {/* 结构化数据 */}
      <ArticleStructuredData article={article} />
      <BreadcrumbStructuredData items={breadcrumbItems} />

      <ReadingProgress />
      <MainLayout>
        <PageContainer maxWidth="full">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* 主要内容 */}
            <div className="lg:col-span-3 space-y-8">
              {/* 返回按钮 */}
              <Button asChild variant="ghost" className="mb-4">
                <Link href="/articles">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  返回文章列表
                </Link>
              </Button>

              {/* 文章头部 */}
              <div className="space-y-6">
                {/* 分类和标签 */}
                <div className="flex flex-wrap items-center gap-2">
                  <Badge variant="outline">{article.category}</Badge>
                  {article.tags.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>

                {/* 标题 */}
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
                  {article.title}
                </h1>

                {/* AI 摘要 */}
                {article.summary && (
                  <Card className="border-l-4 border-primary">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
                        <span className="text-sm font-medium text-muted-foreground">AI 智能摘要</span>
                      </div>
                      <p className="text-muted-foreground leading-relaxed">
                        {article.summary}
                      </p>
                    </CardContent>
                  </Card>
                )}

                {/* 作者信息和元数据 */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 py-4 border-y">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={article.author.avatar} />
                      <AvatarFallback>{initials}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">{article.author.name}</span>
                        <Badge variant={article.author.role === 'admin' ? 'destructive' : 'secondary'} className="text-xs">
                          {article.author.role === 'admin' ? '管理员' : article.author.role === 'collaborator' ? '协作者' : '用户'}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {formatDate(article.publishedAt || article.createdAt)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {readingTime} 分钟阅读
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        {article.viewCount}
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        {article.likeCount}
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      <Share2 className="w-4 h-4 mr-2" />
                      分享
                    </Button>
                  </div>
                </div>
              </div>

              {/* 封面图片 */}
              {article.coverImage && (
                <div className="relative aspect-video overflow-hidden rounded-lg">
                  <Image
                    src={article.coverImage}
                    alt={article.title}
                    fill
                    className="object-cover"
                    priority
                  />
                </div>
              )}

              {/* 文章内容 */}
              <div className="prose prose-gray dark:prose-invert max-w-none">
                <MarkdownRenderer content={article.content} />
              </div>

              {/* 文章底部装饰 */}
              <div className="text-center py-8">
                <div className="inline-flex items-center gap-2 text-muted-foreground">
                  <div className="w-8 h-px bg-border"></div>
                  <span className="text-sm">文章结束</span>
                  <div className="w-8 h-px bg-border"></div>
                </div>
              </div>

              {/* 评论区 */}
              <SimpleGiscusComments mapping="specific" term={article.slug} />
            </div>

            {/* 侧边栏 */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-6">
                {/* 目录 */}
                <Card>
                  <CardContent className="pt-6">
                    <TableOfContents content={article.content} />
                  </CardContent>
                </Card>

                {/* 作者信息卡片 */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center space-y-4">
                      <Avatar className="h-16 w-16 mx-auto">
                        <AvatarImage src={article.author.avatar} />
                        <AvatarFallback className="text-lg">{initials}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{article.author.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {article.author.role === 'admin' ? '管理员' : article.author.role === 'collaborator' ? '协作者' : '用户'}
                        </p>
                      </div>
                      <Button asChild variant="outline" size="sm" className="w-full">
                        <Link href={`/authors/${article.author.id}`}>
                          <User className="w-4 h-4 mr-2" />
                          查看更多文章
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </PageContainer>
      </MainLayout>
    </>
  )
}

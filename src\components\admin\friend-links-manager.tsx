'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Plus, Edit, Trash2, MoreHorizontal, Search, ExternalLink, Star, Check, X, Clock } from 'lucide-react'
import Link from 'next/link'

interface FriendLink {
  id: string
  name: string
  url: string
  description?: string
  avatar?: string
  category: string
  status: 'pending' | 'approved' | 'rejected'
  order_index: number
  is_featured: boolean
  contact_email?: string
  created_at: string
  updated_at: string
  approved_at?: string
  created_by?: string
  approved_by?: string
}

export function FriendLinksManager() {
  const t = useTranslations('admin.friendLinks')
  const [links, setLinks] = useState<FriendLink[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [selectedLinks, setSelectedLinks] = useState<string[]>([])
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deletingLink, setDeletingLink] = useState<FriendLink | null>(null)

  // 模拟数据加载
  useEffect(() => {
    const loadLinks = async () => {
      setLoading(true)
      try {
        // 这里应该调用实际的 API
        const mockLinks: FriendLink[] = [
          {
            id: '1',
            name: 'Next.js',
            url: 'https://nextjs.org',
            description: 'React 全栈框架',
            avatar: 'https://nextjs.org/favicon.ico',
            category: 'tech',
            status: 'approved',
            order_index: 1,
            is_featured: true,
            created_at: '2024-01-01',
            updated_at: '2024-01-01',
            approved_at: '2024-01-01',
          },
          {
            id: '2',
            name: 'Vercel',
            url: 'https://vercel.com',
            description: '现代化部署平台',
            category: 'tech',
            status: 'approved',
            order_index: 2,
            is_featured: true,
            created_at: '2024-01-01',
            updated_at: '2024-01-01',
            approved_at: '2024-01-01',
          },
          {
            id: '3',
            name: '待审核博客',
            url: 'https://example.com',
            description: '一个很棒的技术博客',
            category: 'blog',
            status: 'pending',
            order_index: 3,
            is_featured: false,
            contact_email: '<EMAIL>',
            created_at: '2024-01-02',
            updated_at: '2024-01-02',
          },
        ]

        setLinks(mockLinks)
      } catch (error) {
        console.error('Failed to load friend links:', error)
      } finally {
        setLoading(false)
      }
    }

    loadLinks()
  }, [])

  const filteredLinks = links.filter(link => {
    const matchesSearch = link.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         link.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         link.url.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || link.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || link.category === categoryFilter
    
    return matchesSearch && matchesStatus && matchesCategory
  })

  const handleSelectLink = (linkId: string, checked: boolean) => {
    if (checked) {
      setSelectedLinks(prev => [...prev, linkId])
    } else {
      setSelectedLinks(prev => prev.filter(id => id !== linkId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedLinks(filteredLinks.map(link => link.id))
    } else {
      setSelectedLinks([])
    }
  }

  const handleBatchStatusUpdate = async (status: 'approved' | 'rejected') => {
    if (selectedLinks.length === 0) return

    try {
      // 这里应该调用批量更新 API
      setLinks(prev => prev.map(link => 
        selectedLinks.includes(link.id) 
          ? { ...link, status, updated_at: new Date().toISOString() }
          : link
      ))
      setSelectedLinks([])
    } catch (error) {
      console.error('Failed to update links status:', error)
    }
  }

  const handleDelete = async (link: FriendLink) => {
    setDeletingLink(link)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!deletingLink) return

    try {
      // 这里应该调用删除 API
      setLinks(prev => prev.filter(l => l.id !== deletingLink.id))
      setDeleteDialogOpen(false)
      setDeletingLink(null)
    } catch (error) {
      console.error('Failed to delete link:', error)
    }
  }

  const getStatusBadge = (status: FriendLink['status']) => {
    const variants = {
      approved: 'default',
      pending: 'secondary',
      rejected: 'destructive',
    } as const

    const icons = {
      approved: <Check className="h-3 w-3" />,
      pending: <Clock className="h-3 w-3" />,
      rejected: <X className="h-3 w-3" />,
    }

    const labels = {
      approved: t('approved'),
      pending: t('pending'),
      rejected: t('rejected'),
    }

    return (
      <Badge variant={variants[status]} className="gap-1">
        {icons[status]}
        {labels[status]}
      </Badge>
    )
  }

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      friend: t('friend'),
      tech: t('tech'),
      blog: t('blog'),
      other: t('other'),
    }
    return labels[category] || category
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t('loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground">{t('subtitle')}</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/friend-links/new">
            <Plus className="h-4 w-4 mr-2" />
            {t('createLink')}
          </Link>
        </Button>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有状态</SelectItem>
            <SelectItem value="approved">{t('approved')}</SelectItem>
            <SelectItem value="pending">{t('pending')}</SelectItem>
            <SelectItem value="rejected">{t('rejected')}</SelectItem>
          </SelectContent>
        </Select>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有分类</SelectItem>
            <SelectItem value="friend">{t('friend')}</SelectItem>
            <SelectItem value="tech">{t('tech')}</SelectItem>
            <SelectItem value="blog">{t('blog')}</SelectItem>
            <SelectItem value="other">{t('other')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {selectedLinks.length > 0 && (
        <div className="flex items-center gap-2 p-4 bg-muted rounded-lg">
          <span className="text-sm text-muted-foreground">
            已选择 {selectedLinks.length} 个链接
          </span>
          <Button size="sm" onClick={() => handleBatchStatusUpdate('approved')}>
            <Check className="h-4 w-4 mr-2" />
            批量通过
          </Button>
          <Button size="sm" variant="outline" onClick={() => handleBatchStatusUpdate('rejected')}>
            <X className="h-4 w-4 mr-2" />
            批量拒绝
          </Button>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>{t('linkList')}</CardTitle>
          <CardDescription>
            {t('linkListDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedLinks.length === filteredLinks.length && filteredLinks.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>{t('name')}</TableHead>
                <TableHead>{t('category')}</TableHead>
                <TableHead>{t('status')}</TableHead>
                <TableHead>{t('featured')}</TableHead>
                <TableHead>{t('updatedAt')}</TableHead>
                <TableHead className="w-[70px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLinks.map((link) => (
                <TableRow key={link.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedLinks.includes(link.id)}
                      onCheckedChange={(checked: boolean) => handleSelectLink(link.id, checked)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={link.avatar} alt={link.name} />
                        <AvatarFallback>{link.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{link.name}</span>
                          {link.is_featured && <Star className="h-4 w-4 text-yellow-500" />}
                        </div>
                        {link.description && (
                          <div className="text-sm text-muted-foreground">
                            {link.description}
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground">
                          {link.url}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{getCategoryLabel(link.category)}</Badge>
                  </TableCell>
                  <TableCell>{getStatusBadge(link.status)}</TableCell>
                  <TableCell>
                    {link.is_featured ? (
                      <Star className="h-4 w-4 text-yellow-500" />
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(link.updated_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <a href={link.url} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="h-4 w-4 mr-2" />
                            {t('visit')}
                          </a>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/friend-links/edit/${link.id}`}>
                            <Edit className="h-4 w-4 mr-2" />
                            {t('edit')}
                          </Link>
                        </DropdownMenuItem>
                        {link.status === 'pending' && (
                          <>
                            <DropdownMenuItem onClick={() => handleBatchStatusUpdate('approved')}>
                              <Check className="h-4 w-4 mr-2" />
                              {t('approve')}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleBatchStatusUpdate('rejected')}>
                              <X className="h-4 w-4 mr-2" />
                              {t('reject')}
                            </DropdownMenuItem>
                          </>
                        )}
                        <DropdownMenuItem
                          onClick={() => handleDelete(link)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          {t('delete')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredLinks.length === 0 && (
            <div className="text-center py-8">
              <ExternalLink className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">{t('noLinks')}</h3>
              <p className="text-muted-foreground mb-4">{t('noLinksDescription')}</p>
              <Button asChild>
                <Link href="/dashboard/friend-links/new">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('createFirstLink')}
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('deleteLink')}</DialogTitle>
            <DialogDescription>
              {t('deleteLinkConfirm', { name: deletingLink?.name || '' })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              {t('cancel')}
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              {t('delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

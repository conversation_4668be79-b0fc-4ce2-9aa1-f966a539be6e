@echo off
setlocal enabledelayedexpansion

REM Modern Blog Cloudflare Workers 部署脚本 (Windows)
REM 使用方法: deploy.bat [dev|prod]

set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=dev

if not "%ENVIRONMENT%"=="dev" if not "%ENVIRONMENT%"=="prod" (
    echo 错误: 环境参数必须是 'dev' 或 'prod'
    echo 使用方法: %0 [dev^|prod]
    exit /b 1
)

echo 开始部署到 %ENVIRONMENT% 环境...

REM 检查 wrangler
where wrangler >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: wrangler CLI 未安装
    echo 请运行: npm install -g wrangler
    exit /b 1
)

REM 检查 Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: Node.js 未安装
    exit /b 1
)

echo ✓ 依赖检查通过

REM 检查登录状态
wrangler whoami >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未登录 Cloudflare
    echo 请运行: wrangler login
    exit /b 1
)

echo ✓ Cloudflare 认证通过

REM 安装依赖
echo 安装 npm 依赖...
call npm install
if %errorlevel% neq 0 (
    echo 错误: npm install 失败
    exit /b 1
)
echo ✓ 依赖安装完成

REM 类型检查
echo 执行 TypeScript 类型检查...
call npm run type-check
if %errorlevel% neq 0 (
    echo 错误: 类型检查失败
    exit /b 1
)
echo ✓ 类型检查通过

REM 提醒检查环境变量
echo.
echo 请确保已设置所有必要的 secrets:
echo - GITHUB_CLIENT_SECRET
echo - JWT_SECRET
echo - ADMIN_EMAILS
echo.
set /p confirm="是否已设置所有必要的 secrets? (y/N): "
if /i not "%confirm%"=="y" (
    echo 请先设置必要的 secrets
    echo 示例:
    echo wrangler secret put GITHUB_CLIENT_SECRET --env %ENVIRONMENT%
    echo wrangler secret put JWT_SECRET --env %ENVIRONMENT%
    echo wrangler secret put ADMIN_EMAILS --env %ENVIRONMENT%
    exit /b 1
)

REM 提醒检查资源
echo.
echo 请确保已创建以下资源:
echo - KV 命名空间: CACHE, SESSIONS
echo - R2 存储桶: modern-blog-storage
echo - D1 数据库: modern-blog-db
echo.
set /p confirm="是否已创建所有资源? (y/N): "
if /i not "%confirm%"=="y" (
    echo 请先创建必要的资源
    echo 运行以下命令:
    echo wrangler kv:namespace create CACHE
    echo wrangler kv:namespace create SESSIONS
    echo wrangler r2 bucket create modern-blog-storage
    echo wrangler d1 create modern-blog-db
    echo wrangler d1 execute modern-blog-db --file=./schema.sql --env %ENVIRONMENT%
    exit /b 1
)

REM 确认部署
echo.
echo 准备部署到 %ENVIRONMENT% 环境
set /p confirm="是否继续? (y/N): "
if /i not "%confirm%"=="y" (
    echo 部署已取消
    exit /b 0
)

REM 部署
echo 开始部署到 %ENVIRONMENT% 环境...
if "%ENVIRONMENT%"=="prod" (
    call wrangler deploy --env production
) else (
    call wrangler deploy --env development
)

if %errorlevel% neq 0 (
    echo 错误: 部署失败
    exit /b 1
)

echo ✓ 部署完成!

REM 显示部署信息
echo.
echo 🎉 部署完成!
echo 环境: %ENVIRONMENT%
echo.
echo API 端点:
echo - 健康检查: /api/health
echo - GitHub OAuth: /api/auth/github/url
echo - 文件上传: /api/files/upload
echo - AI 摘要: /api/ai/summary
echo.
echo 下一步:
echo 1. 更新前端的 API_BASE_URL
echo 2. 测试 GitHub OAuth 流程
echo 3. 验证文件上传功能

endlocal

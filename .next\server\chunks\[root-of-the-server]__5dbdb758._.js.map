{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Github<PERSON>rovider from \"next-auth/providers/github\"\nimport { User } from \"@/types\"\n\n// 扩展 NextAuth 类型\ndeclare module \"next-auth\" {\n  interface Session {\n    user: User & {\n      id: string\n    }\n  }\n  \n  interface User {\n    role: 'admin' | 'collaborator' | 'user'\n  }\n}\n\ndeclare module \"next-auth/jwt\" {\n  interface JWT {\n    role: 'admin' | 'collaborator' | 'user'\n  }\n}\n\n// 管理员邮箱列表（实际应用中应该从数据库或环境变量获取）\nconst ADMIN_EMAILS = [\n  process.env.ADMIN_EMAIL || \"admin@localhost\",\n  // 可以添加更多管理员邮箱\n]\n\n// 协作者邮箱列表\nconst COLLABORATOR_EMAILS = [\n  // 可以添加协作者邮箱\n]\n\n// 根据邮箱确定用户角色\nfunction getUserRole(email: string): 'admin' | 'collaborator' | 'user' {\n  if (ADMIN_EMAILS.includes(email)) {\n    return 'admin'\n  }\n  if (COLLABORATOR_EMAILS.includes(email)) {\n    return 'collaborator'\n  }\n  return 'user'\n}\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    GithubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n      profile(profile) {\n        return {\n          id: profile.id.toString(),\n          name: profile.name || profile.login,\n          email: profile.email,\n          image: profile.avatar_url,\n          role: getUserRole(profile.email || ''),\n        }\n      },\n    }),\n  ],\n  \n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    \n    async session({ session, token }) {\n      if (session.user) {\n        session.user.id = token.sub!\n        session.user.role = token.role\n      }\n      return session\n    },\n    \n    async signIn({ user, account, profile }) {\n      // 可以在这里添加额外的登录验证逻辑\n      return true\n    },\n    \n    async redirect({ url, baseUrl }) {\n      // 登录后重定向逻辑\n      if (url.startsWith(\"/\")) return `${baseUrl}${url}`\n      else if (new URL(url).origin === baseUrl) return url\n      return baseUrl\n    },\n  },\n  \n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  \n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 天\n  },\n  \n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;;AAsBA,8BAA8B;AAC9B,MAAM,eAAe;IACnB,QAAQ,GAAG,CAAC,WAAW,IAAI;CAE5B;AAED,UAAU;AACV,MAAM,sBAAsB,EAE3B;AAED,aAAa;AACb,SAAS,YAAY,KAAa;IAChC,IAAI,aAAa,QAAQ,CAAC,QAAQ;QAChC,OAAO;IACT;IACA,IAAI,oBAAoB,QAAQ,CAAC,QAAQ;QACvC,OAAO;IACT;IACA,OAAO;AACT;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;YAC9C,SAAQ,OAAO;gBACb,OAAO;oBACL,IAAI,QAAQ,EAAE,CAAC,QAAQ;oBACvB,MAAM,QAAQ,IAAI,IAAI,QAAQ,KAAK;oBACnC,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,UAAU;oBACzB,MAAM,YAAY,QAAQ,KAAK,IAAI;gBACrC;YACF;QACF;KACD;IAED,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;QAEA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,mBAAmB;YACnB,OAAO;QACT;QAEA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,WAAW;YACX,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAC7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IAEA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IAEA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IAEA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\"\nimport { authOptions } from \"@/lib/auth\"\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}
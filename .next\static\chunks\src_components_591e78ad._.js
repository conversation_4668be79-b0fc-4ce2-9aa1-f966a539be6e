(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/article/article-editor.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_prismjs_components_56371eb0._.js",
  "static/chunks/src_79b4183b._.js",
  "static/chunks/node_modules_d73aec5c._.js",
  "static/chunks/node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js",
  "static/chunks/node_modules_highlight_d98bfb5f.js",
  "static/chunks/node_modules_parse5_dist_afbcc6e3._.js",
  "static/chunks/node_modules_@radix-ui_2cb5c572._.js",
  "static/chunks/node_modules_@floating-ui_9ec1fa39._.js",
  "static/chunks/node_modules_0f1e885a._.js",
  {
    "path": "static/chunks/node_modules_prismjs_themes_prism-tomorrow_a276aa2a.css",
    "included": [
      "[project]/node_modules/prismjs/themes/prism-tomorrow.css [app-client] (css)"
    ]
  },
  "static/chunks/src_components_article_article-editor_tsx_c86ee6c9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/article/article-editor.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/files/file-manager.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_2a28fd98._.js",
  "static/chunks/node_modules_578d29c5._.js",
  "static/chunks/src_components_files_file-manager_tsx_c86ee6c9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/files/file-manager.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/search/search-interface.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_15f8e3f8._.js",
  "static/chunks/node_modules_c0ef64d7._.js",
  "static/chunks/src_components_search_search-interface_tsx_c86ee6c9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/search/search-interface.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/components/article/ai-assistant.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_6570953a._.js",
  "static/chunks/src_components_c7f793fa._.js",
  "static/chunks/src_components_article_ai-assistant_tsx_c86ee6c9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/article/ai-assistant.tsx [app-client] (ecmascript)");
    });
});
}}),
}]);
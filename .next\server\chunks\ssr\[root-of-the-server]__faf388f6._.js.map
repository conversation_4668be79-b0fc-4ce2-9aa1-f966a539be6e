{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      // 确保按钮有适当的类型\n      type={!asChild && !props.type ? \"button\" : props.type}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAa;QACb,MAAM,CAAC,WAAW,CAAC,MAAM,IAAI,GAAG,WAAW,MAAM,IAAI;QACpD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({\n  className,\n  type,\n  id,\n  \"aria-label\": ariaLabel,\n  \"aria-describedby\": ariaDescribedby,\n  required,\n  ...props\n}: React.ComponentProps<\"input\">) {\n  // 确保输入框有适当的无障碍标签\n  const hasAccessibleLabel = id || ariaLabel || ariaDescribedby || props[\"aria-labelledby\"]\n\n  if (!hasAccessibleLabel && process.env.NODE_ENV === \"development\") {\n    console.warn(\n      \"Input component is missing an accessible label. \" +\n      \"Add an id (paired with a label), aria-label, aria-labelledby, or aria-describedby attribute.\"\n    )\n  }\n\n  return (\n    <input\n      type={type}\n      id={id}\n      data-slot=\"input\"\n      aria-label={ariaLabel}\n      aria-describedby={ariaDescribedby}\n      required={required}\n      aria-required={required}\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EACb,SAAS,EACT,IAAI,EACJ,EAAE,EACF,cAAc,SAAS,EACvB,oBAAoB,eAAe,EACnC,QAAQ,EACR,GAAG,OAC2B;IAC9B,iBAAiB;IACjB,MAAM,qBAAqB,MAAM,aAAa,mBAAmB,KAAK,CAAC,kBAAkB;IAEzF,IAAI,CAAC,sBAAsB,oDAAyB,eAAe;QACjE,QAAQ,IAAI,CACV,qDACA;IAEJ;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,IAAI;QACJ,aAAU;QACV,cAAY;QACZ,oBAAkB;QAClB,UAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/optimized-image.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from \"react\"\nimport Image from \"next/image\"\nimport { cn } from \"@/lib/utils\"\n\ninterface OptimizedImageProps {\n  src: string\n  alt: string\n  width?: number\n  height?: number\n  className?: string\n  priority?: boolean\n  sizes?: string\n  quality?: number\n  fill?: boolean\n  objectFit?: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\"\n  objectPosition?: string\n  onLoad?: () => void\n  fallbackSrc?: string\n  lazy?: boolean\n  placeholder?: 'blur' | 'empty'\n  blurDataURL?: string\n  [key: string]: any\n}\n\n/**\n * 优化的图片组件\n * 提供懒加载、模糊占位符、错误处理等功能\n */\nexport function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className,\n  priority = false,\n  sizes = \"100vw\",\n  quality = 75,\n  fill = false,\n  objectFit = \"cover\",\n  objectPosition = \"center\",\n  onLoad,\n  fallbackSrc = \"/images/placeholder.jpg\",\n  lazy = true,\n  placeholder = 'empty',\n  blurDataURL,\n  ...props\n}: OptimizedImageProps) {\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState(false)\n  const [imageSrc, setImageSrc] = useState(src)\n  const [isInView, setIsInView] = useState(!lazy || priority)\n  const imgRef = useRef<HTMLDivElement>(null)\n\n  // 懒加载逻辑\n  useEffect(() => {\n    if (!lazy || priority || isInView) return\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            setIsInView(true)\n            observer.disconnect()\n          }\n        })\n      },\n      {\n        rootMargin: '50px', // 提前50px开始加载\n        threshold: 0.1,\n      }\n    )\n\n    if (imgRef.current) {\n      observer.observe(imgRef.current)\n    }\n\n    return () => observer.disconnect()\n  }, [lazy, priority, isInView])\n\n  // 当 src 变化时重置状态\n  useEffect(() => {\n    setIsLoading(true)\n    setError(false)\n    setImageSrc(src)\n  }, [src])\n\n  // 生成模糊占位符\n  const blurDataURL = \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n\n  // 处理图片加载完成\n  const handleImageLoad = () => {\n    setIsLoading(false)\n    if (onLoad) onLoad()\n  }\n\n  // 处理图片加载错误\n  const handleImageError = () => {\n    setError(true)\n    setImageSrc(fallbackSrc)\n  }\n\n  // 如果图片不在视口内，显示占位符\n  if (!isInView) {\n    return (\n      <div\n        ref={imgRef}\n        className={cn(\n          \"flex items-center justify-center bg-muted animate-pulse\",\n          className\n        )}\n        style={{\n          width: fill ? \"100%\" : width,\n          height: fill ? \"100%\" : height,\n        }}\n      >\n        <div className=\"text-center\">\n          <div className=\"w-8 h-8 bg-muted-foreground/20 rounded mx-auto mb-2\"></div>\n          <div className=\"w-16 h-3 bg-muted-foreground/20 rounded mx-auto\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div\n      ref={imgRef}\n      className={cn(\n        \"relative overflow-hidden\",\n        isLoading && \"animate-pulse bg-muted\",\n        className\n      )}\n      style={{\n        width: fill ? \"100%\" : width,\n        height: fill ? \"100%\" : height,\n      }}\n    >\n      <Image\n        src={imageSrc}\n        alt={alt}\n        width={fill ? undefined : width}\n        height={fill ? undefined : height}\n        className={cn(\n          \"transition-opacity duration-300\",\n          isLoading ? \"opacity-0\" : \"opacity-100\",\n          objectFit === \"cover\" && \"object-cover\",\n          objectFit === \"contain\" && \"object-contain\",\n          objectFit === \"fill\" && \"object-fill\",\n          objectFit === \"none\" && \"object-none\",\n          objectFit === \"scale-down\" && \"object-scale-down\"\n        )}\n        style={{ objectPosition }}\n        priority={priority}\n        placeholder=\"blur\"\n        blurDataURL={blurDataURL}\n        loading={priority ? \"eager\" : \"lazy\"}\n        sizes={sizes}\n        quality={quality}\n        fill={fill}\n        onLoad={handleImageLoad}\n        onError={handleImageError}\n        {...props}\n      />\n    </div>\n  )\n}\n\n/**\n * 响应式图片组件\n * 根据屏幕尺寸加载不同大小的图片\n */\nexport function ResponsiveImage({\n  src,\n  alt,\n  className,\n  ...props\n}: OptimizedImageProps) {\n  // 构建响应式图片源\n  const basePath = src.replace(/\\.[^/.]+$/, \"\") // 移除扩展名\n  const extension = src.split('.').pop() || \"jpg\"\n  \n  // 生成不同尺寸的图片路径\n  const srcSet = {\n    small: `${basePath}-sm.${extension}`,\n    medium: `${basePath}-md.${extension}`,\n    large: `${basePath}-lg.${extension}`,\n    original: src,\n  }\n\n  return (\n    <OptimizedImage\n      src={src}\n      alt={alt}\n      className={className}\n      sizes=\"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw\"\n      srcSet={`\n        ${srcSet.small} 640w,\n        ${srcSet.medium} 1024w,\n        ${srcSet.large} 1920w,\n        ${srcSet.original} 2560w\n      `}\n      {...props}\n    />\n  )\n}\n\n/**\n * 背景图片组件\n */\nexport function BackgroundImage({\n  src,\n  alt,\n  className,\n  children,\n  overlay = false,\n  overlayColor = \"rgba(0, 0, 0, 0.5)\",\n  ...props\n}: OptimizedImageProps & {\n  children?: React.ReactNode\n  overlay?: boolean\n  overlayColor?: string\n}) {\n  return (\n    <div className={cn(\"relative\", className)}>\n      <OptimizedImage\n        src={src}\n        alt={alt}\n        fill\n        className=\"absolute inset-0 z-0\"\n        {...props}\n      />\n      \n      {overlay && (\n        <div\n          className=\"absolute inset-0 z-10\"\n          style={{ backgroundColor: overlayColor }}\n        />\n      )}\n      \n      {children && (\n        <div className=\"relative z-20\">{children}</div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAJA;;;;;AA8BO,SAAS,eAAe,EAC7B,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,WAAW,KAAK,EAChB,QAAQ,OAAO,EACf,UAAU,EAAE,EACZ,OAAO,KAAK,EACZ,YAAY,OAAO,EACnB,iBAAiB,QAAQ,EACzB,MAAM,EACN,cAAc,yBAAyB,EACvC,OAAO,IAAI,EACX,cAAc,OAAO,EACrB,WAAW,EACX,GAAG,OACiB;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,QAAQ;IAClD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEtC,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,YAAY,UAAU;QAEnC,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,MAAM,cAAc,EAAE;oBACxB,YAAY;oBACZ,SAAS,UAAU;gBACrB;YACF;QACF,GACA;YACE,YAAY;YACZ,WAAW;QACb;QAGF,IAAI,OAAO,OAAO,EAAE;YAClB,SAAS,OAAO,CAAC,OAAO,OAAO;QACjC;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG;QAAC;QAAM;QAAU;KAAS;IAE7B,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,SAAS;QACT,YAAY;IACd,GAAG;QAAC;KAAI;IAER,UAAU;IACV,MAAM,cAAc;IAEpB,WAAW;IACX,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI,QAAQ;IACd;IAEA,WAAW;IACX,MAAM,mBAAmB;QACvB,SAAS;QACT,YAAY;IACd;IAEA,kBAAkB;IAClB,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;YAEF,OAAO;gBACL,OAAO,OAAO,SAAS;gBACvB,QAAQ,OAAO,SAAS;YAC1B;sBAEA,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4BACA,aAAa,0BACb;QAEF,OAAO;YACL,OAAO,OAAO,SAAS;YACvB,QAAQ,OAAO,SAAS;QAC1B;kBAEA,cAAA,8OAAC,6HAAA,CAAA,UAAK;YACJ,KAAK;YACL,KAAK;YACL,OAAO,OAAO,YAAY;YAC1B,QAAQ,OAAO,YAAY;YAC3B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mCACA,YAAY,cAAc,eAC1B,cAAc,WAAW,gBACzB,cAAc,aAAa,kBAC3B,cAAc,UAAU,eACxB,cAAc,UAAU,eACxB,cAAc,gBAAgB;YAEhC,OAAO;gBAAE;YAAe;YACxB,UAAU;YACV,aAAY;YACZ,aAAa;YACb,SAAS,WAAW,UAAU;YAC9B,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;YACR,SAAS;YACR,GAAG,KAAK;;;;;;;;;;;AAIjB;AAMO,SAAS,gBAAgB,EAC9B,GAAG,EACH,GAAG,EACH,SAAS,EACT,GAAG,OACiB;IACpB,WAAW;IACX,MAAM,WAAW,IAAI,OAAO,CAAC,aAAa,IAAI,QAAQ;;IACtD,MAAM,YAAY,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM;IAE1C,cAAc;IACd,MAAM,SAAS;QACb,OAAO,GAAG,SAAS,IAAI,EAAE,WAAW;QACpC,QAAQ,GAAG,SAAS,IAAI,EAAE,WAAW;QACrC,OAAO,GAAG,SAAS,IAAI,EAAE,WAAW;QACpC,UAAU;IACZ;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,KAAK;QACL,WAAW;QACX,OAAM;QACN,QAAQ,CAAC;QACP,EAAE,OAAO,KAAK,CAAC;QACf,EAAE,OAAO,MAAM,CAAC;QAChB,EAAE,OAAO,KAAK,CAAC;QACf,EAAE,OAAO,QAAQ,CAAC;MACpB,CAAC;QACA,GAAG,KAAK;;;;;;AAGf;AAKO,SAAS,gBAAgB,EAC9B,GAAG,EACH,GAAG,EACH,SAAS,EACT,QAAQ,EACR,UAAU,KAAK,EACf,eAAe,oBAAoB,EACnC,GAAG,OAKJ;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAC7B,8OAAC;gBACC,KAAK;gBACL,KAAK;gBACL,IAAI;gBACJ,WAAU;gBACT,GAAG,KAAK;;;;;;YAGV,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,iBAAiB;gBAAa;;;;;;YAI1C,0BACC,8OAAC;gBAAI,WAAU;0BAAiB;;;;;;;;;;;;AAIxC", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/article-card.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { <PERSON>, <PERSON>, <PERSON>, Heart, Tag } from \"lucide-react\"\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { OptimizedImage } from \"@/components/ui/optimized-image\"\nimport { formatDate, formatRelativeTime, truncateText } from \"@/lib/utils\"\nimport type { Article } from \"@/types\"\n\ninterface ArticleCardProps {\n  article: Article\n  showAuthor?: boolean\n  showStats?: boolean\n  variant?: \"default\" | \"compact\" | \"featured\"\n}\n\nexport function ArticleCard({ \n  article, \n  showAuthor = true, \n  showStats = true,\n  variant = \"default\" \n}: ArticleCardProps) {\n  const isCompact = variant === \"compact\"\n  const isFeatured = variant === \"featured\"\n\n  return (\n    <Card className={`group hover:shadow-lg transition-all duration-300 h-full flex flex-col ${\n      isFeatured ? \"border-primary/20 bg-gradient-to-br from-background to-muted/20\" : \"\"\n    }`}>\n      {/* 封面图片 */}\n      {article.coverImage && !isCompact && (\n        <div className=\"relative overflow-hidden rounded-t-lg\">\n          <Link href={`/articles/${article.slug}`}>\n            <OptimizedImage\n              src={article.coverImage}\n              alt={article.title}\n              width={400}\n              height={200}\n              className=\"w-full h-32 sm:h-40 md:h-48 object-cover transition-transform duration-300 group-hover:scale-105\"\n            />\n          </Link>\n          {isFeatured && (\n            <div className=\"absolute top-2 left-2 sm:top-4 sm:left-4\">\n              <Badge variant=\"secondary\" className=\"bg-primary text-primary-foreground text-xs\">\n                精选\n              </Badge>\n            </div>\n          )}\n        </div>\n      )}\n\n      <CardHeader className={`${isCompact ? \"pb-2\" : \"\"} flex-none`}>\n        {/* 分类和标签 */}\n        <div className=\"flex items-center gap-1 sm:gap-2 mb-2 flex-wrap\">\n          <Badge variant=\"outline\" className=\"text-xs\">\n            {article.category}\n          </Badge>\n          {article.tags.slice(0, isCompact ? 1 : 2).map((tag) => (\n            <Badge key={tag} variant=\"secondary\" className=\"text-xs\">\n              <Tag className=\"w-3 h-3 mr-1\" />\n              {tag}\n            </Badge>\n          ))}\n        </div>\n\n        {/* 标题 */}\n        <Link href={`/articles/${article.slug}`}>\n          <h3 className={`font-semibold line-clamp-2 hover:text-primary transition-colors ${\n            isFeatured ? \"text-lg sm:text-xl\" : isCompact ? \"text-sm sm:text-base\" : \"text-base sm:text-lg\"\n          }`}>\n            {article.title}\n          </h3>\n        </Link>\n      </CardHeader>\n\n      <CardContent className={`${isCompact ? \"py-2\" : \"\"} flex-1`}>\n        {/* 摘要 */}\n        {(article.excerpt || article.summary) && !isCompact && (\n          <p className=\"text-muted-foreground text-xs sm:text-sm line-clamp-3 mb-4\">\n            {truncateText(article.excerpt || article.summary || \"\", isCompact ? 80 : 120)}\n          </p>\n        )}\n\n        {/* AI 摘要标识 */}\n        {article.summary && !isCompact && (\n          <div className=\"flex items-center gap-1 text-xs text-muted-foreground mb-4\">\n            <div className=\"w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full\"></div>\n            <span className=\"hidden sm:inline\">AI 智能摘要</span>\n            <span className=\"sm:hidden\">AI</span>\n          </div>\n        )}\n      </CardContent>\n\n      <CardFooter className=\"flex items-center justify-between pt-0\">\n        {/* 作者信息 */}\n        {showAuthor && (\n          <div className=\"flex items-center gap-2\">\n            <Avatar className=\"w-6 h-6\">\n              <AvatarImage src={article.author.avatar} />\n              <AvatarFallback className=\"text-xs\">\n                {article.author.name.charAt(0)}\n              </AvatarFallback>\n            </Avatar>\n            <span className=\"text-sm text-muted-foreground\">\n              {article.author.name}\n            </span>\n          </div>\n        )}\n\n        {/* 发布时间 */}\n        <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n          <Calendar className=\"w-3 h-3\" />\n          <time dateTime={article.publishedAt?.toISOString()}>\n            {article.publishedAt ? formatRelativeTime(article.publishedAt) : formatRelativeTime(article.createdAt)}\n          </time>\n        </div>\n      </CardFooter>\n\n      {/* 统计信息 */}\n      {showStats && !isCompact && (\n        <CardFooter className=\"pt-0 border-t\">\n          <div className=\"flex items-center gap-4 text-xs text-muted-foreground w-full\">\n            <div className=\"flex items-center gap-1\">\n              <Eye className=\"w-3 h-3\" />\n              {article.viewCount}\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <Heart className=\"w-3 h-3\" />\n              {article.likeCount}\n            </div>\n            <div className=\"flex items-center gap-1 ml-auto\">\n              <Clock className=\"w-3 h-3\" />\n              {Math.ceil(article.content.length / 500)} 分钟阅读\n            </div>\n          </div>\n        </CardFooter>\n      )}\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAUO,SAAS,YAAY,EAC1B,OAAO,EACP,aAAa,IAAI,EACjB,YAAY,IAAI,EAChB,UAAU,SAAS,EACF;IACjB,MAAM,YAAY,YAAY;IAC9B,MAAM,aAAa,YAAY;IAE/B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,uEAAuE,EACvF,aAAa,oEAAoE,IACjF;;YAEC,QAAQ,UAAU,IAAI,CAAC,2BACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;kCACrC,cAAA,8OAAC,8IAAA,CAAA,iBAAc;4BACb,KAAK,QAAQ,UAAU;4BACvB,KAAK,QAAQ,KAAK;4BAClB,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;oBAGb,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAA6C;;;;;;;;;;;;;;;;;0BAQ1F,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAW,GAAG,YAAY,SAAS,GAAG,UAAU,CAAC;;kCAE3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAChC,QAAQ,QAAQ;;;;;;4BAElB,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC,oBAC7C,8OAAC,iIAAA,CAAA,QAAK;oCAAW,SAAQ;oCAAY,WAAU;;sDAC7C,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCACd;;mCAFS;;;;;;;;;;;kCAQhB,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;kCACrC,cAAA,8OAAC;4BAAG,WAAW,CAAC,gEAAgE,EAC9E,aAAa,uBAAuB,YAAY,yBAAyB,wBACzE;sCACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;0BAKpB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAW,GAAG,YAAY,SAAS,GAAG,OAAO,CAAC;;oBAExD,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,KAAK,CAAC,2BACxC,8OAAC;wBAAE,WAAU;kCACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,IAAI,YAAY,KAAK;;;;;;oBAK5E,QAAQ,OAAO,IAAI,CAAC,2BACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,8OAAC;gCAAK,WAAU;0CAAY;;;;;;;;;;;;;;;;;;0BAKlC,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;oBAEnB,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,kIAAA,CAAA,cAAW;wCAAC,KAAK,QAAQ,MAAM,CAAC,MAAM;;;;;;kDACvC,8OAAC,kIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,QAAQ,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;0CAGhC,8OAAC;gCAAK,WAAU;0CACb,QAAQ,MAAM,CAAC,IAAI;;;;;;;;;;;;kCAM1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAK,UAAU,QAAQ,WAAW,EAAE;0CAClC,QAAQ,WAAW,GAAG,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,WAAW,IAAI,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;;;;;;;YAM1G,aAAa,CAAC,2BACb,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCACd,QAAQ,SAAS;;;;;;;sCAEpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB,QAAQ,SAAS;;;;;;;sCAEpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB,KAAK,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,GAAG;gCAAK;;;;;;;;;;;;;;;;;;;;;;;;AAOvD", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\ninterface LoadingProps {\n  size?: \"sm\" | \"md\" | \"lg\"\n  className?: string\n}\n\nexport function Loading({ size = \"md\", className }: LoadingProps) {\n  const sizeClasses = {\n    sm: \"w-4 h-4\",\n    md: \"w-6 h-6\", \n    lg: \"w-8 h-8\"\n  }\n\n  return (\n    <div className={cn(\"flex items-center justify-center\", className)}>\n      <div className={cn(\n        \"animate-spin rounded-full border-2 border-muted border-t-primary\",\n        sizeClasses[size]\n      )} />\n    </div>\n  )\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn(\"animate-pulse bg-muted rounded\", className)} />\n  )\n}\n\nexport function ArticleCardSkeleton() {\n  return (\n    <div className=\"border rounded-lg p-6 space-y-4\">\n      <div className=\"flex items-center gap-2\">\n        <LoadingSkeleton className=\"h-5 w-16\" />\n        <LoadingSkeleton className=\"h-5 w-12\" />\n      </div>\n      <LoadingSkeleton className=\"h-6 w-3/4\" />\n      <LoadingSkeleton className=\"h-4 w-full\" />\n      <LoadingSkeleton className=\"h-4 w-2/3\" />\n      <div className=\"flex items-center justify-between pt-4\">\n        <div className=\"flex items-center gap-2\">\n          <LoadingSkeleton className=\"h-6 w-6 rounded-full\" />\n          <LoadingSkeleton className=\"h-4 w-20\" />\n        </div>\n        <LoadingSkeleton className=\"h-4 w-16\" />\n      </div>\n    </div>\n  )\n}\n\nexport function PageLoading() {\n  return (\n    <div className=\"flex items-center justify-center min-h-[400px]\">\n      <div className=\"text-center space-y-4\">\n        <Loading size=\"lg\" />\n        <p className=\"text-muted-foreground\">加载中...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAOO,SAAS,QAAQ,EAAE,OAAO,IAAI,EAAE,SAAS,EAAgB;IAC9D,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,oEACA,WAAW,CAAC,KAAK;;;;;;;;;;;AAIzB;AAEO,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;;;;;;AAEzD;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAgB,WAAU;;;;;;kCAC3B,8OAAC;wBAAgB,WAAU;;;;;;;;;;;;0BAE7B,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAgB,WAAU;;;;;;0BAC3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAgB,WAAU;;;;;;0CAC3B,8OAAC;gCAAgB,WAAU;;;;;;;;;;;;kCAE7B,8OAAC;wBAAgB,WAAU;;;;;;;;;;;;;;;;;;AAInC;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAQ,MAAK;;;;;;8BACd,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAI7C", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/responsive-grid.tsx"], "sourcesContent": ["import { ReactNode } from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ResponsiveGridProps {\n  children: ReactNode\n  className?: string\n  cols?: {\n    default?: number\n    sm?: number\n    md?: number\n    lg?: number\n    xl?: number\n  }\n  gap?: number\n}\n\nexport function ResponsiveGrid({ \n  children, \n  className,\n  cols = { default: 1, md: 2, lg: 3 },\n  gap = 6\n}: ResponsiveGridProps) {\n  const gridClasses = [\n    `grid`,\n    `gap-${gap}`,\n    cols.default && `grid-cols-${cols.default}`,\n    cols.sm && `sm:grid-cols-${cols.sm}`,\n    cols.md && `md:grid-cols-${cols.md}`,\n    cols.lg && `lg:grid-cols-${cols.lg}`,\n    cols.xl && `xl:grid-cols-${cols.xl}`,\n  ].filter(Boolean).join(' ')\n\n  return (\n    <div className={cn(gridClasses, className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface ResponsiveContainerProps {\n  children: ReactNode\n  className?: string\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"full\"\n}\n\nexport function ResponsiveContainer({ \n  children, \n  className,\n  size = \"2xl\" \n}: ResponsiveContainerProps) {\n  const sizeClasses = {\n    sm: \"max-w-sm\",\n    md: \"max-w-md\",\n    lg: \"max-w-lg\", \n    xl: \"max-w-xl\",\n    \"2xl\": \"max-w-2xl\",\n    full: \"max-w-full\"\n  }\n\n  return (\n    <div className={cn(\n      \"mx-auto w-full px-4 sm:px-6 lg:px-8\",\n      sizeClasses[size],\n      className\n    )}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAeO,SAAS,eAAe,EAC7B,QAAQ,EACR,SAAS,EACT,OAAO;IAAE,SAAS;IAAG,IAAI;IAAG,IAAI;AAAE,CAAC,EACnC,MAAM,CAAC,EACa;IACpB,MAAM,cAAc;QAClB,CAAC,IAAI,CAAC;QACN,CAAC,IAAI,EAAE,KAAK;QACZ,KAAK,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAC3C,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACpC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACpC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACpC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;KACrC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC7B;;;;;;AAGP;AAQO,SAAS,oBAAoB,EAClC,QAAQ,EACR,SAAS,EACT,OAAO,KAAK,EACa;IACzB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uCACA,WAAW,CAAC,KAAK,EACjB;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/config/site.ts"], "sourcesContent": ["export const siteConfig = {\n  name: \"现代博客系统\",\n  description: \"基于 Next.js 的现代化博客系统，支持 AI 摘要、多语言、深色模式等功能\",\n  url: \"https://your-domain.com\",\n  ogImage: \"https://your-domain.com/og.jpg\",\n  links: {\n    github: \"https://github.com/yourusername/your-repo\",\n    twitter: \"https://twitter.com/yourusername\",\n  },\n  author: {\n    name: \"Your Name\",\n    email: \"<EMAIL>\",\n    twitter: \"@yourusername\",\n  },\n  // 主题配置\n  theme: {\n    defaultTheme: \"light\" as const,\n    enableSystemTheme: true,\n  },\n  // 语言配置\n  i18n: {\n    defaultLocale: \"zh\" as const,\n    locales: [\"zh\", \"en\"] as const,\n  },\n  // 分页配置\n  pagination: {\n    articlesPerPage: 10,\n    commentsPerPage: 20,\n    filesPerPage: 20,\n  },\n  // 文件上传配置\n  upload: {\n    maxFileSize: 10 * 1024 * 1024, // 10MB\n    allowedTypes: [\n      \"image/jpeg\",\n      \"image/png\",\n      \"image/gif\",\n      \"image/webp\",\n      \"application/pdf\",\n      \"text/plain\",\n      \"text/markdown\",\n    ],\n  },\n  // SEO 配置\n  seo: {\n    titleTemplate: \"%s | 现代博客系统\",\n    defaultTitle: \"现代博客系统\",\n    description: \"基于 Next.js 的现代化博客系统\",\n    openGraph: {\n      type: \"website\",\n      locale: \"zh_CN\",\n      url: \"https://your-domain.com\",\n      siteName: \"现代博客系统\",\n    },\n    twitter: {\n      handle: \"@yourusername\",\n      site: \"@yourusername\",\n      cardType: \"summary_large_image\",\n    },\n  },\n  // 功能开关\n  features: {\n    enableComments: true,\n    enableSearch: true,\n    enableAnalytics: true,\n    enableAISummary: true,\n    enableFileManagement: true,\n    enableMultiLanguage: true,\n    enableDarkMode: true,\n  },\n  // 外部服务配置\n  services: {\n    cloudflareWorker: {\n      authUrl: process.env.CLOUDFLARE_AUTH_URL || \"\",\n      fileUrl: process.env.CLOUDFLARE_FILE_URL || \"\",\n    },\n    github: {\n      clientId: process.env.GITHUB_CLIENT_ID || \"\",\n      clientSecret: process.env.GITHUB_CLIENT_SECRET || \"\",\n    },\n    analytics: {\n      googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID || \"\",\n      plausibleDomain: process.env.PLAUSIBLE_DOMAIN || \"\",\n    },\n  },\n};\n\nexport type SiteConfig = typeof siteConfig;\n"], "names": [], "mappings": ";;;AAAO,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK;IACL,SAAS;IACT,OAAO;QACL,QAAQ;QACR,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,OAAO;IACP,OAAO;QACL,cAAc;QACd,mBAAmB;IACrB;IACA,OAAO;IACP,MAAM;QACJ,eAAe;QACf,SAAS;YAAC;YAAM;SAAK;IACvB;IACA,OAAO;IACP,YAAY;QACV,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;IAChB;IACA,SAAS;IACT,QAAQ;QACN,aAAa,KAAK,OAAO;QACzB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;IACT,KAAK;QACH,eAAe;QACf,cAAc;QACd,aAAa;QACb,WAAW;YACT,MAAM;YACN,QAAQ;YACR,KAAK;YACL,UAAU;QACZ;QACA,SAAS;YACP,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;IACF;IACA,OAAO;IACP,UAAU;QACR,gBAAgB;QAChB,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,sBAAsB;QACtB,qBAAqB;QACrB,gBAAgB;IAClB;IACA,SAAS;IACT,UAAU;QACR,kBAAkB;YAChB,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YAC5C,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QAC9C;QACA,QAAQ;YACN,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,WAAW;YACT,mBAAmB,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YACtD,iBAAiB,QAAQ,GAAG,CAAC,gBAAgB,IAAI;QACnD;IACF;AACF", "debugId": null}}, {"offset": {"line": 1253, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/seo/structured-data.tsx"], "sourcesContent": ["import { siteConfig } from \"@/config/site\"\nimport { Article, User } from \"@/types\"\n\ninterface StructuredDataProps {\n  type: \"website\" | \"article\" | \"breadcrumb\" | \"organization\" | \"person\"\n  data?: any\n}\n\n/**\n * 网站结构化数据\n */\nexport function WebsiteStructuredData() {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"WebSite\",\n    name: siteConfig.name,\n    description: siteConfig.description,\n    url: siteConfig.url,\n    potentialAction: {\n      \"@type\": \"SearchAction\",\n      target: {\n        \"@type\": \"EntryPoint\",\n        urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,\n      },\n      \"query-input\": \"required name=search_term_string\",\n    },\n    publisher: {\n      \"@type\": \"Organization\",\n      name: siteConfig.name,\n      logo: {\n        \"@type\": \"ImageObject\",\n        url: `${siteConfig.url}/logo.png`,\n        width: 512,\n        height: 512,\n      },\n    },\n    sameAs: [\n      siteConfig.links.github,\n      siteConfig.links.twitter,\n    ].filter(Boolean),\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 文章结构化数据\n */\nexport function ArticleStructuredData({ article }: { article: Article }) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BlogPosting\",\n    headline: article.title,\n    description: article.summary || article.excerpt,\n    image: article.coverImage ? [article.coverImage] : [],\n    datePublished: article.publishedAt?.toISOString() || article.createdAt.toISOString(),\n    dateModified: article.updatedAt.toISOString(),\n    author: {\n      \"@type\": \"Person\",\n      name: article.author.name,\n      url: `${siteConfig.url}/authors/${article.author.id}`,\n    },\n    publisher: {\n      \"@type\": \"Organization\",\n      name: siteConfig.name,\n      logo: {\n        \"@type\": \"ImageObject\",\n        url: `${siteConfig.url}/logo.png`,\n        width: 512,\n        height: 512,\n      },\n    },\n    mainEntityOfPage: {\n      \"@type\": \"WebPage\",\n      \"@id\": `${siteConfig.url}/articles/${article.slug}`,\n    },\n    keywords: article.tags.join(\", \"),\n    articleSection: article.category,\n    wordCount: article.content.length,\n    commentCount: 0, // 可以根据实际评论数量更新\n    interactionStatistic: [\n      {\n        \"@type\": \"InteractionCounter\",\n        interactionType: \"https://schema.org/ReadAction\",\n        userInteractionCount: article.viewCount,\n      },\n      {\n        \"@type\": \"InteractionCounter\",\n        interactionType: \"https://schema.org/LikeAction\",\n        userInteractionCount: article.likeCount,\n      },\n    ],\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 面包屑导航结构化数据\n */\nexport function BreadcrumbStructuredData({ items }: { \n  items: Array<{ name: string; url: string }> \n}) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BreadcrumbList\",\n    itemListElement: items.map((item, index) => ({\n      \"@type\": \"ListItem\",\n      position: index + 1,\n      name: item.name,\n      item: `${siteConfig.url}${item.url}`,\n    })),\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 组织结构化数据\n */\nexport function OrganizationStructuredData() {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    name: siteConfig.name,\n    description: siteConfig.description,\n    url: siteConfig.url,\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: `${siteConfig.url}/logo.png`,\n      width: 512,\n      height: 512,\n    },\n    contactPoint: {\n      \"@type\": \"ContactPoint\",\n      email: siteConfig.author.email,\n      contactType: \"customer service\",\n    },\n    sameAs: [\n      siteConfig.links.github,\n      siteConfig.links.twitter,\n    ].filter(Boolean),\n    founder: {\n      \"@type\": \"Person\",\n      name: siteConfig.author.name,\n    },\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 作者结构化数据\n */\nexport function PersonStructuredData({ author }: { author: User }) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Person\",\n    name: author.name,\n    email: author.email,\n    image: author.avatar,\n    url: `${siteConfig.url}/authors/${author.id}`,\n    jobTitle: author.role === 'admin' ? '管理员' : author.role === 'collaborator' ? '协作者' : '用户',\n    worksFor: {\n      \"@type\": \"Organization\",\n      name: siteConfig.name,\n    },\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * FAQ 结构化数据\n */\nexport function FAQStructuredData({ faqs }: { \n  faqs: Array<{ question: string; answer: string }> \n}) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"FAQPage\",\n    mainEntity: faqs.map(faq => ({\n      \"@type\": \"Question\",\n      name: faq.question,\n      acceptedAnswer: {\n        \"@type\": \"Answer\",\n        text: faq.answer,\n      },\n    })),\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n\n/**\n * 搜索结果结构化数据\n */\nexport function SearchResultsStructuredData({ \n  query, \n  results \n}: { \n  query: string\n  results: Article[] \n}) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"SearchResultsPage\",\n    mainEntity: {\n      \"@type\": \"ItemList\",\n      numberOfItems: results.length,\n      itemListElement: results.map((article, index) => ({\n        \"@type\": \"ListItem\",\n        position: index + 1,\n        item: {\n          \"@type\": \"Article\",\n          headline: article.title,\n          description: article.summary || article.excerpt,\n          url: `${siteConfig.url}/articles/${article.slug}`,\n          datePublished: article.publishedAt?.toISOString() || article.createdAt.toISOString(),\n          author: {\n            \"@type\": \"Person\",\n            name: article.author.name,\n          },\n        },\n      })),\n    },\n  }\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAWO,SAAS;IACd,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;QACrB,aAAa,qHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,KAAK,qHAAA,CAAA,aAAU,CAAC,GAAG;QACnB,iBAAiB;YACf,SAAS;YACT,QAAQ;gBACN,SAAS;gBACT,aAAa,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC;YAChE;YACA,eAAe;QACjB;QACA,WAAW;YACT,SAAS;YACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;YACrB,MAAM;gBACJ,SAAS;gBACT,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,CAAC;gBACjC,OAAO;gBACP,QAAQ;YACV;QACF;QACA,QAAQ;YACN,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;YACvB,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO;SACzB,CAAC,MAAM,CAAC;IACX;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,sBAAsB,EAAE,OAAO,EAAwB;IACrE,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,UAAU,QAAQ,KAAK;QACvB,aAAa,QAAQ,OAAO,IAAI,QAAQ,OAAO;QAC/C,OAAO,QAAQ,UAAU,GAAG;YAAC,QAAQ,UAAU;SAAC,GAAG,EAAE;QACrD,eAAe,QAAQ,WAAW,EAAE,iBAAiB,QAAQ,SAAS,CAAC,WAAW;QAClF,cAAc,QAAQ,SAAS,CAAC,WAAW;QAC3C,QAAQ;YACN,SAAS;YACT,MAAM,QAAQ,MAAM,CAAC,IAAI;YACzB,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,EAAE,EAAE;QACvD;QACA,WAAW;YACT,SAAS;YACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;YACrB,MAAM;gBACJ,SAAS;gBACT,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,CAAC;gBACjC,OAAO;gBACP,QAAQ;YACV;QACF;QACA,kBAAkB;YAChB,SAAS;YACT,OAAO,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;QACrD;QACA,UAAU,QAAQ,IAAI,CAAC,IAAI,CAAC;QAC5B,gBAAgB,QAAQ,QAAQ;QAChC,WAAW,QAAQ,OAAO,CAAC,MAAM;QACjC,cAAc;QACd,sBAAsB;YACpB;gBACE,SAAS;gBACT,iBAAiB;gBACjB,sBAAsB,QAAQ,SAAS;YACzC;YACA;gBACE,SAAS;gBACT,iBAAiB;gBACjB,sBAAsB,QAAQ,SAAS;YACzC;SACD;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,yBAAyB,EAAE,KAAK,EAE/C;IACC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC3C,SAAS;gBACT,UAAU,QAAQ;gBAClB,MAAM,KAAK,IAAI;gBACf,MAAM,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,GAAG,KAAK,GAAG,EAAE;YACtC,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS;IACd,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;QACrB,aAAa,qHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,KAAK,qHAAA,CAAA,aAAU,CAAC,GAAG;QACnB,MAAM;YACJ,SAAS;YACT,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,CAAC;YACjC,OAAO;YACP,QAAQ;QACV;QACA,cAAc;YACZ,SAAS;YACT,OAAO,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK;YAC9B,aAAa;QACf;QACA,QAAQ;YACN,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;YACvB,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO;SACzB,CAAC,MAAM,CAAC;QACT,SAAS;YACP,SAAS;YACT,MAAM,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,IAAI;QAC9B;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,qBAAqB,EAAE,MAAM,EAAoB;IAC/D,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,KAAK;QACnB,OAAO,OAAO,MAAM;QACpB,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;QAC7C,UAAU,OAAO,IAAI,KAAK,UAAU,QAAQ,OAAO,IAAI,KAAK,iBAAiB,QAAQ;QACrF,UAAU;YACR,SAAS;YACT,MAAM,qHAAA,CAAA,aAAU,CAAC,IAAI;QACvB;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,kBAAkB,EAAE,IAAI,EAEvC;IACC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,YAAY,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,SAAS;gBACT,MAAM,IAAI,QAAQ;gBAClB,gBAAgB;oBACd,SAAS;oBACT,MAAM,IAAI,MAAM;gBAClB;YACF,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAKO,SAAS,4BAA4B,EAC1C,KAAK,EACL,OAAO,EAIR;IACC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,YAAY;YACV,SAAS;YACT,eAAe,QAAQ,MAAM;YAC7B,iBAAiB,QAAQ,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;oBAChD,SAAS;oBACT,UAAU,QAAQ;oBAClB,MAAM;wBACJ,SAAS;wBACT,UAAU,QAAQ,KAAK;wBACvB,aAAa,QAAQ,OAAO,IAAI,QAAQ,OAAO;wBAC/C,KAAK,GAAG,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;wBACjD,eAAe,QAAQ,WAAW,EAAE,iBAAiB,QAAQ,SAAS,CAAC,WAAW;wBAClF,QAAQ;4BACN,SAAS;4BACT,MAAM,QAAQ,MAAM,CAAC,IAAI;wBAC3B;oBACF;gBACF,CAAC;QACH;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE", "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/search/search-interface.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, use<PERSON>allback, useRef } from \"react\"\nimport { useRouter, usePathname, useSearchParams } from \"next/navigation\"\nimport { Search as SearchIcon, Filter, X, Clock, Tag, Calendar } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\nimport { ArticleCard } from \"@/components/ui/article-card\"\nimport { ArticleCardSkeleton } from \"@/components/ui/loading\"\nimport { ResponsiveGrid } from \"@/components/ui/responsive-grid\"\nimport { SearchResultsStructuredData } from \"@/components/seo/structured-data\"\nimport { debounce } from \"@/lib/utils\"\nimport type { Article } from \"@/types\"\n\ninterface SearchInterfaceProps {\n  initialQuery?: string\n  initialPage?: number\n  initialCategory?: string\n  initialTag?: string\n}\n\ninterface SearchResults {\n  articles: Article[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n  query: string\n  suggestions: string[]\n}\n\nexport function SearchInterface({\n  initialQuery = \"\",\n  initialPage = 1,\n  initialCategory = \"\",\n  initialTag = \"\",\n}: SearchInterfaceProps) {\n  const router = useRouter()\n  const pathname = usePathname()\n  const searchParams = useSearchParams()\n  \n  const [query, setQuery] = useState(initialQuery)\n  const [page, setPage] = useState(initialPage)\n  const [category, setCategory] = useState(initialCategory)\n  const [tag, setTag] = useState(initialTag)\n  const [activeTab, setActiveTab] = useState<\"all\" | \"articles\" | \"tags\">(\"all\")\n  \n  const [results, setResults] = useState<SearchResults | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  \n  const searchInputRef = useRef<HTMLInputElement>(null)\n  \n  // 执行搜索\n  const performSearch = useCallback(async () => {\n    if (!query.trim()) {\n      setResults(null)\n      return\n    }\n    \n    setIsLoading(true)\n    setError(null)\n    \n    try {\n      const params = new URLSearchParams()\n      params.set(\"q\", query)\n      params.set(\"page\", page.toString())\n      if (category) params.set(\"category\", category)\n      if (tag) params.set(\"tag\", tag)\n      \n      const response = await fetch(`/api/search?${params.toString()}`)\n      \n      if (!response.ok) {\n        throw new Error(\"搜索请求失败\")\n      }\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        setResults(data.data)\n      } else {\n        throw new Error(data.error || \"搜索失败\")\n      }\n    } catch (err) {\n      console.error(\"Search error:\", err)\n      setError(err instanceof Error ? err.message : \"搜索过程中发生错误\")\n    } finally {\n      setIsLoading(false)\n    }\n  }, [query, page, category, tag])\n  \n  // 更新 URL 参数\n  const updateUrlParams = useCallback(() => {\n    const params = new URLSearchParams(searchParams)\n    \n    if (query) {\n      params.set(\"q\", query)\n    } else {\n      params.delete(\"q\")\n    }\n    \n    if (page > 1) {\n      params.set(\"page\", page.toString())\n    } else {\n      params.delete(\"page\")\n    }\n    \n    if (category) {\n      params.set(\"category\", category)\n    } else {\n      params.delete(\"category\")\n    }\n    \n    if (tag) {\n      params.set(\"tag\", tag)\n    } else {\n      params.delete(\"tag\")\n    }\n    \n    router.push(`${pathname}?${params.toString()}`)\n  }, [query, page, category, tag, pathname, router, searchParams])\n  \n  // 防抖搜索\n  const debouncedSearch = useCallback(\n    debounce(() => {\n      performSearch()\n      updateUrlParams()\n    }, 300),\n    [performSearch, updateUrlParams]\n  )\n  \n  // 当搜索参数变化时执行搜索\n  useEffect(() => {\n    if (query.trim()) {\n      debouncedSearch()\n    } else {\n      setResults(null)\n      updateUrlParams()\n    }\n  }, [query, page, category, tag, debouncedSearch, updateUrlParams])\n  \n  // 处理搜索表单提交\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    performSearch()\n    updateUrlParams()\n  }\n  \n  // 处理搜索建议点击\n  const handleSuggestionClick = (suggestion: string) => {\n    setQuery(suggestion)\n    setPage(1)\n    if (searchInputRef.current) {\n      searchInputRef.current.focus()\n    }\n  }\n  \n  // 处理分页\n  const handlePageChange = (newPage: number) => {\n    setPage(newPage)\n  }\n  \n  // 清除搜索\n  const handleClearSearch = () => {\n    setQuery(\"\")\n    setPage(1)\n    setCategory(\"\")\n    setTag(\"\")\n    setResults(null)\n    if (searchInputRef.current) {\n      searchInputRef.current.focus()\n    }\n  }\n  \n  return (\n    <div className=\"space-y-6\">\n      {/* 搜索表单 */}\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <div className=\"relative flex-1\">\n            <SearchIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\n            <Input\n              ref={searchInputRef}\n              type=\"search\"\n              placeholder=\"搜索文章、标签或关键词...\"\n              className=\"pl-10 w-full\"\n              value={query}\n              onChange={(e) => {\n                setQuery(e.target.value)\n                setPage(1)\n              }}\n              autoFocus\n            />\n            {query && (\n              <Button\n                type=\"button\"\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"absolute right-2 top-1/2 transform -translate-y-1/2\"\n                onClick={handleClearSearch}\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            )}\n          </div>\n          <Button type=\"submit\" disabled={!query.trim() || isLoading}>\n            <SearchIcon className=\"w-4 h-4 mr-2\" />\n            搜索\n          </Button>\n        </div>\n        \n        {/* 高级筛选 */}\n        <div className=\"flex flex-wrap gap-2\">\n          {category && (\n            <Badge variant=\"outline\" className=\"flex items-center gap-1\">\n              <Filter className=\"w-3 h-3\" />\n              分类: {category}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-4 w-4 ml-1 p-0\"\n                onClick={() => setCategory(\"\")}\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            </Badge>\n          )}\n          \n          {tag && (\n            <Badge variant=\"outline\" className=\"flex items-center gap-1\">\n              <Tag className=\"w-3 h-3\" />\n              标签: {tag}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-4 w-4 ml-1 p-0\"\n                onClick={() => setTag(\"\")}\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            </Badge>\n          )}\n        </div>\n      </form>\n      \n      {/* 搜索结果 */}\n      {isLoading ? (\n        <div className=\"space-y-6\">\n          <div className=\"animate-pulse h-6 w-48 bg-muted rounded\"></div>\n          <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>\n            {Array.from({ length: 6 }).map((_, i) => (\n              <ArticleCardSkeleton key={i} />\n            ))}\n          </ResponsiveGrid>\n        </div>\n      ) : error ? (\n        <Card>\n          <CardContent className=\"py-8 text-center\">\n            <div className=\"text-destructive mb-2\">搜索出错</div>\n            <p className=\"text-muted-foreground\">{error}</p>\n            <Button\n              variant=\"outline\"\n              className=\"mt-4\"\n              onClick={performSearch}\n            >\n              重试\n            </Button>\n          </CardContent>\n        </Card>\n      ) : results ? (\n        <div className=\"space-y-6\">\n          {/* 结构化数据 */}\n          {results.articles.length > 0 && (\n            <SearchResultsStructuredData\n              query={results.query}\n              results={results.articles}\n            />\n          )}\n          \n          {/* 搜索结果标签页 */}\n          <Tabs defaultValue=\"all\" value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>\n            <TabsList>\n              <TabsTrigger value=\"all\">\n                全部 ({results.pagination.total})\n              </TabsTrigger>\n              <TabsTrigger value=\"articles\">\n                文章 ({results.articles.length})\n              </TabsTrigger>\n              <TabsTrigger value=\"tags\">\n                标签 ({results.suggestions.length})\n              </TabsTrigger>\n            </TabsList>\n            \n            <TabsContent value=\"all\" className=\"space-y-6\">\n              {/* 搜索建议 */}\n              {results.suggestions.length > 0 && (\n                <div className=\"space-y-2\">\n                  <h3 className=\"text-sm font-medium\">您可能想搜索:</h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {results.suggestions.map((suggestion) => (\n                      <Badge\n                        key={suggestion}\n                        variant=\"outline\"\n                        className=\"cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors\"\n                        onClick={() => handleSuggestionClick(suggestion)}\n                      >\n                        {suggestion}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              )}\n              \n              {/* 文章结果 */}\n              {results.articles.length > 0 ? (\n                <>\n                  <div className=\"flex items-center justify-between\">\n                    <h2 className=\"text-xl font-semibold\">\n                      搜索结果 ({results.pagination.total})\n                    </h2>\n                    <div className=\"text-sm text-muted-foreground\">\n                      第 {results.pagination.page} 页，共 {results.pagination.totalPages} 页\n                    </div>\n                  </div>\n                  \n                  <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>\n                    {results.articles.map((article) => (\n                      <ArticleCard\n                        key={article.id}\n                        article={article}\n                        variant=\"default\"\n                      />\n                    ))}\n                  </ResponsiveGrid>\n                  \n                  {/* 分页 */}\n                  {results.pagination.totalPages > 1 && (\n                    <div className=\"flex justify-center items-center gap-2 mt-6\">\n                      <Button\n                        variant=\"outline\"\n                        onClick={() => handlePageChange(page - 1)}\n                        disabled={page <= 1}\n                      >\n                        上一页\n                      </Button>\n                      \n                      <div className=\"flex items-center gap-1\">\n                        {Array.from({ length: Math.min(5, results.pagination.totalPages) }).map((_, i) => {\n                          const pageNum = i + 1\n                          return (\n                            <Button\n                              key={pageNum}\n                              variant={pageNum === page ? \"default\" : \"outline\"}\n                              size=\"sm\"\n                              onClick={() => handlePageChange(pageNum)}\n                            >\n                              {pageNum}\n                            </Button>\n                          )\n                        })}\n                      </div>\n                      \n                      <Button\n                        variant=\"outline\"\n                        onClick={() => handlePageChange(page + 1)}\n                        disabled={page >= results.pagination.totalPages}\n                      >\n                        下一页\n                      </Button>\n                    </div>\n                  )}\n                </>\n              ) : (\n                <div className=\"text-center py-12\">\n                  <div className=\"text-6xl mb-4\">🔍</div>\n                  <h3 className=\"text-xl font-semibold mb-2\">未找到结果</h3>\n                  <p className=\"text-muted-foreground mb-4\">\n                    没有找到与 \"{query}\" 相关的内容\n                  </p>\n                  <Button variant=\"outline\" onClick={handleClearSearch}>\n                    清除搜索\n                  </Button>\n                </div>\n              )}\n            </TabsContent>\n            \n            <TabsContent value=\"articles\">\n              {results.articles.length > 0 ? (\n                <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>\n                  {results.articles.map((article) => (\n                    <ArticleCard\n                      key={article.id}\n                      article={article}\n                      variant=\"default\"\n                    />\n                  ))}\n                </ResponsiveGrid>\n              ) : (\n                <div className=\"text-center py-12\">\n                  <p className=\"text-muted-foreground\">没有找到相关文章</p>\n                </div>\n              )}\n            </TabsContent>\n            \n            <TabsContent value=\"tags\">\n              {results.suggestions.length > 0 ? (\n                <div className=\"flex flex-wrap gap-2\">\n                  {results.suggestions.map((suggestion) => (\n                    <Badge\n                      key={suggestion}\n                      className=\"text-base py-2 px-4 cursor-pointer\"\n                      onClick={() => handleSuggestionClick(suggestion)}\n                    >\n                      {suggestion}\n                    </Badge>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-12\">\n                  <p className=\"text-muted-foreground\">没有找到相关标签</p>\n                </div>\n              )}\n            </TabsContent>\n          </Tabs>\n        </div>\n      ) : query ? (\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent mx-auto\"></div>\n          <p className=\"mt-4 text-muted-foreground\">搜索中...</p>\n        </div>\n      ) : (\n        <div className=\"text-center py-12\">\n          <div className=\"text-6xl mb-4\">🔍</div>\n          <h3 className=\"text-xl font-semibold mb-2\">搜索网站内容</h3>\n          <p className=\"text-muted-foreground\">\n            输入关键词开始搜索文章、标签和分类\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;;AAoCO,SAAS,gBAAgB,EAC9B,eAAe,EAAE,EACjB,cAAc,CAAC,EACf,kBAAkB,EAAE,EACpB,aAAa,EAAE,EACM;IACrB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAExE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,OAAO;IACP,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,WAAW;YACX;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,KAAK;YAChB,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ;YAChC,IAAI,UAAU,OAAO,GAAG,CAAC,YAAY;YACrC,IAAI,KAAK,OAAO,GAAG,CAAC,OAAO;YAE3B,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;YAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW,KAAK,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAO;QAAM;QAAU;KAAI;IAE/B,YAAY;IACZ,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,MAAM,SAAS,IAAI,gBAAgB;QAEnC,IAAI,OAAO;YACT,OAAO,GAAG,CAAC,KAAK;QAClB,OAAO;YACL,OAAO,MAAM,CAAC;QAChB;QAEA,IAAI,OAAO,GAAG;YACZ,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ;QAClC,OAAO;YACL,OAAO,MAAM,CAAC;QAChB;QAEA,IAAI,UAAU;YACZ,OAAO,GAAG,CAAC,YAAY;QACzB,OAAO;YACL,OAAO,MAAM,CAAC;QAChB;QAEA,IAAI,KAAK;YACP,OAAO,GAAG,CAAC,OAAO;QACpB,OAAO;YACL,OAAO,MAAM,CAAC;QAChB;QAEA,OAAO,IAAI,CAAC,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI;IAChD,GAAG;QAAC;QAAO;QAAM;QAAU;QAAK;QAAU;QAAQ;KAAa;IAE/D,OAAO;IACP,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE;QACP;QACA;IACF,GAAG,MACH;QAAC;QAAe;KAAgB;IAGlC,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,IAAI,IAAI;YAChB;QACF,OAAO;YACL,WAAW;YACX;QACF;IACF,GAAG;QAAC;QAAO;QAAM;QAAU;QAAK;QAAiB;KAAgB;IAEjE,WAAW;IACX,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB;QACA;IACF;IAEA,WAAW;IACX,MAAM,wBAAwB,CAAC;QAC7B,SAAS;QACT,QAAQ;QACR,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,KAAK;QAC9B;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC;QACxB,QAAQ;IACV;IAEA,OAAO;IACP,MAAM,oBAAoB;QACxB,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,WAAW;QACX,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,KAAK;QAC9B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,KAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC;4CACT,SAAS,EAAE,MAAM,CAAC,KAAK;4CACvB,QAAQ;wCACV;wCACA,SAAS;;;;;;oCAEV,uBACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAInB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU,CAAC,MAAM,IAAI,MAAM;;kDAC/C,8OAAC,sMAAA,CAAA,SAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAM3C,8OAAC;wBAAI,WAAU;;4BACZ,0BACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;oCACzB;kDACL,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,YAAY;kDAE3B,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAKlB,qBACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAY;oCACtB;kDACL,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,OAAO;kDAEtB,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQtB,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,8IAAA,CAAA,iBAAc;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;kCACtD,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,mIAAA,CAAA,sBAAmB,MAAM;;;;;;;;;;;;;;;uBAI9B,sBACF,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;sCACvC,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;sCACtC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;;;;;uBAKH,wBACF,8OAAC;gBAAI,WAAU;;oBAEZ,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,8OAAC,+IAAA,CAAA,8BAA2B;wBAC1B,OAAO,QAAQ,KAAK;wBACpB,SAAS,QAAQ,QAAQ;;;;;;kCAK7B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAM,OAAO;wBAAW,eAAe,CAAC,IAAM,aAAa;;0CAC5E,8OAAC,gIAAA,CAAA,WAAQ;;kDACP,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAAM;4CAClB,QAAQ,UAAU,CAAC,KAAK;4CAAC;;;;;;;kDAEhC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAAW;4CACvB,QAAQ,QAAQ,CAAC,MAAM;4CAAC;;;;;;;kDAE/B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAAO;4CACnB,QAAQ,WAAW,CAAC,MAAM;4CAAC;;;;;;;;;;;;;0CAIpC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAM,WAAU;;oCAEhC,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,2BACxB,8OAAC,iIAAA,CAAA,QAAK;wDAEJ,SAAQ;wDACR,WAAU;wDACV,SAAS,IAAM,sBAAsB;kEAEpC;uDALI;;;;;;;;;;;;;;;;oCAad,QAAQ,QAAQ,CAAC,MAAM,GAAG,kBACzB;;0DACE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;4DAAwB;4DAC7B,QAAQ,UAAU,CAAC,KAAK;4DAAC;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;4DAAgC;4DAC1C,QAAQ,UAAU,CAAC,IAAI;4DAAC;4DAAM,QAAQ,UAAU,CAAC,UAAU;4DAAC;;;;;;;;;;;;;0DAInE,8OAAC,8IAAA,CAAA,iBAAc;gDAAC,MAAM;oDAAE,SAAS;oDAAG,IAAI;oDAAG,IAAI;gDAAE;gDAAG,KAAK;0DACtD,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,8OAAC,2IAAA,CAAA,cAAW;wDAEV,SAAS;wDACT,SAAQ;uDAFH,QAAQ,EAAE;;;;;;;;;;4CAQpB,QAAQ,UAAU,CAAC,UAAU,GAAG,mBAC/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS,IAAM,iBAAiB,OAAO;wDACvC,UAAU,QAAQ;kEACnB;;;;;;kEAID,8OAAC;wDAAI,WAAU;kEACZ,MAAM,IAAI,CAAC;4DAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,QAAQ,UAAU,CAAC,UAAU;wDAAE,GAAG,GAAG,CAAC,CAAC,GAAG;4DAC1E,MAAM,UAAU,IAAI;4DACpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;gEAEL,SAAS,YAAY,OAAO,YAAY;gEACxC,MAAK;gEACL,SAAS,IAAM,iBAAiB;0EAE/B;+DALI;;;;;wDAQX;;;;;;kEAGF,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS,IAAM,iBAAiB,OAAO;wDACvC,UAAU,QAAQ,QAAQ,UAAU,CAAC,UAAU;kEAChD;;;;;;;;;;;;;qEAOP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;;oDAA6B;oDAChC;oDAAM;;;;;;;0DAEhB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS;0DAAmB;;;;;;;;;;;;;;;;;;0CAO5D,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAChB,QAAQ,QAAQ,CAAC,MAAM,GAAG,kBACzB,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,MAAM;wCAAE,SAAS;wCAAG,IAAI;wCAAG,IAAI;oCAAE;oCAAG,KAAK;8CACtD,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,8OAAC,2IAAA,CAAA,cAAW;4CAEV,SAAS;4CACT,SAAQ;2CAFH,QAAQ,EAAE;;;;;;;;;yDAOrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;0CAK3C,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAChB,QAAQ,WAAW,CAAC,MAAM,GAAG,kBAC5B,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,2BACxB,8OAAC,iIAAA,CAAA,QAAK;4CAEJ,WAAU;4CACV,SAAS,IAAM,sBAAsB;sDAEpC;2CAJI;;;;;;;;;yDASX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAM7C,sBACF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;qCAG5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}
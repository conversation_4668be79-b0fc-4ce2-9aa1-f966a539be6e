{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/types": ["./src/types/index.ts", "./workers/src/types/index.ts"], "@/utils": ["./src/lib/utils/index.ts", "./workers/src/utils/index.ts"], "@/utils/*": ["./src/lib/utils/*", "./workers/src/utils/*"], "@/services/*": ["./src/lib/services/*", "./workers/src/services/*"], "@/lib/*": ["./src/lib/*"], "@/components/*": ["./src/components/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}
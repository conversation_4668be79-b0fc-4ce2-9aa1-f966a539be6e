import { JWTPayload, User, ApiError } from '@/types';

/**
 * JWT 工具类
 */
export class JWT {
  private secret: string;

  constructor(secret: string) {
    this.secret = secret;
  }

  /**
   * 生成 JWT Token
   */
  async sign(payload: Omit<JWTPayload, 'iat' | 'exp'>, expiresIn: number = 7 * 24 * 60 * 60): Promise<string> {
    const now = Math.floor(Date.now() / 1000);
    const fullPayload: JWTPayload = {
      ...payload,
      iat: now,
      exp: now + expiresIn,
    };

    const header = {
      alg: 'HS256',
      typ: 'JWT',
    };

    const encodedHeader = this.base64UrlEncode(JSON.stringify(header));
    const encodedPayload = this.base64UrlEncode(JSON.stringify(fullPayload));
    
    const signature = await this.sign256(`${encodedHeader}.${encodedPayload}`);
    
    return `${encodedHeader}.${encodedPayload}.${signature}`;
  }

  /**
   * 验证 JWT Token
   */
  async verify(token: string): Promise<JWTPayload> {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new ApiError('Invalid token format', 401);
    }

    const [encodedHeader, encodedPayload, signature] = parts;
    
    // 验证签名
    const expectedSignature = await this.sign256(`${encodedHeader}.${encodedPayload}`);
    if (signature !== expectedSignature) {
      throw new ApiError('Invalid token signature', 401);
    }

    // 解析载荷
    const payload: JWTPayload = JSON.parse(this.base64UrlDecode(encodedPayload));
    
    // 检查过期时间
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      throw new ApiError('Token has expired', 401);
    }

    return payload;
  }

  /**
   * 从用户信息生成 Token
   */
  async generateUserToken(user: User): Promise<string> {
    return this.sign({
      userId: user.id,
      email: user.email,
      role: user.role,
    });
  }

  /**
   * Base64 URL 编码
   */
  private base64UrlEncode(str: string): string {
    const encoder = new TextEncoder();
    const data = encoder.encode(str);
    const base64 = btoa(String.fromCharCode(...data));
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }

  /**
   * Base64 URL 解码
   */
  private base64UrlDecode(str: string): string {
    // 添加填充
    str += '='.repeat((4 - str.length % 4) % 4);
    // 替换字符
    str = str.replace(/-/g, '+').replace(/_/g, '/');
    
    const binary = atob(str);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    
    const decoder = new TextDecoder();
    return decoder.decode(bytes);
  }

  /**
   * HMAC SHA256 签名
   */
  private async sign256(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(this.secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
    const signatureArray = Array.from(new Uint8Array(signature));
    const base64 = btoa(String.fromCharCode(...signatureArray));
    
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }
}

/**
 * 从请求头中提取 Token
 */
export function extractTokenFromHeader(request: Request): string | null {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.slice(7);
}

/**
 * 验证用户权限
 */
export function hasPermission(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'user': 0,
    'collaborator': 1,
    'admin': 2,
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] ?? -1;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] ?? 999;

  return userLevel >= requiredLevel;
}

/**
 * 检查是否为管理员邮箱
 */
export function isAdminEmail(email: string, adminEmails: string[]): boolean {
  return adminEmails.includes(email);
}

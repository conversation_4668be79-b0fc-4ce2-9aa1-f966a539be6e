{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "upload": "Upload", "download": "Download", "copy": "Copy", "share": "Share", "back": "Back", "next": "Next", "previous": "Previous", "home": "Home", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "settings": "Settings", "admin": "Admin", "dashboard": "Dashboard"}, "navigation": {"home": "Home", "articles": "Articles", "archive": "Archive", "now": "Now", "guestbook": "Guestbook", "integrations": "Integrations", "business": "Business", "files": "Files", "profile": "Profile"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "signInWith": "Sign in with {provider}", "welcomeBack": "Welcome Back", "signInToAccess": "Sign in to {siteName}", "authFailed": "Authentication Failed", "authError": "There was a problem during authentication", "accessDenied": "Access Denied", "noPermission": "You don't have permission to access this page", "needHelp": "Need help?", "contactAdmin": "Contact Administrator"}, "articles": {"title": "Articles", "subtitle": "Explore our curated collection of technical articles and insights", "searchPlaceholder": "Search articles...", "allCategories": "All Categories", "allStatuses": "All Statuses", "published": "Published", "draft": "Draft", "archived": "Archived", "noArticles": "No Articles", "noArticlesFound": "No articles found matching your criteria", "notPublished": "No articles have been published yet", "readingTime": "{minutes} min read", "viewCount": "{count} views", "likeCount": "{count} likes", "aiSummary": "AI Summary", "articleEnd": "End of Article", "tableOfContents": "Table of Contents", "readingProgress": "Reading Progress", "shareArticle": "Share Article", "copyLink": "Copy Link", "relatedArticles": "Related Articles"}, "dashboard": {"stats": {"articles": "Articles", "totalViews": "Total Views", "users": "Users", "files": "Files", "published": "Published", "draft": "Draft", "admins": "Admins", "collaborators": "Collaborators", "images": "Images", "uniqueVisitors": "Unique Visitors", "contentStats": "Content Statistics", "contentStatsDesc": "Article publishing and management status", "viewStats": "View Statistics", "viewStatsDesc": "Website views and visitor data", "publishedArticles": "Published Articles", "articlesViewed": "Articles Viewed", "recentArticles": "Recent Articles", "recentViews": "Recent Views", "popularArticles": "Popular Articles", "popularArticlesDesc": "Most viewed articles", "views": "views", "noData": "No data available"}}, "files": {"title": "File Management", "subtitle": "Upload, manage and organize your media files", "uploadFiles": "Upload Files", "uploading": "Uploading...", "searchFiles": "Search files...", "allTypes": "All Types", "images": "Images", "videos": "Videos", "audio": "Audio", "documents": "Documents", "gridView": "Grid View", "listView": "List View", "noFiles": "No Files", "noFilesFound": "No files found matching your criteria", "uploadFirst": "Upload your first file", "public": "Public", "private": "Private", "copyUrl": "Copy URL", "fileSize": "File Size", "uploadedAt": "Uploaded", "statistics": {"totalFiles": "Total Files", "imageFiles": "Image Files", "documentFiles": "Document Files", "totalSize": "Total Size"}}, "editor": {"bold": "Bold", "italic": "Italic", "strikethrough": "Strikethrough", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "bulletList": "Bullet List", "numberedList": "Numbered List", "quote": "Quote", "link": "Link", "image": "Image", "code": "Code", "undo": "Undo", "redo": "Redo", "search": "Search", "edit": "Edit", "split": "Split", "previewMode": "Preview Mode", "editMode": "Edit Mode", "splitView": "Split View", "settings": "Settings", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen", "save": "Save", "words": "Words", "characters": "Characters", "lines": "Lines", "readingTime": "Reading Time", "minutes": "minutes", "lastSaved": "Last Saved", "autoSave": "Auto Save", "searchAndReplace": "Search and Replace", "searchPlaceholder": "Search content...", "replacePlaceholder": "Replace with...", "findNext": "Find Next", "replaceAll": "Replace All", "editorSettings": "Editor Settings", "fontSize": "Font Size", "lineHeight": "Line Height", "wordWrap": "Word Wrap", "spellCheck": "Spell Check", "autoComplete": "Auto Complete"}, "profile": {"title": "Profile", "subtitle": "Manage your account information and preferences", "basicInfo": "Basic Information", "basicInfoDesc": "Your account basic information", "userId": "User ID", "role": "Role", "permissions": "Permissions", "permissionsDesc": "Your permission level in the system", "viewArticles": "View Articles", "createArticles": "Create Articles", "deleteArticles": "Delete Articles", "fileManagement": "File Management", "systemAdmin": "System Administration", "allowed": "✓ Allowed", "denied": "✗ Denied", "roles": {"admin": "Administrator", "collaborator": "Collaborator", "user": "User"}}, "errors": {"notFound": "Page Not Found", "articleNotFound": "Article Not Found", "articleNotFoundDesc": "Sorry, the article you're looking for doesn't exist or has been deleted", "possibleReasons": "Possible reasons:", "wrongLink": "Incorrect article link", "articleDeleted": "Article has been deleted", "articleNotPublished": "Article is not yet published", "backToArticles": "Back to Articles", "backToHome": "Back to Home", "serverError": "Server Error", "somethingWrong": "Something went wrong", "tryAgain": "Please try again later"}, "footer": {"description": "Modern blog system built with Next.js", "quickLinks": "Quick Links", "features": "Features", "other": "Other", "builtWith": "Built with ❤️ using Next.js, Tailwind CSS, and Shadcn UI", "allRightsReserved": "All rights reserved"}, "theme": {"toggle": "Toggle theme", "light": "Light mode", "dark": "Dark mode", "system": "System"}, "admin": {"loading": "Loading...", "accessDenied": "Access Denied", "loginRequired": "Please sign in to access the admin panel", "signIn": "Sign In", "insufficientPermissions": "Insufficient Permissions", "adminAccessRequired": "Admin or collaborator permissions required to access this page", "backToHome": "Back to Home", "collaboratorNotice": "You are currently signed in as a collaborator, some features may be limited.", "sidebar": {"title": "Admin Panel", "dashboard": "Dashboard", "content": "Content Management", "articles": "Articles", "pages": "Pages", "categories": "Categories", "tags": "Tags", "media": "Media Files", "users": "User Management", "comments": "Comment Management", "links": "Friend Links", "analytics": "Analytics", "settings": "System Settings", "version": "Version", "lastUpdate": "Last Update"}, "header": {"searchPlaceholder": "Search...", "unknownUser": "Unknown User", "admin": "Admin", "collaborator": "Collaborator", "profile": "Profile", "settings": "Settings", "signOut": "Sign Out"}, "categoryTag": {"title": "Category & Tag Management", "subtitle": "Manage blog categories and tags to organize content structure", "loading": "Loading...", "categories": "Categories", "tags": "Tags", "createCategory": "Create Category", "createTag": "Create Tag", "editCategory": "Edit Category", "editTag": "Edit Tag", "categoryDescription": "Categories are used to organize main content types of articles", "tagDescription": "Tags are used to mark specific topics and keywords of articles", "searchPlaceholder": "Search categories or tags...", "name": "Name", "slug": "URL Slug", "description": "Description", "color": "Color", "articleCount": "Article Count", "updatedAt": "Updated At", "namePlaceholder": "Enter name", "slugPlaceholder": "url-slug", "descriptionPlaceholder": "Enter description (optional)", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "create": "Create", "update": "Update", "confirmDelete": "Are you sure you want to delete this item? This action cannot be undone."}}, "comments": {"comments": "Comments", "newest": "Newest", "oldest": "Oldest", "popular": "Popular", "controversial": "Controversial", "write": "Write", "preview": "Preview", "writeComment": "Write a comment...", "writeReply": "Write a reply...", "replyingTo": "Replying to", "cancel": "Cancel", "comment": "Comment", "reply": "Reply", "share": "Share", "edit": "Edit", "delete": "Delete", "pin": "<PERSON>n", "unpin": "Unpin", "report": "Report", "pinned": "Pinned", "edited": "Edited", "justNow": "Just now", "minutesAgo": "{count} minutes ago", "hoursAgo": "{count} hours ago", "daysAgo": "{count} days ago", "markdownSupported": "<PERSON><PERSON> supported", "mentionWithAt": "Use @ to mention users", "nothingToPreview": "Nothing to preview", "noComments": "No Comments", "beFirstToComment": "Be the first to comment", "commentActions": "Comment Actions", "loginRequired": "Please login first", "loadCommentsFailed": "Failed to load comments", "commentSubmitted": "Comment submitted", "submitCommentFailed": "Failed to submit comment", "likeCommentFailed": "Failed to like comment", "dislikeCommentFailed": "Failed to dislike comment", "commentReported": "Comment reported", "reportCommentFailed": "Failed to report comment", "commentDeleted": "Comment deleted", "deleteCommentFailed": "Failed to delete comment", "commentUpdated": "Comment updated", "editCommentFailed": "Failed to edit comment", "commentPinned": "Comment pinned", "pinCommentFailed": "Failed to pin comment"}, "analytics": {"title": "Analytics", "description": "Deep insights into website performance and user behavior", "analytics": "Analytics", "analyticsDescription": "Comprehensive website analytics and insights", "dashboard": "Dashboard", "seoOptimization": "SEO Optimization", "keywordResearch": "Keyword Research", "sitemap": "Sitemap", "totalViews": "Total Views", "uniqueVisitors": "Unique Visitors", "avgSessionDuration": "Avg Session Duration", "bounceRate": "Bounce Rate", "fromLastPeriod": "from last period", "traffic": "Traffic", "sources": "Sources", "content": "Content", "audience": "Audience", "realtime": "Real-time", "trafficTrend": "Traffic Trend", "views": "Views", "visitors": "Visitors", "sessions": "Sessions", "trafficSources": "Traffic Sources", "deviceTypes": "Device Types", "topPages": "Top Pages", "avgTime": "Avg Time", "geography": "Geography", "activeUsers": "Active Users", "currentlyOnline": "currently online", "recentActivity": "Recent Activity", "noData": "No Data", "noDataDescription": "Not enough data to display analytics results", "loadDataFailed": "Failed to load data", "export": "Export", "exportSuccess": "Export successful", "exportFailed": "Export failed", "seoTips": "SEO Tips", "tip1Title": "Optimize Title Tags", "tip1Description": "Ensure each page has unique and descriptive title tags", "tip2Title": "Improve Page Speed", "tip2Description": "Fast-loading pages provide better user experience", "tip3Title": "Create Quality Content", "tip3Description": "Regularly publish valuable original content", "topCountries": "Top Countries"}, "toast": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "loading": "Loading..."}, "api": {"loading": "Processing request...", "success": "Request successful", "error": "Request failed"}, "form": {"submitSuccess": "Submitted successfully", "submitError": "Submission failed", "validationError": "Form validation failed"}, "fileUpload": {"uploading": "Uploading", "uploadSuccess": "Upload successful", "uploadError": "Upload failed"}, "copy": {"success": "<PERSON><PERSON>d successfully", "error": "Co<PERSON> failed"}, "search": {"title": "Search", "placeholder": "Search articles...", "inputPlaceholder": "Search articles, tags or keywords...", "search": "Search", "searchFailed": "Search failed", "noResults": "No results found", "noResultsDesc": "No content found related to \"{query}\"", "noResultsDescription": "No content found related to \"{query}\", please try other keywords", "results": "results", "resultsFor": "Search results: {query}", "resultsTitle": "Search: {query}", "resultsDescription": "Search content related to \"{query}\"", "resultsCount": "Found {count} results related to \"{query}\"", "viewAllResults": "View all search results for \"{query}\"", "recentSearches": "Recent searches", "suggestions": "Search suggestions", "clearHistory": "Clear history", "filters": "Filters", "activeFilters": "Active filters", "clearAll": "Clear all", "contentType": "Content type", "allTypes": "All types", "articles": "Articles", "tags": "Tags", "categories": "Categories", "users": "Users", "category": "Category", "tag": "Tag", "author": "Author", "date": "Date", "type": "Type", "selectCategory": "Select category", "allCategories": "All categories", "selectTag": "Select tag", "allTags": "All tags", "selectAuthor": "Select author", "allAuthors": "All authors", "dateRange": "Date range", "selectDateRange": "Select date range", "allTime": "All time", "today": "Today", "thisWeek": "This week", "thisMonth": "This month", "thisYear": "This year", "page": "Page", "previousPage": "Previous", "nextPage": "Next", "moreTags": "more tags", "startSearching": "Start searching", "startSearchingDescription": "Enter keywords to search articles, tags and other content", "searchTips": "Search tips", "tip1": "Use quotes to search exact phrases", "tip2": "Use + to include required words", "tip3": "Use - to exclude specific words", "tip4": "Use tags and categories to filter results", "searchSuggestions": "Search suggestions", "suggestion1": "Check if spelling is correct", "suggestion2": "Try using more general keywords", "suggestion3": "Use different keyword combinations", "searchDescription": "Search content related to \"{query}\""}, "imageUpload": {"insertImage": "Insert Image", "upload": "Upload", "fromUrl": "From URL", "gallery": "Gallery", "dragDropOrClick": "Drag and drop files here or click to upload", "supportedFormats": "Supported formats", "maxFileSize": "Max file size", "invalidFileType": "Invalid file type", "fileTooLarge": "File size exceeds {size}MB", "uploadFailed": "Upload failed", "success": "Success", "error": "Error", "imageUrl": "Image URL", "altText": "Alt Text", "altTextPlaceholder": "Describe the image content...", "title": "Title", "optional": "optional", "titlePlaceholder": "Image title...", "insertFromUrl": "<PERSON><PERSON><PERSON> from URL", "noImagesYet": "No images yet", "uploadFirstImage": "Upload your first image", "insert": "Insert", "imageDetails": "Image Details", "filename": "Filename", "dimensions": "Dimensions", "fileSize": "File Size", "url": "URL", "copyUrl": "Copy URL", "urlCopied": "URL copied", "copyFailed": "Co<PERSON> failed", "pleaseEnterUrl": "Please enter image URL", "image": "Image"}, "versionControl": {"versionHistory": "Version History", "versions": "versions", "compare": "Compare", "createVersion": "Create Version", "createNewVersion": "Create New Version", "versionSummary": "Version Summary", "versionSummaryPlaceholder": "Describe the changes in this version...", "version": "Version", "author": "Author", "date": "Date", "words": "Words", "status": "Status", "all": "All", "allAuthors": "All Authors", "draft": "Draft", "published": "Published", "archived": "Archived", "noVersions": "No Versions", "noVersionsDescription": "No versions have been created yet", "restore": "Rest<PERSON>", "preview": "Preview", "justNow": "Just now", "minutesAgo": "{count} minutes ago", "hoursAgo": "{count} hours ago", "daysAgo": "{count} days ago", "compareVersions": "Compare Versions", "contentDifferences": "Content Differences", "noChanges": "No changes", "versionCreated": "Version created", "createVersionFailed": "Failed to create version", "versionRestored": "Version restored", "restoreVersionFailed": "Failed to restore version", "compareVersionsFailed": "Failed to compare versions", "loadVersionsFailed": "Failed to load versions", "title": "Title", "contentPreview": "Content Preview"}, "draftManager": {"draftManager": "Draft Manager", "drafts": "drafts", "deleteSelected": "Delete Selected", "saveDraft": "Save Draft", "autoSave": "Auto Save", "interval": "Interval", "enabled": "Enabled", "disabled": "Disabled", "searchDrafts": "Search drafts...", "allStatuses": "All Statuses", "autoSaved": "Auto Saved", "manualSaved": "Manual Saved", "lastSaved": "Last Saved", "title": "Title", "wordCount": "Word Count", "noDrafts": "No Drafts", "noDraftsDescription": "No drafts have been saved yet", "untitled": "Untitled", "recoverable": "Recoverable", "loadDraft": "Load Draft", "restoreDraft": "Restore Draft", "previewDraft": "Preview Draft", "deleteDraft": "Delete Draft", "draftSaved": "Draft saved", "saveDraftFailed": "Failed to save draft", "draftDeleted": "Draft deleted", "deleteDraftFailed": "Failed to delete draft", "draftsDeleted": "Deleted {count} drafts", "deleteDraftsFailed": "Failed to delete drafts", "draftRestored": "Draft restored", "loadDraftsFailed": "Failed to load drafts"}, "publishScheduler": {"publishScheduler": "Publish Scheduler", "scheduled": "Scheduled", "schedulePost": "Schedule Post", "editSchedule": "Edit Schedule", "noScheduledPosts": "No Scheduled Posts", "noScheduledPostsDescription": "No posts have been scheduled for publication yet", "publishDateTime": "Publish Date & Time", "visibility": "Visibility", "public": "Public", "unlisted": "Unlisted", "private": "Private", "basic": "Basic", "seo": "SEO", "social": "Social", "titlePlaceholder": "Article title...", "seoTitle": "SEO Title", "seoTitlePlaceholder": "Title displayed in search engines...", "seoDescription": "SEO Description", "seoDescriptionPlaceholder": "Description displayed in search engines...", "socialMediaSharing": "Social Media Sharing", "shareToTwitter": "Share to Twitter", "shareToFacebook": "Share to Facebook", "shareToLinkedIn": "Share to LinkedIn", "notifications": "Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "updateSchedule": "Update Schedule", "publishNow": "Publish Now", "cancelSchedule": "Cancel Schedule", "delete": "Delete", "preview": "Preview", "socialMediaEnabled": "Social media enabled", "pleaseSelectDateTime": "Please select publish date and time", "scheduleDateMustBeFuture": "Schedule date must be in the future", "postScheduled": "Post scheduled", "schedulePostFailed": "Failed to schedule post", "scheduleUpdated": "Schedule updated", "updateScheduleFailed": "Failed to update schedule", "scheduleCancelled": "Schedule cancelled", "cancelScheduleFailed": "Failed to cancel schedule", "postPublished": "Post published", "publishNowFailed": "Failed to publish now", "loadScheduledPostsFailed": "Failed to load scheduled posts"}, "contentWorkflow": {"contentWorkflow": "Content Workflow", "pending": "Pending", "submitForReview": "Submit for Review", "workflowSettings": "Workflow Settings", "allStatuses": "All Statuses", "inReview": "In Review", "approved": "Approved", "rejected": "Rejected", "published": "Published", "allAssignees": "All Assignees", "assignedToMe": "Assigned to Me", "unassigned": "Unassigned", "noWorkflowItems": "No Workflow Items", "noWorkflowItemsDescription": "No content has been submitted for review yet", "review": "Review", "viewDetails": "View Details", "unassign": "Unassign", "approveItem": "Approve Item", "rejectItem": "Reject Item", "approve": "Approve", "reject": "Reject", "approvalComment": "Approval Comment", "rejectionComment": "Rejection Comment", "approvalCommentPlaceholder": "Add approval comment (optional)...", "rejectionCommentPlaceholder": "Please explain the reason for rejection...", "cancel": "Cancel", "content": "Content", "comments": "Comments", "history": "History", "workflowHistoryPlaceholder": "Workflow history will be displayed here", "workflowSettingsDescription": "Configure content review workflow", "submittedForReview": "Submitted for review", "submitForReviewFailed": "Failed to submit for review", "itemApproved": "Item approved", "itemRejected": "Item rejected", "rejectionCommentRequired": "Rejection comment is required", "reviewFailed": "Review failed", "itemAssigned": "Item assigned", "assignFailed": "Failed to assign", "loadWorkflowItemsFailed": "Failed to load workflow items", "low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "articleEditor": {"editArticle": "Edit Article", "newArticle": "New Article", "unsavedChanges": "Unsaved Changes", "draft": "Draft", "published": "Published", "scheduled": "Scheduled", "lastSaved": "Last Saved", "exitPreview": "Exit Preview", "preview": "Preview", "save": "Save", "publish": "Publish", "submitForReview": "Submit for Review", "titlePlaceholder": "Enter article title...", "selectCategory": "Select Category", "technology": "Technology", "design": "Design", "business": "Business", "lifestyle": "Lifestyle", "addTag": "Add Tag", "contentPlaceholder": "Start writing...", "untitled": "Untitled", "noContent": "No content", "tools": "Tools", "articleSaved": "Article saved", "saveArticleFailed": "Failed to save article", "articlePublished": "Article published", "publishArticleFailed": "Failed to publish article", "submittedForReview": "Submitted for review", "submitForReviewFailed": "Failed to submit for review"}, "followSystem": {"follow": "Follow", "following": "Following", "followers": "Followers", "followBack": "Follow Back", "unfollow": "Unfollow", "verified": "Verified", "articles": "Articles", "mutualFollowers": "Mutual Followers", "lastActive": "Last Active", "neverActive": "Never Active", "activeNow": "Active Now", "activeHoursAgo": "Active {count} hours ago", "activeDaysAgo": "Active {count} days ago", "activeWeeksAgo": "Active weeks ago", "suggestedUsers": "Suggested Users", "userActions": "User Actions", "blockUser": "Block User", "searchUsers": "Search Users", "noUsersFound": "No users found", "noUsers": "No users", "enableNotifications": "Enable Notifications", "disableNotifications": "Disable Notifications", "followSuccess": "Followed successfully", "followFailed": "Failed to follow", "unfollowSuccess": "Unfollowed successfully", "unfollowFailed": "Failed to unfollow", "blockSuccess": "Blocked successfully", "blockFailed": "Failed to block", "notificationsEnabled": "Notifications enabled", "notificationsDisabled": "Notifications disabled", "notificationToggleFailed": "Failed to toggle notifications"}, "articleInteractions": {"likes": "<PERSON>s", "bookmarks": "Bookmarks", "share": "Share", "comments": "Comments", "views": "Views", "shareArticle": "Share Article", "shareOn": "Share on", "email": "Email", "copyLink": "Copy Link", "reportArticle": "Report Article", "reportReason": "Report Reason", "reportDescription": "Description", "reportDescriptionPlaceholder": "Please describe the reason for reporting...", "cancel": "Cancel", "submitReport": "Submit Report", "reportReason.spam": "Spam", "reportReason.harassment": "Harassment", "reportReason.inappropriate": "Inappropriate Content", "reportReason.copyright": "Copyright Violation", "reportReason.misinformation": "Misinformation", "reportReason.other": "Other", "loginRequired": "Please login first", "likeSuccess": "Liked successfully", "unlikeSuccess": "Unlike<PERSON> successfully", "likeFailed": "Failed to like", "bookmarkSuccess": "Bookmarked successfully", "unbookmarkSuccess": "Unbookmarked successfully", "bookmarkFailed": "Failed to bookmark", "linkCopied": "Link copied", "copyFailed": "Failed to copy", "reportSuccess": "Reported successfully", "reportFailed": "Failed to report", "reportReasonRequired": "Please select a reason"}, "activityTimeline": {"activityFeed": "Activity Feed", "allActivities": "All Activities", "articles": "Articles", "interactions": "Interactions", "follows": "Follows", "comments": "Comments", "justNow": "Just now", "minutesAgo": "{count} minutes ago", "hoursAgo": "{count} hours ago", "daysAgo": "{count} days ago", "noMoreActivities": "No more activities", "noActivities": "No Activities", "noActivitiesDescription": "Follow some users to see their activities", "loadActivitiesFailed": "Failed to load activities", "activityDescription.articlePublished": "{user} published article \"{article}\"", "activityDescription.articleLiked": "{user} liked article \"{article}\"", "activityDescription.articleBookmarked": "{user} bookmarked article \"{article}\"", "activityDescription.articleShared": "{user} shared article \"{article}\" on {platform}", "activityDescription.userFollowed": "{user} followed {target}", "activityDescription.commentPosted": "{user} commented on article \"{article}\"", "activityDescription.articleUpdated": "{user} updated article \"{article}\"", "activityDescription.milestoneReached": "{user} reached milestone: {milestone} ({count})", "activityDescription.unknown": "Unknown activity"}, "messaging": {"messages": "Messages", "searchConversations": "Search conversations", "noConversationsFound": "No conversations found", "noConversations": "No conversations", "selectConversation": "Select Conversation", "selectConversationDescription": "Choose a conversation to start chatting", "online": "Online", "lastSeen": "Last seen: {time}", "typing": "Typing...", "typeMessage": "Type a message...", "replyingTo": "Replying to", "replyTo": "Reply to", "edited": "Edited", "sendMessageFailed": "Failed to send message", "uploadFailed": "Upload failed", "noMessages": "No messages"}, "social": {"title": "Social Hub", "description": "Interact with other users and follow interesting content creators", "followers": "Followers", "following": "Following", "likes": "<PERSON>s", "bookmarks": "Bookmarks", "shares": "Shares", "comments": "Comments", "timeline": "Timeline", "messages": "Messages", "trending": "Trending", "activityFeed": "Activity Feed", "suggestedUsers": "Suggested Users", "followingManagement": "Following Management", "messaging": "Messaging", "trendingActivities": "Trending Activities", "popularUsers": "Popular Users"}, "notifications": {"notifications": "Notifications", "unread": "Unread", "markAllRead": "<PERSON> as <PERSON>", "notificationSettings": "Notification Settings", "all": "All", "interactions": "Interactions", "system": "System", "noNotifications": "No notifications", "markAsRead": "<PERSON> <PERSON>", "delete": "Delete", "justNow": "Just now", "minutesAgo": "{count} minutes ago", "hoursAgo": "{count} hours ago", "daysAgo": "{count} days ago", "notificationDeleted": "Notification deleted", "deleteNotificationFailed": "Failed to delete notification", "settingsUpdated": "Settings updated", "updateSettingsFailed": "Failed to update settings", "notificationTypes": "Notification Types", "notificationMethods": "Notification Methods", "notificationFrequency": "Notification Frequency", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "immediate": "Immediate", "hourly": "Hourly", "daily": "Daily", "weekly": "Weekly", "notificationType.likes": "<PERSON>s", "notificationType.comments": "Comments", "notificationType.follows": "Follows", "notificationType.shares": "Shares", "notificationType.bookmarks": "Bookmarks", "notificationType.mentions": "Mentions", "notificationType.system": "System Notifications"}, "seo": {"seoAnalyzer": "SEO Analyzer", "targetUrl": "Target URL", "targetKeywords": "Target Keywords", "keywordsPlaceholder": "keyword1, keyword2, keyword3", "analyzing": "Analyzing", "analyzeSEO": "Analyze SEO", "exportReport": "Export Report", "urlRequired": "Please enter URL", "analysisComplete": "Analysis complete", "analysisFailed": "Analysis failed", "suggestionApplied": "Suggestion applied", "applySuggestionFailed": "Failed to apply suggestion", "reportExported": "Report exported", "exportFailed": "Export failed", "seoScore": "SEO Score", "overallPerformance": "Overall Performance", "outOf100": "out of 100", "errors": "Errors", "warnings": "Warnings", "suggestions": "Suggestions", "issues": "Issues", "keywords": "Keywords", "metadata": "<PERSON><PERSON><PERSON>", "performance": "Performance", "impact.high": "High Impact", "impact.medium": "Medium Impact", "impact.low": "Low Impact", "howToFix": "How to Fix", "density": "Density", "position": "Position", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "volume": "Volume", "trend": "Trend", "trend.up": "Up", "trend.down": "Down", "trend.stable": "Stable", "keywordSuggestions": "Keyword Suggestions", "titleTag": "Title Tag", "metaDescription": "Meta Description", "currentTitle": "Current Title", "currentDescription": "Current Description", "length": "Length", "characters": "characters", "optimal": "Optimal", "needsImprovement": "Needs Improvement", "coreWebVitals": "Core Web Vitals", "lcp": "Largest Contentful Paint", "fid": "First Input Delay", "cls": "Cumulative Layout Shift", "pagespeedScores": "PageSpeed Scores", "mobileScore": "Mobile Score", "desktopScore": "Desktop Score", "implementation": "Implementation", "estimatedImpact": "Estimated Impact", "apply": "Apply", "priority.high": "High Priority", "priority.medium": "Medium Priority", "priority.low": "Low Priority"}, "sitemap": {"sitemapGenerator": "Sitemap Generator", "sitemapDescription": "Automatically generate and manage website sitemaps", "generating": "Generating", "generateNow": "Generate Now", "submitToSearchEngines": "Submit to Search Engines", "totalUrls": "Total URLs", "fileSize": "File Size", "lastGenerated": "Last Generated", "status": "Status", "status.generating": "Generating", "status.success": "Success", "status.error": "Error", "files": "Files", "configuration": "Configuration", "customUrls": "Custom URLs", "urls": "URLs", "fileStatus.active": "Active", "fileStatus.outdated": "Outdated", "fileStatus.error": "Error", "sitemapConfiguration": "Sitemap Configuration", "basicSettings": "Basic Settings", "enableSitemap": "Enable Sitemap", "autoGenerate": "Auto Generate", "contentTypes": "Content Types", "includeImages": "Include Images", "includeNews": "Include News", "includeVideos": "Include Videos", "defaultChangeFreq": "Default Change Frequency", "defaultPriority": "Default Priority", "excludePaths": "Exclude Paths", "always": "Always", "hourly": "Hourly", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "never": "Never", "addUrl": "Add URL", "remove": "Remove", "noCustomUrls": "No custom URLs", "loadDataFailed": "Failed to load data", "generateSuccess": "Generated successfully", "generateFailed": "Generation failed", "configUpdated": "Configuration updated", "updateConfigFailed": "Failed to update configuration", "downloadFailed": "Download failed", "submitSuccess": "Submitted successfully", "submitFailed": "Submission failed"}, "keywordResearch": {"keywordResearch": "Keyword Research", "searchPlaceholder": "Enter seed keywords...", "searching": "Searching", "search": "Search", "queryRequired": "Please enter search query", "searchComplete": "Search complete", "searchFailed": "Search failed", "competitorAnalysisFailed": "Competitor analysis failed", "selectKeywordsFirst": "Please select keywords first", "group": "Group", "groupCreated": "Group created", "exportSuccess": "Export successful", "exportFailed": "Export failed", "searchVolume": "Search Volume", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchIntent": "Search Intent", "allIntents": "All Intents", "informational": "Informational", "navigational": "Navigational", "transactional": "Transactional", "commercial": "Commercial", "createGroup": "Create Group", "export": "Export", "keywords": "Keywords", "groups": "Groups", "competitors": "Competitors", "relatedKeywords": "Related Keywords", "questionSuggestions": "Question Suggestions", "intent.informational": "Informational", "intent.navigational": "Navigational", "intent.transactional": "Transactional", "intent.commercial": "Commercial", "noKeywords": "No Keywords", "noKeywordsDescription": "Enter keywords to start searching", "totalVolume": "Total Volume", "avgDifficulty": "Avg <PERSON>", "noGroups": "No Groups", "noGroupsDescription": "Select keywords to create groups", "addCompetitor": "Add Competitor", "analyze": "Analyze", "traffic": "Traffic", "topKeywords": "Top Keywords", "noCompetitors": "No Competitors", "noCompetitorsDescription": "Enter domain to analyze competitors"}, "structuredData": {"structuredDataGenerator": "Structured Data Generator", "structuredDataDescription": "Create and manage structured data markup for your web pages", "selectTemplate": "Select Template", "selectTemplatePlaceholder": "Choose structured data type", "fillDetails": "Fill Details", "targetUrl": "Target URL", "generateCode": "Generate Code", "generatedCode": "Generated Code", "copy": "Copy", "validate": "Validate", "preview": "Preview", "applyToPage": "Apply to <PERSON>", "noCodeGenerated": "No Code Generated", "selectTemplateAndFill": "Select a template and fill in details to generate structured data code", "validationResults": "Validation Results", "validStructuredData": "Valid Structured Data", "validStructuredDataDescription": "Your structured data markup is valid and can be applied to the webpage", "errors": "Errors", "warnings": "Warnings", "true": "True", "false": "False", "structuredDataPreview": "Structured Data Preview", "previewDescription": "Below is the structured data code that will be added to the page", "loadTemplatesFailed": "Failed to load templates", "requiredFieldsMissing": "Required fields missing: {fields}", "generationSuccess": "Generation successful", "generationFailed": "Generation failed", "validationSuccess": "Validation successful", "validationFailed": "Validation failed", "validationError": "Validation error occurred", "applySuccess": "Applied successfully", "applyFailed": "Application failed", "codeCopied": "Code copied", "copyFailed": "Co<PERSON> failed"}, "newsletter": {"subscribeToNewsletter": "Subscribe to Newsletter", "newsletterDescription": "Get the latest articles and updates", "name": "Name", "email": "Email", "namePlaceholder": "Enter your name", "emailPlaceholder": "Enter your email", "preferredLanguage": "Preferred Language", "interests": "Interests", "subscribe": "Subscribe", "thankYou": "Thank You!", "subscribeConfirmation": "You have successfully subscribed to our newsletter", "privacyNotice": "We respect your privacy and won't share your email address", "emailRequired": "Please enter your email address", "subscribeSuccess": "Subscription successful", "subscribeFailed": "Subscription failed", "newsletterStats": "Newsletter Stats", "subscribers": "Subscribers", "openRate": "Open Rate", "clickRate": "Click Rate", "unsubscribeRate": "Unsubscribe Rate", "lastCampaign": "Last Campaign", "opens": "Opens", "clicks": "<PERSON>licks", "newsletterSettings": "Newsletter Settings", "general": "General", "provider": "Provider", "fields": "Fields", "doubleOptIn": "Double Opt-in", "welcomeEmail": "Welcome Email", "frequency": "Frequency", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "apiKey": "API Key", "listId": "List ID", "captureFields": "Capture Fields", "tags": "Tags", "language": "Language", "custom": "Custom", "configUpdated": "Configuration updated", "updateConfigFailed": "Failed to update configuration", "tag.technology": "Technology", "tag.design": "Design", "tag.development": "Development", "tag.business": "Business"}, "socialMedia": {"connectSocialAccounts": "Connect Social Accounts", "twitter": "Twitter", "facebook": "Facebook", "linkedin": "LinkedIn", "instagram": "Instagram", "youtube": "YouTube", "github": "GitHub", "connect": "Connect", "disconnect": "Disconnect", "connectSuccess": "Connected successfully", "connectFailed": "Connection failed", "disconnectSuccess": "Disconnected successfully", "disconnectFailed": "Disconnection failed", "settingsUpdated": "Settings updated", "updateSettingsFailed": "Failed to update settings", "selectPlatform": "Please select a platform", "shareSuccess": "Shared successfully", "shareFailed": "Sharing failed", "linkCopied": "Link copied", "copyFailed": "Co<PERSON> failed", "connectedAccounts": "Connected Accounts", "refresh": "Refresh", "autoShare": "Auto Share", "noConnectedAccounts": "No Connected Accounts", "noConnectedAccountsDescription": "Connect social media accounts to automatically share content", "connectAccount": "Connect Account", "recentPosts": "Recent Posts", "noPosts": "No Posts", "noPostsDescription": "No content has been posted to social media yet", "shareContent": "Share Content", "selectPlatforms": "Select Platforms", "customMessage": "Custom Message", "customMessagePlaceholder": "Add a custom message...", "share": "Share", "embedSocialFeeds": "Embed Social Feeds", "twitterEmbedDescription": "Embed Twitter timeline", "instagramEmbedDescription": "Embed Instagram posts", "youtubeEmbedDescription": "Embed YouTube videos", "generateEmbed": "Generate Embed", "copyLink": "Copy Link", "view": "View"}, "plugins": {"pluginManager": "Plugin Manager", "pluginManagerDescription": "Manage and extend blog system functionality", "marketplace": "Marketplace", "installed": "Installed", "searchPlugins": "Search plugins", "allCategories": "All Categories", "allPlugins": "All Plugins", "notInstalled": "Not Installed", "by": "by", "category.content": "Content", "category.ui": "UI", "category.analytics": "Analytics", "category.social": "Social", "category.seo": "SEO", "category.utility": "Utility", "permissions": "Permissions", "permission.read": "Read Access", "permission.write": "Write Access", "permission.admin": "Admin Access", "permission.api": "API Access", "details": "Details", "install": "Install", "uninstall": "Uninstall", "enable": "Enable", "disable": "Disable", "installSuccess": "Installation successful", "installFailed": "Installation failed", "uninstallSuccess": "Uninstallation successful", "uninstallFailed": "Uninstallation failed", "enableSuccess": "Enabled successfully", "disableSuccess": "Disabled successfully", "toggleFailed": "Toggle failed", "updateSuccess": "Update successful", "updateFailed": "Update failed", "configureSuccess": "Configuration successful", "configureFailed": "Configuration failed", "loadPluginsFailed": "Failed to load plugins", "noPluginsFound": "No Plugins Found", "noPluginsFoundDescription": "Try adjusting your search criteria or browse other categories", "noInstalledPlugins": "No Installed Plugins", "noInstalledPluginsDescription": "Browse the marketplace to install features you need", "browseMarketplace": "Browse Marketplace", "uploadPlugin": "Upload Plugin", "uploadPluginDescription": "Upload a custom plugin package (.zip format)", "selectFile": "Select File", "refresh": "Refresh", "description": "Description", "information": "Information", "version": "Version", "author": "Author", "category": "Category", "downloads": "Downloads", "rating": "Rating", "dependencies": "Dependencies", "homepage": "Homepage", "repository": "Repository", "documentation": "Documentation"}, "themes": {"themeCustomizer": "Theme Customizer", "themeCustomizerDescription": "Customize website appearance and styling", "themeName": "Theme Name", "themeNamePlaceholder": "Enter theme name", "themePresets": "Theme Presets", "selectPreset": "Select Preset", "presetApplied": "Preset applied", "exportTheme": "Export Theme", "importTheme": "Import Theme", "saveTheme": "Save Theme", "reset": "Reset", "colors": "Colors", "typography": "Typography", "layout": "Layout", "animations": "Animations", "preview": "Preview", "colors.primary": "Primary", "colors.secondary": "Secondary", "colors.accent": "Accent", "colors.background": "Background", "colors.foreground": "Foreground", "colors.muted": "Muted", "colors.mutedForeground": "Muted Foreground", "colors.border": "Border", "fonts.heading": "Heading Font", "fonts.body": "Body Font", "fonts.mono": "Monospace Font", "fontPreview": "Font preview text", "spacing": "Spacing", "spacing.containerWidth": "Container <PERSON><PERSON><PERSON>", "spacing.gapSmall": "Small Gap", "spacing.gapMedium": "Medium Gap", "spacing.gapLarge": "Large Gap", "borderRadius": "Border Radius", "borderRadius.small": "Small Radius", "borderRadius.medium": "Medium Radius", "borderRadius.large": "Large Radius", "animations.enabled": "Enable Animations", "animations.duration": "Animation Duration", "livePreview": "Live Preview", "hidePreview": "Hide Preview", "showPreview": "Show Preview", "previewHeading": "This is a heading style", "previewSubheading": "This is a subheading style", "primaryButton": "Primary Button", "secondaryButton": "Secondary Button", "outlineButton": "Outline Button", "previewText": "This is preview text showing the current theme styling effects.", "accentBox": "This is an accent box for highlighting important content.", "generatedCSS": "Generated CSS", "copyCode": "Copy Code", "loadConfigFailed": "Failed to load configuration", "configSaved": "Configuration saved", "saveFailed": "Save failed", "configReset": "Configuration reset", "configImported": "Configuration imported", "importFailed": "Import failed"}, "videoPlayer": {"browserNotSupported": "Your browser does not support video playback", "videoError": "Video failed to load", "errorOccurred": "An error occurred", "fullscreenError": "Fullscreen mode failed", "subtitles": "Subtitles", "quality": "Quality", "off": "Off", "video": "Video", "checkOutVideo": "Check out this video", "linkCopied": "Link copied", "copyFailed": "Co<PERSON> failed"}, "audioPlayer": {"audioError": "Audio failed to load", "playbackError": "Playback failed", "playbackSpeed": "Playback speed: {speed}x", "linkCopied": "Link copied", "copyFailed": "Co<PERSON> failed", "playlist": "Playlist", "fontPreview": "Font preview text"}, "imageGallery": {"image": "Image", "checkOutImage": "Check out this image", "linkCopied": "Link copied", "copyFailed": "Co<PERSON> failed"}, "webhooks": {"webhookManager": "Webhook Manager", "webhookManagerDescription": "Manage event notifications and third-party integrations", "webhooks": "Webhooks", "events": "Events", "createWebhook": "Create Webhook", "editWebhook": "Edit Webhook", "name": "Name", "url": "URL", "webhookName": "Webhook Name", "active": "Active", "inactive": "Inactive", "successCount": "Success Count", "failureCount": "Failure Count", "lastTriggered": "Last Triggered", "retryCount": "Retry Count", "timeout": "Timeout", "secret": "Secret", "optionalSecret": "Optional Secret", "attempts": "Attempts", "eventDetails": "Event Details", "payload": "Payload", "response": "Response", "webhookCreated": "Webhook created", "webhookUpdated": "Webhook updated", "webhookDeleted": "Webhook deleted", "webhookEnabled": "Webhook enabled", "webhookDisabled": "Webhook disabled", "webhookTestSuccess": "Test successful", "webhookTestFailed": "Test failed", "eventRetrySuccess": "Retry successful", "eventRetryFailed": "Retry failed", "loadWebhooksFailed": "Failed to load webhooks", "saveWebhookFailed": "Failed to save webhook", "deleteWebhookFailed": "Failed to delete webhook", "toggleWebhookFailed": "Failed to toggle webhook", "urlCopied": "URL copied", "copyFailed": "Co<PERSON> failed", "noWebhooks": "No Webhooks", "noWebhooksDescription": "Create webhooks to receive event notifications", "createFirstWebhook": "Create First Webhook", "noEvents": "No Events", "noEventsDescription": "When events are triggered, they will appear here", "refresh": "Refresh", "cancel": "Cancel", "create": "Create", "update": "Update", "events.article.created": "Article Created", "events.article.updated": "Article Updated", "events.article.deleted": "Article Deleted", "events.article.published": "Article Published", "events.comment.created": "Comment Created", "events.comment.updated": "Comment Updated", "events.comment.deleted": "Comment Deleted", "events.user.registered": "User Registered", "events.user.updated": "User Updated", "events.user.deleted": "User Deleted", "events.subscription.created": "Subscription Created", "events.subscription.cancelled": "Subscription Cancelled", "status.pending": "Pending", "status.success": "Success", "status.failed": "Failed", "status.retrying": "Retrying"}, "integrations": {"title": "Extensions and Integrations", "description": "Explore powerful extension features for the blog system, including third-party service integrations, plugin system, multimedia support, and automated workflows", "newsletter": {"title": "Newsletter", "description": "Email subscription service integration", "status": "Available", "fullDescription": "Powerful email subscription system with support for multiple providers and custom configurations", "variants": "Subscription Component Variants", "cardVariant": "Card Style", "inlineVariant": "Inline Style", "minimalVariant": "Minimal Style", "features": "Advanced Features"}, "social": {"title": "Social Media", "description": "Social media integration and sharing", "status": "Available", "fullDescription": "Comprehensive social media integration with auto-sharing, account management, and content embedding", "shareButtons": "Share Buttons", "connectAccounts": "Connect Accounts", "dashboard": "Social Dashboard"}, "plugins": {"title": "Plugin System", "description": "Extensible plugin architecture", "status": "Available", "fullDescription": "Flexible plugin system supporting feature extensions, theme customization, and third-party integrations"}, "themes": {"title": "Theme Customization", "description": "Visual theme editor", "status": "Available", "fullDescription": "Powerful theme customization tools with live preview, code generation, and configuration import/export"}, "media": {"title": "Multimedia Support", "description": "Rich media content support", "status": "Available", "fullDescription": "Comprehensive multimedia content support including video player, audio player, and image gallery", "videoPlayer": "Video Player", "audioPlayer": "Audio Player", "imageGallery": "Image Gallery"}, "webhooks": {"title": "Webhook System", "description": "Event notifications and automation", "status": "Available", "fullDescription": "Complete webhook system with event listening, automated workflows, and third-party service integration"}, "tabs": {"newsletter": "Newsletter", "social": "Social", "plugins": "Plugins", "themes": "Themes", "media": "Media", "webhooks": "Webhooks"}}, "business": {"title": "Business Features", "description": "Professional advertising management and data export features to help monetize your blog system", "ads": {"title": "Ad Management", "description": "Comprehensive ad campaign management and revenue optimization", "status": "Available", "fullDescription": "Professional advertising management system with support for multiple ad types, precise targeting, and real-time analytics"}, "placements": {"title": "Ad Placements", "description": "Flexible ad placement configuration and code generation", "status": "Available", "fullDescription": "Visual ad placement management tool with support for multiple sizes, positions, and targeting strategies"}, "export": {"title": "Data Export", "description": "Complete data export and backup solutions", "status": "Available", "fullDescription": "Powerful data export system with support for multiple formats and custom export strategies"}, "migration": {"title": "Data Migration", "description": "Professional data migration and synchronization tools", "status": "Available", "fullDescription": "Enterprise-grade data migration solutions with support for multiple data sources and target systems"}, "revenue": {"title": "Revenue Statistics", "description": "Real-time revenue monitoring and analysis", "thisMonth": "This Month", "lastMonth": "Last Month", "growth": "Growth"}, "performance": {"title": "Performance", "impressions": "Impressions", "clicks": "<PERSON>licks", "ctr": "CTR", "cpm": "CPM"}, "security": {"title": "Data Security", "description": "Enterprise-grade data security protection", "encryption": "Data Encryption", "backup": "Auto Backup", "compliance": "Compliance"}, "enterprise": {"title": "Enterprise Solutions", "description": "Professional business features and technical support for enterprise users to drive business growth and data security", "cloud": {"title": "Cloud Deployment", "description": "Global CDN acceleration and high availability architecture"}, "performance": {"title": "Performance Optimization", "description": "Intelligent caching and load balancing"}, "security": {"title": "Security Protection", "description": "Enterprise-grade security protection and compliance support"}}, "tabs": {"ads": "Ads", "placements": "Placements", "export": "Export", "migration": "Migration"}}, "ads": {"adManager": "Ad Manager", "adManagerDescription": "Manage ad campaigns, monitor revenue, and optimize ad performance", "campaigns": "Campaigns", "analytics": "Analytics", "settings": "Settings", "createCampaign": "Create Campaign", "editCampaign": "Edit Campaign", "campaignCreated": "Campaign created", "campaignUpdated": "Campaign updated", "campaignDeleted": "Campaign deleted", "campaignActivated": "Campaign activated", "campaignPaused": "Campaign paused", "loadCampaignsFailed": "Failed to load campaigns", "saveCampaignFailed": "Failed to save campaign", "deleteCampaignFailed": "Failed to delete campaign", "toggleCampaignFailed": "Failed to toggle campaign status", "noCampaigns": "No Campaigns", "noCampaignsDescription": "Create your first ad campaign to start earning revenue", "createFirstCampaign": "Create First Campaign", "analyticsComingSoon": "Analytics Coming Soon", "analyticsDescription": "Detailed ad performance analysis and optimization recommendations", "settingsComingSoon": "Settings Coming Soon", "settingsDescription": "Ad system configuration and preference settings", "totalRevenue": "Total Revenue", "totalImpressions": "Total Impressions", "totalClicks": "Total Clicks", "averageCTR": "Average CTR", "averageCPM": "Average CPM", "activeCampaigns": "Active Campaigns", "topPerformer": "Top Performer", "fromLastMonth": "from last month", "budget": "Budget", "spent": "Spent", "budgetUsed": "Budget Used", "impressions": "Impressions", "clicks": "<PERSON>licks", "ctr": "CTR", "revenue": "Revenue", "period": "Period", "refresh": "Refresh", "status": {"active": "Active", "paused": "Paused", "completed": "Completed", "draft": "Draft"}, "exportType": {"full": "Full Export", "partial": "Partial Export", "incremental": "Incremental Export"}}, "adPlacements": {"adPlacements": "Ad Placements", "adPlacementsDescription": "Configure and manage website ad placements, generate embed codes", "createPlacement": "Create Placement", "editPlacement": "Edit Placement", "placementCreated": "Placement created", "placementUpdated": "Placement updated", "placementDeleted": "Placement deleted", "placementActivated": "Placement activated", "placementDeactivated": "Placement deactivated", "loadPlacementsFailed": "Failed to load placements", "savePlacementFailed": "Failed to save placement", "deletePlacementFailed": "Failed to delete placement", "togglePlacementFailed": "Failed to toggle placement status", "noPlacements": "No Placements", "noPlacementsDescription": "Create ad placements to start displaying ads", "createFirstPlacement": "Create First Placement", "name": "Name", "placementName": "Placement Name", "type": {"banner": "Banner", "sidebar": "Sidebar", "inline": "Inline", "popup": "Popup", "sticky": "<PERSON>y"}, "position": {"header": "Header", "footer": "Footer", "sidebar-left": "Left Sidebar", "sidebar-right": "Right Sidebar", "content-top": "Content Top", "content-bottom": "Content Bottom", "between-posts": "Between Posts"}, "size": "Size", "fillRate": "Fill Rate", "targetDevices": "Target Devices", "created": "Created", "lastUpdated": "Last Updated", "active": "Active", "inactive": "Inactive", "responsive": "Responsive", "adSize": "Ad Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "presetSizes": "Preset Sizes", "adCode": "Ad Code", "adCodeDescription": "Embed this code into your website to display ads", "copyCode": "Copy Code", "codeCopied": "Code copied", "copyFailed": "Co<PERSON> failed", "cancel": "Cancel", "create": "Create", "update": "Update", "device": {"mobile": "Mobile", "tablet": "Tablet", "desktop": "Desktop"}}, "dataExport": {"dataExportBackup": "Data Export & Backup", "dataExportDescription": "Manage data export jobs and system backups to ensure data security", "exports": "Export Jobs", "backups": "Backup Jobs", "createExport": "Create Export", "createBackup": "Create Backup", "exportJobCreated": "Export job created", "backupJobCreated": "Backup job created", "createExportFailed": "Failed to create export", "createBackupFailed": "Failed to create backup", "loadExportsFailed": "Failed to load export jobs", "jobCancelled": "Job cancelled", "cancelJobFailed": "Failed to cancel job", "downloadFailed": "Download failed", "noExports": "No Export Jobs", "noExportsDescription": "Create export jobs to backup your data", "createFirstExport": "Create First Export", "noBackups": "No Backup Jobs", "noBackupsDescription": "Create backup jobs to protect your data", "createFirstBackup": "Create First Backup", "name": "Name", "exportNamePlaceholder": "Enter export job name", "exportType": {"full": "Full Export", "partial": "Partial Export", "incremental": "Incremental Export"}, "format": "Format", "selectDataTypes": "Select Data Types", "startDate": "Start Date", "endDate": "End Date", "progress": "Progress", "dataTypes": {"articles": "Articles", "users": "Users", "comments": "Comments", "media": "Media Files", "settings": "Settings", "analytics": "Analytics Data"}, "dateRange": "Date Range", "created": "Created", "completed": "Completed", "fileSize": "File Size", "download": "Download", "cancel": "Cancel", "refresh": "Refresh", "nextRun": "Next Run", "encrypted": "Encrypted", "compressed": "Compressed", "restore": "Rest<PERSON>", "status": {"pending": "Pending", "running": "Running", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}, "backupType": {"manual": "Manual Backup", "scheduled": "Scheduled Backup"}, "destination": {"local": "Local Storage", "cloud": "Cloud Storage", "external": "External Storage"}}, "dataMigration": {"dataMigration": "Data Migration", "dataMigrationDescription": "Manage data migration jobs with support for multiple data sources and target systems", "createMigrationJob": "Create Migration Job", "jobCreated": "Job created", "createJobFailed": "Failed to create job", "loadJobsFailed": "Failed to load jobs", "startSuccess": "Started successfully", "startFailed": "Failed to start", "pauseSuccess": "Paused successfully", "pauseFailed": "Failed to pause", "resumeSuccess": "Resumed successfully", "resumeFailed": "Failed to resume", "cancelSuccess": "Cancelled successfully", "cancelFailed": "Failed to cancel", "noJobs": "No Migration Jobs", "noJobsDescription": "Create migration jobs to transfer and synchronize data", "createFirstJob": "Create First Job", "name": "Name", "jobNamePlaceholder": "Enter job name", "type": "Type", "source": "Source", "destination": "Destination", "sourceType": "Source Type", "destinationType": "Destination Type", "selectFile": "Select File", "host": "Host", "database": "Database", "url": "URL", "table": "Table", "fileName": "File Name", "enableValidation": "Enable Validation", "validationDescription": "Validate data integrity and format during migration", "validationEnabled": "Validation enabled", "progress": "Progress", "totalRecords": "Total Records", "processed": "Processed", "errors": "Errors", "validation": "Validation", "created": "Created", "completed": "Completed", "error": "Error", "start": "Start", "pause": "Pause", "resume": "Resume", "cancel": "Cancel", "restart": "<PERSON><PERSON>", "createJob": "Create Job", "jobLogs": "Job Logs", "noLogs": "No logs available", "refresh": "Refresh", "status": {"pending": "Pending", "running": "Running", "completed": "Completed", "failed": "Failed", "paused": "Paused"}, "jobType": {"import": "Import", "export": "Export", "sync": "Sync"}, "sourceTypes": {"file": "File", "database": "Database", "api": "API", "url": "URL"}, "destinationTypes": {"database": "Database", "file": "File", "api": "API"}}, "accessibility": {"skipToMain": "Skip to main content", "skipToNavigation": "Skip to navigation", "skipToFooter": "Skip to footer", "skipLinks": "Skip links", "shortcuts": {"title": "Keyboard Shortcuts", "description": "Use the following shortcuts to navigate and operate quickly", "showHelp": "Show keyboard shortcuts help", "search": "Search", "home": "Go to home", "articles": "Articles list", "dashboard": "Admin dashboard", "help": "Show help", "close": "Close", "categories": {"navigation": "Navigation", "general": "General", "help": "Help"}}}, "rss": {"title": "RSS Subscription", "subtitle": "Subscribe to our latest content via RSS readers", "subscribe": "Subscribe", "copied": "Copied {type} link", "copyFailed": "Co<PERSON> failed", "filtered": "Filter", "rssDescription": "Standard RSS 2.0 format", "atomDescription": "Modern Atom 1.0 format", "jsonDescription": "Lightweight JSON Feed format", "howToUse": "How to Use RSS", "readers": {"title": "RSS Readers", "description": "Use professional RSS readers for the best experience"}, "browsers": {"title": "Browser Support", "description": "Modern browsers can directly open RSS links to preview content"}, "apps": {"title": "Mobile Apps", "description": "Use RSS apps on your phone to read updates anytime, anywhere"}}, "friendLinks": {"title": "Friend Links", "description": "Recommended excellent websites and blogs", "loading": "Loading...", "featured": "Featured Links", "allLinks": "All Links", "visit": "Visit", "categories": {"tech": "Technology", "blog": "Blog", "friend": "Friend", "other": "Other"}, "applyTitle": "Apply for Friend Link", "applyDescription": "Quality websites are welcome to apply for friend links, let's build a better web ecosystem together", "requirements": {"title": "Application Requirements", "content": "Healthy website content, no illegal or inappropriate information", "update": "Website maintains regular updates, not long-term inactive", "friendly": "User-friendly website design with good user experience", "https": "Support HTTPS access"}, "applyNow": "Apply Now"}, "meta": {"defaultTitle": "Modern Blog System", "defaultDescription": "A modern blog system built with Next.js, featuring AI summaries, multi-language support, dark mode, and more"}}
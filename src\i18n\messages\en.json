{
  "common": {
    "loading": "Loading...",
    "save": "Save",
    "cancel": "Cancel",
    "delete": "Delete",
    "edit": "Edit",
    "view": "View",
    "search": "Search",
    "filter": "Filter",
    "upload": "Upload",
    "download": "Download",
    "copy": "Copy",
    "share": "Share",
    "back": "Back",
    "next": "Next",
    "previous": "Previous",
    "home": "Home",
    "login": "Login",
    "logout": "Logout",
    "profile": "Profile",
    "settings": "Settings",
    "admin": "Admin",
    "dashboard": "Dashboard"
  },
  "navigation": {
    "home": "Home",
    "articles": "Articles",
    "archive": "Archive",
    "now": "Now",
    "guestbook": "Guestbook",
    "files": "Files",
    "profile": "Profile"
  },
  "auth": {
    "signIn": "Sign In",
    "signOut": "Sign Out",
    "signInWith": "Sign in with {provider}",
    "welcomeBack": "Welcome Back",
    "signInToAccess": "Sign in to {siteName}",
    "authFailed": "Authentication Failed",
    "authError": "There was a problem during authentication",
    "accessDenied": "Access Denied",
    "noPermission": "You don't have permission to access this page",
    "needHelp": "Need help?",
    "contactAdmin": "Contact Administrator"
  },
  "articles": {
    "title": "Articles",
    "subtitle": "Explore our curated collection of technical articles and insights",
    "searchPlaceholder": "Search articles...",
    "allCategories": "All Categories",
    "allStatuses": "All Statuses",
    "published": "Published",
    "draft": "Draft",
    "archived": "Archived",
    "noArticles": "No Articles",
    "noArticlesFound": "No articles found matching your criteria",
    "notPublished": "No articles have been published yet",
    "readingTime": "{minutes} min read",
    "viewCount": "{count} views",
    "likeCount": "{count} likes",
    "aiSummary": "AI Summary",
    "articleEnd": "End of Article",
    "tableOfContents": "Table of Contents",
    "readingProgress": "Reading Progress",
    "shareArticle": "Share Article",
    "copyLink": "Copy Link",
    "relatedArticles": "Related Articles"
  },
  "dashboard": {
    "title": "Dashboard",
    "welcome": "Welcome back, {name}! Manage your content and settings.",
    "createArticle": "Create Article",
    "quickActions": "Quick Actions",
    "recentActivity": "Recent Activity",
    "statistics": {
      "totalArticles": "Total Articles",
      "publishedArticles": "Published",
      "draftArticles": "Drafts",
      "totalViews": "Total Views",
      "totalUsers": "Total Users"
    },
    "actions": {
      "createNew": "Create New Article",
      "createNewDesc": "Start writing a new blog post",
      "manageArticles": "Manage Articles",
      "manageArticlesDesc": "View and edit existing articles",
      "fileManagement": "File Management",
      "fileManagementDesc": "Upload and manage media files"
    }
  },
  "files": {
    "title": "File Management",
    "subtitle": "Upload, manage and organize your media files",
    "uploadFiles": "Upload Files",
    "uploading": "Uploading...",
    "searchFiles": "Search files...",
    "allTypes": "All Types",
    "images": "Images",
    "videos": "Videos",
    "audio": "Audio",
    "documents": "Documents",
    "gridView": "Grid View",
    "listView": "List View",
    "noFiles": "No Files",
    "noFilesFound": "No files found matching your criteria",
    "uploadFirst": "Upload your first file",
    "public": "Public",
    "private": "Private",
    "copyUrl": "Copy URL",
    "fileSize": "File Size",
    "uploadedAt": "Uploaded",
    "statistics": {
      "totalFiles": "Total Files",
      "imageFiles": "Image Files",
      "documentFiles": "Document Files",
      "totalSize": "Total Size"
    }
  },
  "editor": {
    "title": "Create New Article",
    "editTitle": "Edit Article",
    "subtitle": "Use the Markdown editor to create your article content",
    "articleInfo": "Article Information",
    "articleTitle": "Title",
    "titlePlaceholder": "Enter article title...",
    "urlSlug": "URL Slug",
    "slugPlaceholder": "article-slug",
    "excerpt": "Article Excerpt",
    "excerptPlaceholder": "Enter article excerpt...",
    "aiGenerate": "AI Generate",
    "generating": "Generating...",
    "content": "Article Content",
    "contentDesc": "Write your article content using Markdown syntax",
    "contentPlaceholder": "Start writing your article content...",
    "preview": "Preview",
    "edit": "Edit",
    "saveDraft": "Save Draft",
    "saving": "Saving...",
    "publish": "Publish Article",
    "publishSettings": "Publish Settings",
    "category": "Category",
    "selectCategory": "Select Category",
    "tags": "Tags",
    "coverImage": "Cover Image URL",
    "coverImagePlaceholder": "https://example.com/image.jpg",
    "statistics": {
      "characters": "Characters",
      "readingTime": "Reading Time",
      "status": "Status"
    }
  },
  "profile": {
    "title": "Profile",
    "subtitle": "Manage your account information and preferences",
    "basicInfo": "Basic Information",
    "basicInfoDesc": "Your account basic information",
    "userId": "User ID",
    "role": "Role",
    "permissions": "Permissions",
    "permissionsDesc": "Your permission level in the system",
    "viewArticles": "View Articles",
    "createArticles": "Create Articles",
    "deleteArticles": "Delete Articles",
    "fileManagement": "File Management",
    "systemAdmin": "System Administration",
    "allowed": "✓ Allowed",
    "denied": "✗ Denied",
    "roles": {
      "admin": "Administrator",
      "collaborator": "Collaborator",
      "user": "User"
    }
  },
  "errors": {
    "notFound": "Page Not Found",
    "articleNotFound": "Article Not Found",
    "articleNotFoundDesc": "Sorry, the article you're looking for doesn't exist or has been deleted",
    "possibleReasons": "Possible reasons:",
    "wrongLink": "Incorrect article link",
    "articleDeleted": "Article has been deleted",
    "articleNotPublished": "Article is not yet published",
    "backToArticles": "Back to Articles",
    "backToHome": "Back to Home",
    "serverError": "Server Error",
    "somethingWrong": "Something went wrong",
    "tryAgain": "Please try again later"
  },
  "footer": {
    "description": "Modern blog system built with Next.js",
    "quickLinks": "Quick Links",
    "features": "Features",
    "other": "Other",
    "builtWith": "Built with ❤️ using Next.js, Tailwind CSS, and Shadcn UI",
    "allRightsReserved": "All rights reserved"
  },
  "theme": {
    "toggle": "Toggle theme",
    "light": "Light mode",
    "dark": "Dark mode",
    "system": "System"
  },
  "admin": {
    "loading": "Loading...",
    "accessDenied": "Access Denied",
    "loginRequired": "Please sign in to access the admin panel",
    "signIn": "Sign In",
    "insufficientPermissions": "Insufficient Permissions",
    "adminAccessRequired": "Admin or collaborator permissions required to access this page",
    "backToHome": "Back to Home",
    "collaboratorNotice": "You are currently signed in as a collaborator, some features may be limited.",
    "sidebar": {
      "title": "Admin Panel",
      "dashboard": "Dashboard",
      "content": "Content Management",
      "articles": "Articles",
      "pages": "Pages",
      "categories": "Categories",
      "tags": "Tags",
      "media": "Media Files",
      "users": "User Management",
      "comments": "Comment Management",
      "links": "Friend Links",
      "analytics": "Analytics",
      "settings": "System Settings",
      "version": "Version",
      "lastUpdate": "Last Update"
    },
    "header": {
      "searchPlaceholder": "Search...",
      "unknownUser": "Unknown User",
      "admin": "Admin",
      "collaborator": "Collaborator",
      "profile": "Profile",
      "settings": "Settings",
      "signOut": "Sign Out"
    },
    "categoryTag": {
      "title": "Category & Tag Management",
      "subtitle": "Manage blog categories and tags to organize content structure",
      "loading": "Loading...",
      "categories": "Categories",
      "tags": "Tags",
      "createCategory": "Create Category",
      "createTag": "Create Tag",
      "editCategory": "Edit Category",
      "editTag": "Edit Tag",
      "categoryDescription": "Categories are used to organize main content types of articles",
      "tagDescription": "Tags are used to mark specific topics and keywords of articles",
      "searchPlaceholder": "Search categories or tags...",
      "name": "Name",
      "slug": "URL Slug",
      "description": "Description",
      "color": "Color",
      "articleCount": "Article Count",
      "updatedAt": "Updated At",
      "namePlaceholder": "Enter name",
      "slugPlaceholder": "url-slug",
      "descriptionPlaceholder": "Enter description (optional)",
      "edit": "Edit",
      "delete": "Delete",
      "cancel": "Cancel",
      "create": "Create",
      "update": "Update",
      "confirmDelete": "Are you sure you want to delete this item? This action cannot be undone."
    }
  },
  "comments": {
    "title": "Comments",
    "loading": "Loading comments...",
    "configRequired": "Comments feature requires Giscus environment variables configuration",
    "configInstructions": "Please configure in .env.local:",
    "noComments": "No comments yet",
    "writeComment": "Write a comment",
    "reply": "Reply",
    "edit": "Edit",
    "delete": "Delete",
    "reactions": "Reactions",
    "signInToComment": "Sign in to join the discussion"
  },
  "analytics": {
    "title": "Analytics",
    "subtitle": "View website traffic statistics and content performance",
    "loading": "Loading...",
    "noData": "No data available",
    "periods": {
      "7d": "Last 7 days",
      "30d": "Last 30 days",
      "90d": "Last 90 days",
      "1y": "Last year"
    },
    "metrics": {
      "totalViews": "Total Views",
      "uniqueVisitors": "Unique Visitors",
      "articlesViewed": "Articles Viewed",
      "countries": "Countries",
      "views": "views",
      "visitors": "visitors"
    },
    "charts": {
      "viewsTrend": "Views Trend",
      "viewsTrendDesc": "Website traffic over time",
      "topArticles": "Top Articles",
      "topArticlesDesc": "Most viewed articles",
      "geography": "Geographic Distribution",
      "geographyDesc": "Visitor geographic distribution"
    }
  },
  "dashboard": {
    "stats": {
      "articles": "Articles",
      "totalViews": "Total Views",
      "users": "Users",
      "files": "Files",
      "published": "Published",
      "draft": "Draft",
      "admins": "Admins",
      "collaborators": "Collaborators",
      "images": "Images",
      "uniqueVisitors": "Unique Visitors",
      "contentStats": "Content Statistics",
      "contentStatsDesc": "Article publishing and management status",
      "viewStats": "View Statistics",
      "viewStatsDesc": "Website views and visitor data",
      "publishedArticles": "Published Articles",
      "articlesViewed": "Articles Viewed",
      "recentArticles": "Recent Articles",
      "recentViews": "Recent Views",
      "popularArticles": "Popular Articles",
      "popularArticlesDesc": "Most viewed articles",
      "views": "views",
      "noData": "No data available"
    }
  },
  "toast": {
    "success": "Success",
    "error": "Error",
    "warning": "Warning",
    "info": "Info",
    "loading": "Loading..."
  },
  "api": {
    "loading": "Processing request...",
    "success": "Request successful",
    "error": "Request failed"
  },
  "form": {
    "submitSuccess": "Submitted successfully",
    "submitError": "Submission failed",
    "validationError": "Form validation failed"
  },
  "fileUpload": {
    "uploading": "Uploading",
    "uploadSuccess": "Upload successful",
    "uploadError": "Upload failed"
  },
  "copy": {
    "success": "Copied successfully",
    "error": "Copy failed"
  },
  "search": {
    "title": "Search",
    "placeholder": "Search articles...",
    "inputPlaceholder": "Search articles, tags or keywords...",
    "search": "Search",
    "searchFailed": "Search failed",
    "noResults": "No results found",
    "noResultsDesc": "No content found related to \"{query}\"",
    "noResultsDescription": "No content found related to \"{query}\", please try other keywords",
    "results": "results",
    "resultsFor": "Search results: {query}",
    "resultsTitle": "Search: {query}",
    "resultsDescription": "Search content related to \"{query}\"",
    "resultsCount": "Found {count} results related to \"{query}\"",
    "viewAllResults": "View all search results for \"{query}\"",
    "recentSearches": "Recent searches",
    "suggestions": "Search suggestions",
    "clearHistory": "Clear history",
    "filters": "Filters",
    "activeFilters": "Active filters",
    "clearAll": "Clear all",
    "contentType": "Content type",
    "allTypes": "All types",
    "articles": "Articles",
    "tags": "Tags",
    "categories": "Categories",
    "users": "Users",
    "category": "Category",
    "tag": "Tag",
    "author": "Author",
    "date": "Date",
    "type": "Type",
    "selectCategory": "Select category",
    "allCategories": "All categories",
    "selectTag": "Select tag",
    "allTags": "All tags",
    "selectAuthor": "Select author",
    "allAuthors": "All authors",
    "dateRange": "Date range",
    "selectDateRange": "Select date range",
    "allTime": "All time",
    "today": "Today",
    "thisWeek": "This week",
    "thisMonth": "This month",
    "thisYear": "This year",
    "page": "Page",
    "previousPage": "Previous",
    "nextPage": "Next",
    "moreTags": "more tags",
    "startSearching": "Start searching",
    "startSearchingDescription": "Enter keywords to search articles, tags and other content",
    "searchTips": "Search tips",
    "tip1": "Use quotes to search exact phrases",
    "tip2": "Use + to include required words",
    "tip3": "Use - to exclude specific words",
    "tip4": "Use tags and categories to filter results",
    "searchSuggestions": "Search suggestions",
    "suggestion1": "Check if spelling is correct",
    "suggestion2": "Try using more general keywords",
    "suggestion3": "Use different keyword combinations",
    "searchDescription": "Search content related to \"{query}\""
  },
  "editor": {
    "bold": "Bold",
    "italic": "Italic",
    "strikethrough": "Strikethrough",
    "heading1": "Heading 1",
    "heading2": "Heading 2",
    "heading3": "Heading 3",
    "bulletList": "Bullet List",
    "numberedList": "Numbered List",
    "quote": "Quote",
    "link": "Link",
    "image": "Image",
    "code": "Code",
    "undo": "Undo",
    "redo": "Redo",
    "search": "Search",
    "edit": "Edit",
    "split": "Split",
    "previewMode": "Preview Mode",
    "editMode": "Edit Mode",
    "splitView": "Split View",
    "settings": "Settings",
    "fullscreen": "Fullscreen",
    "exitFullscreen": "Exit Fullscreen",
    "save": "Save",
    "words": "Words",
    "characters": "Characters",
    "lines": "Lines",
    "readingTime": "Reading Time",
    "minutes": "minutes",
    "lastSaved": "Last Saved",
    "autoSave": "Auto Save",
    "searchAndReplace": "Search and Replace",
    "searchPlaceholder": "Search content...",
    "replacePlaceholder": "Replace with...",
    "findNext": "Find Next",
    "replaceAll": "Replace All",
    "editorSettings": "Editor Settings",
    "fontSize": "Font Size",
    "lineHeight": "Line Height",
    "wordWrap": "Word Wrap",
    "spellCheck": "Spell Check",
    "autoComplete": "Auto Complete"
  },
  "imageUpload": {
    "insertImage": "Insert Image",
    "upload": "Upload",
    "fromUrl": "From URL",
    "gallery": "Gallery",
    "dragDropOrClick": "Drag and drop files here or click to upload",
    "supportedFormats": "Supported formats",
    "maxFileSize": "Max file size",
    "invalidFileType": "Invalid file type",
    "fileTooLarge": "File size exceeds {size}MB",
    "uploadFailed": "Upload failed",
    "success": "Success",
    "error": "Error",
    "imageUrl": "Image URL",
    "altText": "Alt Text",
    "altTextPlaceholder": "Describe the image content...",
    "title": "Title",
    "optional": "optional",
    "titlePlaceholder": "Image title...",
    "insertFromUrl": "Insert from URL",
    "noImagesYet": "No images yet",
    "uploadFirstImage": "Upload your first image",
    "insert": "Insert",
    "imageDetails": "Image Details",
    "filename": "Filename",
    "dimensions": "Dimensions",
    "fileSize": "File Size",
    "url": "URL",
    "copyUrl": "Copy URL",
    "urlCopied": "URL copied",
    "copyFailed": "Copy failed",
    "pleaseEnterUrl": "Please enter image URL",
    "image": "Image"
  },
  "versionControl": {
    "versionHistory": "Version History",
    "versions": "versions",
    "compare": "Compare",
    "createVersion": "Create Version",
    "createNewVersion": "Create New Version",
    "versionSummary": "Version Summary",
    "versionSummaryPlaceholder": "Describe the changes in this version...",
    "version": "Version",
    "author": "Author",
    "date": "Date",
    "words": "Words",
    "status": "Status",
    "all": "All",
    "allAuthors": "All Authors",
    "draft": "Draft",
    "published": "Published",
    "archived": "Archived",
    "noVersions": "No Versions",
    "noVersionsDescription": "No versions have been created yet",
    "restore": "Restore",
    "preview": "Preview",
    "justNow": "Just now",
    "minutesAgo": "{count} minutes ago",
    "hoursAgo": "{count} hours ago",
    "daysAgo": "{count} days ago",
    "compareVersions": "Compare Versions",
    "contentDifferences": "Content Differences",
    "noChanges": "No changes",
    "versionCreated": "Version created",
    "createVersionFailed": "Failed to create version",
    "versionRestored": "Version restored",
    "restoreVersionFailed": "Failed to restore version",
    "compareVersionsFailed": "Failed to compare versions",
    "loadVersionsFailed": "Failed to load versions",
    "title": "Title",
    "contentPreview": "Content Preview"
  },
  "draftManager": {
    "draftManager": "Draft Manager",
    "drafts": "drafts",
    "deleteSelected": "Delete Selected",
    "saveDraft": "Save Draft",
    "autoSave": "Auto Save",
    "interval": "Interval",
    "enabled": "Enabled",
    "disabled": "Disabled",
    "searchDrafts": "Search drafts...",
    "allStatuses": "All Statuses",
    "autoSaved": "Auto Saved",
    "manualSaved": "Manual Saved",
    "lastSaved": "Last Saved",
    "title": "Title",
    "wordCount": "Word Count",
    "noDrafts": "No Drafts",
    "noDraftsDescription": "No drafts have been saved yet",
    "untitled": "Untitled",
    "recoverable": "Recoverable",
    "loadDraft": "Load Draft",
    "restoreDraft": "Restore Draft",
    "previewDraft": "Preview Draft",
    "deleteDraft": "Delete Draft",
    "draftSaved": "Draft saved",
    "saveDraftFailed": "Failed to save draft",
    "draftDeleted": "Draft deleted",
    "deleteDraftFailed": "Failed to delete draft",
    "draftsDeleted": "Deleted {count} drafts",
    "deleteDraftsFailed": "Failed to delete drafts",
    "draftRestored": "Draft restored",
    "loadDraftsFailed": "Failed to load drafts"
  },
  "publishScheduler": {
    "publishScheduler": "Publish Scheduler",
    "scheduled": "Scheduled",
    "schedulePost": "Schedule Post",
    "editSchedule": "Edit Schedule",
    "noScheduledPosts": "No Scheduled Posts",
    "noScheduledPostsDescription": "No posts have been scheduled for publication yet",
    "publishDateTime": "Publish Date & Time",
    "visibility": "Visibility",
    "public": "Public",
    "unlisted": "Unlisted",
    "private": "Private",
    "basic": "Basic",
    "seo": "SEO",
    "social": "Social",
    "titlePlaceholder": "Article title...",
    "seoTitle": "SEO Title",
    "seoTitlePlaceholder": "Title displayed in search engines...",
    "seoDescription": "SEO Description",
    "seoDescriptionPlaceholder": "Description displayed in search engines...",
    "socialMediaSharing": "Social Media Sharing",
    "shareToTwitter": "Share to Twitter",
    "shareToFacebook": "Share to Facebook",
    "shareToLinkedIn": "Share to LinkedIn",
    "notifications": "Notifications",
    "emailNotifications": "Email Notifications",
    "pushNotifications": "Push Notifications",
    "updateSchedule": "Update Schedule",
    "publishNow": "Publish Now",
    "cancelSchedule": "Cancel Schedule",
    "delete": "Delete",
    "preview": "Preview",
    "socialMediaEnabled": "Social media enabled",
    "pleaseSelectDateTime": "Please select publish date and time",
    "scheduleDateMustBeFuture": "Schedule date must be in the future",
    "postScheduled": "Post scheduled",
    "schedulePostFailed": "Failed to schedule post",
    "scheduleUpdated": "Schedule updated",
    "updateScheduleFailed": "Failed to update schedule",
    "scheduleCancelled": "Schedule cancelled",
    "cancelScheduleFailed": "Failed to cancel schedule",
    "postPublished": "Post published",
    "publishNowFailed": "Failed to publish now",
    "loadScheduledPostsFailed": "Failed to load scheduled posts"
  },
  "contentWorkflow": {
    "contentWorkflow": "Content Workflow",
    "pending": "Pending",
    "submitForReview": "Submit for Review",
    "workflowSettings": "Workflow Settings",
    "allStatuses": "All Statuses",
    "inReview": "In Review",
    "approved": "Approved",
    "rejected": "Rejected",
    "published": "Published",
    "allAssignees": "All Assignees",
    "assignedToMe": "Assigned to Me",
    "unassigned": "Unassigned",
    "noWorkflowItems": "No Workflow Items",
    "noWorkflowItemsDescription": "No content has been submitted for review yet",
    "review": "Review",
    "viewDetails": "View Details",
    "unassign": "Unassign",
    "approveItem": "Approve Item",
    "rejectItem": "Reject Item",
    "approve": "Approve",
    "reject": "Reject",
    "approvalComment": "Approval Comment",
    "rejectionComment": "Rejection Comment",
    "approvalCommentPlaceholder": "Add approval comment (optional)...",
    "rejectionCommentPlaceholder": "Please explain the reason for rejection...",
    "cancel": "Cancel",
    "content": "Content",
    "comments": "Comments",
    "history": "History",
    "workflowHistoryPlaceholder": "Workflow history will be displayed here",
    "workflowSettingsDescription": "Configure content review workflow",
    "submittedForReview": "Submitted for review",
    "submitForReviewFailed": "Failed to submit for review",
    "itemApproved": "Item approved",
    "itemRejected": "Item rejected",
    "rejectionCommentRequired": "Rejection comment is required",
    "reviewFailed": "Review failed",
    "itemAssigned": "Item assigned",
    "assignFailed": "Failed to assign",
    "loadWorkflowItemsFailed": "Failed to load workflow items",
    "low": "Low",
    "medium": "Medium",
    "high": "High",
    "urgent": "Urgent"
  },
  "articleEditor": {
    "editArticle": "Edit Article",
    "newArticle": "New Article",
    "unsavedChanges": "Unsaved Changes",
    "draft": "Draft",
    "published": "Published",
    "scheduled": "Scheduled",
    "lastSaved": "Last Saved",
    "exitPreview": "Exit Preview",
    "preview": "Preview",
    "save": "Save",
    "publish": "Publish",
    "submitForReview": "Submit for Review",
    "titlePlaceholder": "Enter article title...",
    "selectCategory": "Select Category",
    "technology": "Technology",
    "design": "Design",
    "business": "Business",
    "lifestyle": "Lifestyle",
    "addTag": "Add Tag",
    "contentPlaceholder": "Start writing...",
    "untitled": "Untitled",
    "noContent": "No content",
    "tools": "Tools",
    "articleSaved": "Article saved",
    "saveArticleFailed": "Failed to save article",
    "articlePublished": "Article published",
    "publishArticleFailed": "Failed to publish article",
    "submittedForReview": "Submitted for review",
    "submitForReviewFailed": "Failed to submit for review"
  },
  "followSystem": {
    "follow": "Follow",
    "following": "Following",
    "followers": "Followers",
    "followBack": "Follow Back",
    "unfollow": "Unfollow",
    "verified": "Verified",
    "articles": "Articles",
    "mutualFollowers": "Mutual Followers",
    "lastActive": "Last Active",
    "neverActive": "Never Active",
    "activeNow": "Active Now",
    "activeHoursAgo": "Active {count} hours ago",
    "activeDaysAgo": "Active {count} days ago",
    "activeWeeksAgo": "Active weeks ago",
    "suggestedUsers": "Suggested Users",
    "userActions": "User Actions",
    "blockUser": "Block User",
    "searchUsers": "Search Users",
    "noUsersFound": "No users found",
    "noUsers": "No users",
    "enableNotifications": "Enable Notifications",
    "disableNotifications": "Disable Notifications",
    "followSuccess": "Followed successfully",
    "followFailed": "Failed to follow",
    "unfollowSuccess": "Unfollowed successfully",
    "unfollowFailed": "Failed to unfollow",
    "blockSuccess": "Blocked successfully",
    "blockFailed": "Failed to block",
    "notificationsEnabled": "Notifications enabled",
    "notificationsDisabled": "Notifications disabled",
    "notificationToggleFailed": "Failed to toggle notifications"
  },
  "articleInteractions": {
    "likes": "Likes",
    "bookmarks": "Bookmarks",
    "share": "Share",
    "comments": "Comments",
    "views": "Views",
    "shareArticle": "Share Article",
    "shareOn": "Share on",
    "email": "Email",
    "copyLink": "Copy Link",
    "reportArticle": "Report Article",
    "reportReason": "Report Reason",
    "reportDescription": "Description",
    "reportDescriptionPlaceholder": "Please describe the reason for reporting...",
    "cancel": "Cancel",
    "submitReport": "Submit Report",
    "reportReason.spam": "Spam",
    "reportReason.harassment": "Harassment",
    "reportReason.inappropriate": "Inappropriate Content",
    "reportReason.copyright": "Copyright Violation",
    "reportReason.misinformation": "Misinformation",
    "reportReason.other": "Other",
    "loginRequired": "Please login first",
    "likeSuccess": "Liked successfully",
    "unlikeSuccess": "Unliked successfully",
    "likeFailed": "Failed to like",
    "bookmarkSuccess": "Bookmarked successfully",
    "unbookmarkSuccess": "Unbookmarked successfully",
    "bookmarkFailed": "Failed to bookmark",
    "linkCopied": "Link copied",
    "copyFailed": "Failed to copy",
    "reportSuccess": "Reported successfully",
    "reportFailed": "Failed to report",
    "reportReasonRequired": "Please select a reason"
  },
  "activityTimeline": {
    "activityFeed": "Activity Feed",
    "allActivities": "All Activities",
    "articles": "Articles",
    "interactions": "Interactions",
    "follows": "Follows",
    "comments": "Comments",
    "justNow": "Just now",
    "minutesAgo": "{count} minutes ago",
    "hoursAgo": "{count} hours ago",
    "daysAgo": "{count} days ago",
    "noMoreActivities": "No more activities",
    "noActivities": "No Activities",
    "noActivitiesDescription": "Follow some users to see their activities",
    "loadActivitiesFailed": "Failed to load activities",
    "activityDescription.articlePublished": "{user} published article \"{article}\"",
    "activityDescription.articleLiked": "{user} liked article \"{article}\"",
    "activityDescription.articleBookmarked": "{user} bookmarked article \"{article}\"",
    "activityDescription.articleShared": "{user} shared article \"{article}\" on {platform}",
    "activityDescription.userFollowed": "{user} followed {target}",
    "activityDescription.commentPosted": "{user} commented on article \"{article}\"",
    "activityDescription.articleUpdated": "{user} updated article \"{article}\"",
    "activityDescription.milestoneReached": "{user} reached milestone: {milestone} ({count})",
    "activityDescription.unknown": "Unknown activity"
  },
  "messaging": {
    "messages": "Messages",
    "searchConversations": "Search conversations",
    "noConversationsFound": "No conversations found",
    "noConversations": "No conversations",
    "selectConversation": "Select Conversation",
    "selectConversationDescription": "Choose a conversation to start chatting",
    "online": "Online",
    "lastSeen": "Last seen: {time}",
    "typing": "Typing...",
    "typeMessage": "Type a message...",
    "replyingTo": "Replying to",
    "replyTo": "Reply to",
    "edited": "Edited",
    "sendMessageFailed": "Failed to send message",
    "uploadFailed": "Upload failed",
    "noMessages": "No messages"
  },
  "comments": {
    "comments": "Comments",
    "newest": "Newest",
    "oldest": "Oldest",
    "popular": "Popular",
    "controversial": "Controversial",
    "write": "Write",
    "preview": "Preview",
    "writeComment": "Write a comment...",
    "writeReply": "Write a reply...",
    "replyingTo": "Replying to",
    "cancel": "Cancel",
    "comment": "Comment",
    "reply": "Reply",
    "share": "Share",
    "edit": "Edit",
    "delete": "Delete",
    "pin": "Pin",
    "unpin": "Unpin",
    "report": "Report",
    "pinned": "Pinned",
    "edited": "Edited",
    "justNow": "Just now",
    "minutesAgo": "{count} minutes ago",
    "hoursAgo": "{count} hours ago",
    "daysAgo": "{count} days ago",
    "markdownSupported": "Markdown supported",
    "mentionWithAt": "Use @ to mention users",
    "nothingToPreview": "Nothing to preview",
    "noComments": "No Comments",
    "beFirstToComment": "Be the first to comment",
    "commentActions": "Comment Actions",
    "loginRequired": "Please login first",
    "loadCommentsFailed": "Failed to load comments",
    "commentSubmitted": "Comment submitted",
    "submitCommentFailed": "Failed to submit comment",
    "likeCommentFailed": "Failed to like comment",
    "dislikeCommentFailed": "Failed to dislike comment",
    "commentReported": "Comment reported",
    "reportCommentFailed": "Failed to report comment",
    "commentDeleted": "Comment deleted",
    "deleteCommentFailed": "Failed to delete comment",
    "commentUpdated": "Comment updated",
    "editCommentFailed": "Failed to edit comment",
    "commentPinned": "Comment pinned",
    "pinCommentFailed": "Failed to pin comment"
  },
  "social": {
    "title": "Social Hub",
    "description": "Interact with other users and follow interesting content creators",
    "followers": "Followers",
    "following": "Following",
    "likes": "Likes",
    "bookmarks": "Bookmarks",
    "shares": "Shares",
    "comments": "Comments",
    "timeline": "Timeline",
    "messages": "Messages",
    "trending": "Trending",
    "activityFeed": "Activity Feed",
    "suggestedUsers": "Suggested Users",
    "followingManagement": "Following Management",
    "messaging": "Messaging",
    "trendingActivities": "Trending Activities",
    "popularUsers": "Popular Users"
  },
  "notifications": {
    "notifications": "Notifications",
    "unread": "Unread",
    "markAllRead": "Mark All as Read",
    "notificationSettings": "Notification Settings",
    "all": "All",
    "interactions": "Interactions",
    "system": "System",
    "noNotifications": "No notifications",
    "markAsRead": "Mark as Read",
    "delete": "Delete",
    "justNow": "Just now",
    "minutesAgo": "{count} minutes ago",
    "hoursAgo": "{count} hours ago",
    "daysAgo": "{count} days ago",
    "notificationDeleted": "Notification deleted",
    "deleteNotificationFailed": "Failed to delete notification",
    "settingsUpdated": "Settings updated",
    "updateSettingsFailed": "Failed to update settings",
    "notificationTypes": "Notification Types",
    "notificationMethods": "Notification Methods",
    "notificationFrequency": "Notification Frequency",
    "emailNotifications": "Email Notifications",
    "pushNotifications": "Push Notifications",
    "immediate": "Immediate",
    "hourly": "Hourly",
    "daily": "Daily",
    "weekly": "Weekly",
    "notificationType.likes": "Likes",
    "notificationType.comments": "Comments",
    "notificationType.follows": "Follows",
    "notificationType.shares": "Shares",
    "notificationType.bookmarks": "Bookmarks",
    "notificationType.mentions": "Mentions",
    "notificationType.system": "System Notifications"
  }
  "accessibility": {
    "skipToMain": "Skip to main content",
    "skipToNavigation": "Skip to navigation",
    "skipToFooter": "Skip to footer",
    "skipLinks": "Skip links",
    "shortcuts": {
      "title": "Keyboard Shortcuts",
      "description": "Use the following shortcuts to navigate and operate quickly",
      "showHelp": "Show keyboard shortcuts help",
      "search": "Search",
      "home": "Go to home",
      "articles": "Articles list",
      "dashboard": "Admin dashboard",
      "help": "Show help",
      "close": "Close",
      "categories": {
        "navigation": "Navigation",
        "general": "General",
        "help": "Help"
      }
    }
  },
  "rss": {
    "title": "RSS Subscription",
    "subtitle": "Subscribe to our latest content via RSS readers",
    "subscribe": "Subscribe",
    "copied": "Copied {type} link",
    "copyFailed": "Copy failed",
    "filtered": "Filter",
    "rssDescription": "Standard RSS 2.0 format",
    "atomDescription": "Modern Atom 1.0 format",
    "jsonDescription": "Lightweight JSON Feed format",
    "howToUse": "How to Use RSS",
    "readers": {
      "title": "RSS Readers",
      "description": "Use professional RSS readers for the best experience"
    },
    "browsers": {
      "title": "Browser Support",
      "description": "Modern browsers can directly open RSS links to preview content"
    },
    "apps": {
      "title": "Mobile Apps",
      "description": "Use RSS apps on your phone to read updates anytime, anywhere"
    }
  },
  "friendLinks": {
    "title": "Friend Links",
    "description": "Recommended excellent websites and blogs",
    "loading": "Loading...",
    "featured": "Featured Links",
    "allLinks": "All Links",
    "visit": "Visit",
    "categories": {
      "tech": "Technology",
      "blog": "Blog",
      "friend": "Friend",
      "other": "Other"
    },
    "applyTitle": "Apply for Friend Link",
    "applyDescription": "Quality websites are welcome to apply for friend links, let's build a better web ecosystem together",
    "requirements": {
      "title": "Application Requirements",
      "content": "Healthy website content, no illegal or inappropriate information",
      "update": "Website maintains regular updates, not long-term inactive",
      "friendly": "User-friendly website design with good user experience",
      "https": "Support HTTPS access"
    },
    "applyNow": "Apply Now"
  },
  "meta": {
    "defaultTitle": "Modern Blog System",
    "defaultDescription": "A modern blog system built with Next.js, featuring AI summaries, multi-language support, dark mode, and more"
  }
}

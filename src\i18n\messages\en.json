{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "upload": "Upload", "download": "Download", "copy": "Copy", "share": "Share", "back": "Back", "next": "Next", "previous": "Previous", "home": "Home", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "settings": "Settings", "admin": "Admin", "dashboard": "Dashboard"}, "navigation": {"home": "Home", "articles": "Articles", "archive": "Archive", "now": "Now", "guestbook": "Guestbook", "files": "Files", "profile": "Profile"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "signInWith": "Sign in with {provider}", "welcomeBack": "Welcome Back", "signInToAccess": "Sign in to {siteName}", "authFailed": "Authentication Failed", "authError": "There was a problem during authentication", "accessDenied": "Access Denied", "noPermission": "You don't have permission to access this page", "needHelp": "Need help?", "contactAdmin": "Contact Administrator"}, "articles": {"title": "Articles", "subtitle": "Explore our curated collection of technical articles and insights", "searchPlaceholder": "Search articles...", "allCategories": "All Categories", "allStatuses": "All Statuses", "published": "Published", "draft": "Draft", "archived": "Archived", "noArticles": "No Articles", "noArticlesFound": "No articles found matching your criteria", "notPublished": "No articles have been published yet", "readingTime": "{minutes} min read", "viewCount": "{count} views", "likeCount": "{count} likes", "aiSummary": "AI Summary", "articleEnd": "End of Article", "tableOfContents": "Table of Contents", "readingProgress": "Reading Progress", "shareArticle": "Share Article", "copyLink": "Copy Link", "relatedArticles": "Related Articles"}, "dashboard": {"stats": {"articles": "Articles", "totalViews": "Total Views", "users": "Users", "files": "Files", "published": "Published", "draft": "Draft", "admins": "Admins", "collaborators": "Collaborators", "images": "Images", "uniqueVisitors": "Unique Visitors", "contentStats": "Content Statistics", "contentStatsDesc": "Article publishing and management status", "viewStats": "View Statistics", "viewStatsDesc": "Website views and visitor data", "publishedArticles": "Published Articles", "articlesViewed": "Articles Viewed", "recentArticles": "Recent Articles", "recentViews": "Recent Views", "popularArticles": "Popular Articles", "popularArticlesDesc": "Most viewed articles", "views": "views", "noData": "No data available"}}, "files": {"title": "File Management", "subtitle": "Upload, manage and organize your media files", "uploadFiles": "Upload Files", "uploading": "Uploading...", "searchFiles": "Search files...", "allTypes": "All Types", "images": "Images", "videos": "Videos", "audio": "Audio", "documents": "Documents", "gridView": "Grid View", "listView": "List View", "noFiles": "No Files", "noFilesFound": "No files found matching your criteria", "uploadFirst": "Upload your first file", "public": "Public", "private": "Private", "copyUrl": "Copy URL", "fileSize": "File Size", "uploadedAt": "Uploaded", "statistics": {"totalFiles": "Total Files", "imageFiles": "Image Files", "documentFiles": "Document Files", "totalSize": "Total Size"}}, "editor": {"title": "Create New Article", "editTitle": "Edit Article", "subtitle": "Use the Markdown editor to create your article content", "articleInfo": "Article Information", "articleTitle": "Title", "titlePlaceholder": "Enter article title...", "urlSlug": "URL Slug", "slugPlaceholder": "article-slug", "excerpt": "Article Excerpt", "excerptPlaceholder": "Enter article excerpt...", "aiGenerate": "AI Generate", "generating": "Generating...", "content": "Article Content", "contentDesc": "Write your article content using Markdown syntax", "contentPlaceholder": "Start writing your article content...", "preview": "Preview", "edit": "Edit", "saveDraft": "Save Draft", "saving": "Saving...", "publish": "Publish Article", "publishSettings": "Publish Settings", "category": "Category", "selectCategory": "Select Category", "tags": "Tags", "coverImage": "Cover Image URL", "coverImagePlaceholder": "https://example.com/image.jpg", "statistics": {"characters": "Characters", "readingTime": "Reading Time", "status": "Status"}}, "profile": {"title": "Profile", "subtitle": "Manage your account information and preferences", "basicInfo": "Basic Information", "basicInfoDesc": "Your account basic information", "userId": "User ID", "role": "Role", "permissions": "Permissions", "permissionsDesc": "Your permission level in the system", "viewArticles": "View Articles", "createArticles": "Create Articles", "deleteArticles": "Delete Articles", "fileManagement": "File Management", "systemAdmin": "System Administration", "allowed": "✓ Allowed", "denied": "✗ Denied", "roles": {"admin": "Administrator", "collaborator": "Collaborator", "user": "User"}}, "errors": {"notFound": "Page Not Found", "articleNotFound": "Article Not Found", "articleNotFoundDesc": "Sorry, the article you're looking for doesn't exist or has been deleted", "possibleReasons": "Possible reasons:", "wrongLink": "Incorrect article link", "articleDeleted": "Article has been deleted", "articleNotPublished": "Article is not yet published", "backToArticles": "Back to Articles", "backToHome": "Back to Home", "serverError": "Server Error", "somethingWrong": "Something went wrong", "tryAgain": "Please try again later"}, "footer": {"description": "Modern blog system built with Next.js", "quickLinks": "Quick Links", "features": "Features", "other": "Other", "builtWith": "Built with ❤️ using Next.js, Tailwind CSS, and Shadcn UI", "allRightsReserved": "All rights reserved"}, "theme": {"toggle": "Toggle theme", "light": "Light mode", "dark": "Dark mode", "system": "System"}, "admin": {"loading": "Loading...", "accessDenied": "Access Denied", "loginRequired": "Please sign in to access the admin panel", "signIn": "Sign In", "insufficientPermissions": "Insufficient Permissions", "adminAccessRequired": "Admin or collaborator permissions required to access this page", "backToHome": "Back to Home", "collaboratorNotice": "You are currently signed in as a collaborator, some features may be limited.", "sidebar": {"title": "Admin Panel", "dashboard": "Dashboard", "content": "Content Management", "articles": "Articles", "pages": "Pages", "categories": "Categories", "tags": "Tags", "media": "Media Files", "users": "User Management", "comments": "Comment Management", "links": "Friend Links", "analytics": "Analytics", "settings": "System Settings", "version": "Version", "lastUpdate": "Last Update"}, "header": {"searchPlaceholder": "Search...", "unknownUser": "Unknown User", "admin": "Admin", "collaborator": "Collaborator", "profile": "Profile", "settings": "Settings", "signOut": "Sign Out"}, "categoryTag": {"title": "Category & Tag Management", "subtitle": "Manage blog categories and tags to organize content structure", "loading": "Loading...", "categories": "Categories", "tags": "Tags", "createCategory": "Create Category", "createTag": "Create Tag", "editCategory": "Edit Category", "editTag": "Edit Tag", "categoryDescription": "Categories are used to organize main content types of articles", "tagDescription": "Tags are used to mark specific topics and keywords of articles", "searchPlaceholder": "Search categories or tags...", "name": "Name", "slug": "URL Slug", "description": "Description", "color": "Color", "articleCount": "Article Count", "updatedAt": "Updated At", "namePlaceholder": "Enter name", "slugPlaceholder": "url-slug", "descriptionPlaceholder": "Enter description (optional)", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "create": "Create", "update": "Update", "confirmDelete": "Are you sure you want to delete this item? This action cannot be undone."}}, "comments": {"title": "Comments", "loading": "Loading comments...", "configRequired": "Comments feature requires Giscus environment variables configuration", "configInstructions": "Please configure in .env.local:", "noComments": "No comments yet", "writeComment": "Write a comment", "reply": "Reply", "edit": "Edit", "delete": "Delete", "reactions": "Reactions", "signInToComment": "Sign in to join the discussion"}, "analytics": {"title": "Analytics", "subtitle": "View website traffic statistics and content performance", "loading": "Loading...", "noData": "No data available", "periods": {"7d": "Last 7 days", "30d": "Last 30 days", "90d": "Last 90 days", "1y": "Last year"}, "metrics": {"totalViews": "Total Views", "uniqueVisitors": "Unique Visitors", "articlesViewed": "Articles Viewed", "countries": "Countries", "views": "views", "visitors": "visitors"}, "charts": {"viewsTrend": "Views Trend", "viewsTrendDesc": "Website traffic over time", "topArticles": "Top Articles", "topArticlesDesc": "Most viewed articles", "geography": "Geographic Distribution", "geographyDesc": "Visitor geographic distribution"}}, "toast": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "loading": "Loading..."}, "api": {"loading": "Processing request...", "success": "Request successful", "error": "Request failed"}, "form": {"submitSuccess": "Submitted successfully", "submitError": "Submission failed", "validationError": "Form validation failed"}, "fileUpload": {"uploading": "Uploading", "uploadSuccess": "Upload successful", "uploadError": "Upload failed"}, "copy": {"success": "<PERSON><PERSON>d successfully", "error": "Co<PERSON> failed"}, "accessibility": {"skipToMain": "Skip to main content", "skipToNavigation": "Skip to navigation", "skipToFooter": "Skip to footer", "skipLinks": "Skip links", "shortcuts": {"title": "Keyboard Shortcuts", "description": "Use the following shortcuts to navigate and operate quickly", "showHelp": "Show keyboard shortcuts help", "search": "Search", "home": "Go to home", "articles": "Articles list", "dashboard": "Admin dashboard", "help": "Show help", "close": "Close", "categories": {"navigation": "Navigation", "general": "General", "help": "Help"}}}, "rss": {"title": "RSS Subscription", "subtitle": "Subscribe to our latest content via RSS readers", "subscribe": "Subscribe", "copied": "Copied {type} link", "copyFailed": "Co<PERSON> failed", "filtered": "Filter", "rssDescription": "Standard RSS 2.0 format", "atomDescription": "Modern Atom 1.0 format", "jsonDescription": "Lightweight JSON Feed format", "howToUse": "How to Use RSS", "readers": {"title": "RSS Readers", "description": "Use professional RSS readers for the best experience"}, "browsers": {"title": "Browser Support", "description": "Modern browsers can directly open RSS links to preview content"}, "apps": {"title": "Mobile Apps", "description": "Use RSS apps on your phone to read updates anytime, anywhere"}}, "friendLinks": {"title": "Friend Links", "description": "Recommended excellent websites and blogs", "loading": "Loading...", "featured": "Featured Links", "allLinks": "All Links", "visit": "Visit", "categories": {"tech": "Technology", "blog": "Blog", "friend": "Friend", "other": "Other"}, "applyTitle": "Apply for Friend Link", "applyDescription": "Quality websites are welcome to apply for friend links, let's build a better web ecosystem together", "requirements": {"title": "Application Requirements", "content": "Healthy website content, no illegal or inappropriate information", "update": "Website maintains regular updates, not long-term inactive", "friendly": "User-friendly website design with good user experience", "https": "Support HTTPS access"}, "applyNow": "Apply Now"}, "meta": {"defaultTitle": "Modern Blog System", "defaultDescription": "A modern blog system built with Next.js, featuring AI summaries, multi-language support, dark mode, and more"}}
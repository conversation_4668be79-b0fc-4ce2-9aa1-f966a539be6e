@echo off
setlocal enabledelayedexpansion

REM 创建 Cloudflare 资源脚本
REM 使用方法: setup-resources.bat

echo 🚀 创建 Cloudflare Workers 资源

REM 检查 wrangler
where wrangler >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: wrangler CLI 未安装
    echo 请运行: npm install -g wrangler
    exit /b 1
)

REM 检查登录状态
wrangler whoami >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未登录 Cloudflare
    echo 请运行: wrangler login
    exit /b 1
)

echo ✓ Cloudflare 认证通过
echo.

REM 创建 D1 数据库
echo 创建 D1 数据库...
wrangler d1 create modern-blog-db
if %errorlevel% neq 0 (
    echo 警告: D1 数据库创建失败或已存在
) else (
    echo ✓ D1 数据库创建成功
)

REM 执行数据库 schema
echo.
echo 执行数据库 schema...
wrangler d1 execute modern-blog-db --file=./schema.sql
if %errorlevel% neq 0 (
    echo 错误: 数据库 schema 执行失败
    echo 请检查 schema.sql 文件是否存在
) else (
    echo ✓ 数据库 schema 执行成功
)

REM 创建 KV 命名空间
echo.
echo 创建 KV 命名空间...

echo 创建 CACHE 命名空间...
wrangler kv:namespace create "CACHE"
if %errorlevel% neq 0 (
    echo 警告: CACHE 命名空间创建失败或已存在
) else (
    echo ✓ CACHE 命名空间创建成功
)

echo 创建 SESSIONS 命名空间...
wrangler kv:namespace create "SESSIONS"
if %errorlevel% neq 0 (
    echo 警告: SESSIONS 命名空间创建失败或已存在
) else (
    echo ✓ SESSIONS 命名空间创建成功
)

REM 创建 R2 存储桶
echo.
echo 创建 R2 存储桶...
wrangler r2 bucket create modern-blog-storage
if %errorlevel% neq 0 (
    echo 警告: R2 存储桶创建失败或已存在
) else (
    echo ✓ R2 存储桶创建成功
)

echo.
echo 🎉 资源创建完成!
echo.
echo 创建的资源:
echo - D1 数据库: modern-blog-db
echo - KV 命名空间: CACHE, SESSIONS
echo - R2 存储桶: modern-blog-storage
echo.
echo ⚠️  重要提醒:
echo 1. 请将创建的资源 ID 更新到 wrangler.toml 文件中
echo 2. 运行以下命令获取资源 ID:
echo.
echo    wrangler d1 list
echo    wrangler kv:namespace list
echo    wrangler r2 bucket list
echo.
echo 3. 更新 wrangler.toml 中的对应 ID
echo.
echo 下一步:
echo 1. 更新 wrangler.toml 配置文件
echo 2. 设置环境变量 (运行 setup-secrets.bat)
echo 3. 部署 Worker (运行 deploy.bat)

endlocal

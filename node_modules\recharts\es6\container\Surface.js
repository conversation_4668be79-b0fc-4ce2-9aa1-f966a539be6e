var _excluded = ["children", "width", "height", "viewBox", "className", "style", "title", "desc"];
function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }
function _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }
function _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }
/**
 * @fileOverview Surface
 */
import * as React from 'react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';
import { filterProps } from '../util/ReactUtils';
export var Surface = /*#__PURE__*/forwardRef((props, ref) => {
  var {
      children,
      width,
      height,
      viewBox,
      className,
      style,
      title,
      desc
    } = props,
    others = _objectWithoutProperties(props, _excluded);
  var svgView = viewBox || {
    width,
    height,
    x: 0,
    y: 0
  };
  var layerClass = clsx('recharts-surface', className);
  return /*#__PURE__*/React.createElement("svg", _extends({}, filterProps(others, true, 'svg'), {
    className: layerClass,
    width: width,
    height: height,
    style: style,
    viewBox: "".concat(svgView.x, " ").concat(svgView.y, " ").concat(svgView.width, " ").concat(svgView.height),
    ref: ref
  }), /*#__PURE__*/React.createElement("title", null, title), /*#__PURE__*/React.createElement("desc", null, desc), children);
});
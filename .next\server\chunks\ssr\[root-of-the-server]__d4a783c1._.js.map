{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      // 确保按钮有适当的类型\n      type={!asChild && !props.type ? \"button\" : props.type}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAa;QACb,MAAM,CAAC,WAAW,CAAC,MAAM,IAAI,GAAG,WAAW,MAAM,IAAI;QACpD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\nimport { useTranslations } from \"next-intl\"\nimport { motion, AnimatePresence } from \"framer-motion\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuSeparator,\n  DropdownMenuLabel,\n} from \"@/components/ui/dropdown-menu\"\nimport { cn } from \"@/lib/utils\"\n\nexport function ThemeToggle() {\n  const { setTheme, theme, systemTheme } = useTheme()\n  const t = useTranslations('theme')\n  const [mounted, setMounted] = React.useState(false)\n  const [isChanging, setIsChanging] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // 主题切换动画\n  const handleThemeChange = async (newTheme: string) => {\n    setIsChanging(true)\n\n    // 添加页面切换动画\n    if (document.startViewTransition) {\n      await document.startViewTransition(() => {\n        setTheme(newTheme)\n      }).finished\n    } else {\n      setTheme(newTheme)\n    }\n\n    setTimeout(() => setIsChanging(false), 300)\n  }\n\n  // 获取当前实际主题\n  const currentTheme = theme === 'system' ? systemTheme : theme\n\n  if (!mounted) {\n    return (\n      <Button variant=\"ghost\" size=\"icon\" disabled>\n        <div className=\"h-[1.2rem] w-[1.2rem] animate-pulse bg-muted rounded\" />\n        <span className=\"sr-only\">Loading theme</span>\n      </Button>\n    )\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className={cn(\n            \"relative overflow-hidden transition-all duration-300 hover:scale-105 hover:bg-accent\",\n            isChanging && \"animate-pulse\"\n          )}\n          disabled={isChanging}\n        >\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={currentTheme}\n              initial={{ rotate: -90, scale: 0 }}\n              animate={{ rotate: 0, scale: 1 }}\n              exit={{ rotate: 90, scale: 0 }}\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\n              className=\"absolute inset-0 flex items-center justify-center\"\n            >\n              {currentTheme === 'dark' ? (\n                <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n              ) : (\n                <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n              )}\n            </motion.div>\n          </AnimatePresence>\n          <span className=\"sr-only\">{t('toggle')}</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\" className=\"w-48\">\n        <DropdownMenuLabel className=\"flex items-center gap-2\">\n          <Palette className=\"h-4 w-4\" />\n          {t('appearance')}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n\n        <DropdownMenuItem\n          onClick={() => handleThemeChange(\"light\")}\n          className=\"flex items-center gap-3 cursor-pointer\"\n          disabled={isChanging}\n        >\n          <Sun className=\"h-4 w-4\" />\n          <span className=\"flex-1\">{t('light')}</span>\n          <AnimatePresence>\n            {theme === \"light\" && (\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                exit={{ scale: 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Check className=\"h-4 w-4 text-primary\" />\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem\n          onClick={() => handleThemeChange(\"dark\")}\n          className=\"flex items-center gap-3 cursor-pointer\"\n          disabled={isChanging}\n        >\n          <Moon className=\"h-4 w-4\" />\n          <span className=\"flex-1\">{t('dark')}</span>\n          <AnimatePresence>\n            {theme === \"dark\" && (\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                exit={{ scale: 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Check className=\"h-4 w-4 text-primary\" />\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem\n          onClick={() => handleThemeChange(\"system\")}\n          className=\"flex items-center gap-3 cursor-pointer\"\n          disabled={isChanging}\n        >\n          <Monitor className=\"h-4 w-4\" />\n          <span className=\"flex-1\">{t('system')}</span>\n          <AnimatePresence>\n            {theme === \"system\" && (\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                exit={{ scale: 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Check className=\"h-4 w-4 text-primary\" />\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </DropdownMenuItem>\n\n        {theme === \"system\" && (\n          <>\n            <DropdownMenuSeparator />\n            <div className=\"px-2 py-1.5 text-xs text-muted-foreground\">\n              {t('systemDetected')}: {systemTheme === 'dark' ? t('dark') : t('light')}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAGA;AACA;AAQA;AAjBA;;;;;;;;;;AAmBO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAChD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,oBAAoB,OAAO;QAC/B,cAAc;QAEd,WAAW;QACX,IAAI,SAAS,mBAAmB,EAAE;YAChC,MAAM,SAAS,mBAAmB,CAAC;gBACjC,SAAS;YACX,GAAG,QAAQ;QACb,OAAO;YACL,SAAS;QACX;QAEA,WAAW,IAAM,cAAc,QAAQ;IACzC;IAEA,WAAW;IACX,MAAM,eAAe,UAAU,WAAW,cAAc;IAExD,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAQ,MAAK;YAAO,QAAQ;;8BAC1C,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,cAAc;oBAEhB,UAAU;;sCAEV,8OAAC;4BAAgB,MAAK;sCACpB,cAAA,8OAAC,OAAO,GAAG;gCAET,SAAS;oCAAE,QAAQ,CAAC;oCAAI,OAAO;gCAAE;gCACjC,SAAS;oCAAE,QAAQ;oCAAG,OAAO;gCAAE;gCAC/B,MAAM;oCAAE,QAAQ;oCAAI,OAAO;gCAAE;gCAC7B,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAY;gCAC/C,WAAU;0CAET,iBAAiB,uBAChB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;+BAVZ;;;;;;;;;;sCAcT,8OAAC;4BAAK,WAAU;sCAAW,EAAE;;;;;;;;;;;;;;;;;0BAGjC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAClB,EAAE;;;;;;;kCAEL,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,kBAAkB;wBACjC,WAAU;wBACV,UAAU;;0CAEV,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAU,EAAE;;;;;;0CAC5B,8OAAC;0CACE,UAAU,yBACT,8OAAC,OAAO,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,MAAM;wCAAE,OAAO;oCAAE;oCACjB,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,kBAAkB;wBACjC,WAAU;wBACV,UAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU,EAAE;;;;;;0CAC5B,8OAAC;0CACE,UAAU,wBACT,8OAAC,OAAO,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,MAAM;wCAAE,OAAO;oCAAE;oCACjB,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,kBAAkB;wBACjC,WAAU;wBACV,UAAU;;0CAEV,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAK,WAAU;0CAAU,EAAE;;;;;;0CAC5B,8OAAC;0CACE,UAAU,0BACT,8OAAC,OAAO,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,MAAM;wCAAE,OAAO;oCAAE;oCACjB,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;oBAMxB,UAAU,0BACT;;0CACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC;gCAAI,WAAU;;oCACZ,EAAE;oCAAkB;oCAAG,gBAAgB,SAAS,EAAE,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;AAO7E", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/auth/user-nav.tsx"], "sourcesContent": ["\"use client\"\n\nimport { signOut, useSession } from \"next-auth/react\"\nimport Link from \"next/link\"\nimport { LogOut, <PERSON>tings, User, Shield, FileText } from \"lucide-react\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Badge } from \"@/components/ui/badge\"\n\nexport function UserNav() {\n  const { data: session, status } = useSession()\n\n  if (status === \"loading\") {\n    return (\n      <div className=\"w-8 h-8 rounded-full bg-muted animate-pulse\" />\n    )\n  }\n\n  if (!session?.user) {\n    return (\n      <Button asChild variant=\"outline\" size=\"sm\">\n        <Link href=\"/auth/signin\">\n          登录\n        </Link>\n      </Button>\n    )\n  }\n\n  const { user } = session\n  const initials = user.name\n    ?.split(' ')\n    .map(n => n[0])\n    .join('')\n    .toUpperCase() || '?'\n\n  const getRoleBadge = (role: string) => {\n    switch (role) {\n      case 'admin':\n        return <Badge variant=\"destructive\" className=\"text-xs\">管理员</Badge>\n      case 'collaborator':\n        return <Badge variant=\"secondary\" className=\"text-xs\">协作者</Badge>\n      default:\n        return <Badge variant=\"outline\" className=\"text-xs\">用户</Badge>\n    }\n  }\n\n  const handleSignOut = () => {\n    signOut({ callbackUrl: '/' })\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n          <Avatar className=\"h-8 w-8\">\n            <AvatarImage src={user.image || ''} alt={user.name || ''} />\n            <AvatarFallback>{initials}</AvatarFallback>\n          </Avatar>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">{user.name}</p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"pt-1\">\n              {getRoleBadge(user.role)}\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\">\n            <User className=\"mr-2 h-4 w-4\" />\n            个人资料\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/settings\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            设置\n          </Link>\n        </DropdownMenuItem>\n        \n        {(user.role === 'admin' || user.role === 'collaborator') && (\n          <>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem asChild>\n              <Link href=\"/dashboard\">\n                <FileText className=\"mr-2 h-4 w-4\" />\n                内容管理\n              </Link>\n            </DropdownMenuItem>\n          </>\n        )}\n        \n        {user.role === 'admin' && (\n          <DropdownMenuItem asChild>\n            <Link href=\"/admin\">\n              <Shield className=\"mr-2 h-4 w-4\" />\n              系统管理\n            </Link>\n          </DropdownMenuItem>\n        )}\n        \n        <DropdownMenuSeparator />\n        <DropdownMenuItem onClick={handleSignOut}>\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          退出登录\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAQA;AAfA;;;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAE3C,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,CAAC,SAAS,MAAM;QAClB,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,OAAO;YAAC,SAAQ;YAAU,MAAK;sBACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;0BAAe;;;;;;;;;;;IAKhC;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,WAAW,KAAK,IAAI,EACtB,MAAM,KACP,IAAI,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,KAAK,IACL,iBAAiB;IAEpB,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,WAAU;8BAAU;;;;;;YAC1D,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,WAAU;8BAAU;;;;;;YACxD;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAU;;;;;;QACxD;IACF;IAEA,MAAM,gBAAgB;QACpB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAI;IAC7B;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,WAAU;8BAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,kIAAA,CAAA,cAAW;gCAAC,KAAK,KAAK,KAAK,IAAI;gCAAI,KAAK,KAAK,IAAI,IAAI;;;;;;0CACtD,8OAAC,kIAAA,CAAA,iBAAc;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAIvB,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAoC,KAAK,IAAI;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,aAAa,KAAK,IAAI;;;;;;;;;;;;;;;;;kCAI7B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAKrC,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;oBAKxC,CAAC,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,cAAc,mBACrD;;0CACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,mBAAgB;gCAAC,OAAO;0CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;oBAO5C,KAAK,IAAI,KAAK,yBACb,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAMzC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS;;0CACzB,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 1336, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1510, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Command as CommandPrimitive } from \"cmdk\"\nimport { SearchIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\"\n\nfunction Command({\n  className,\n  ...props\n}: React.ComponentProps<typeof CommandPrimitive>) {\n  return (\n    <CommandPrimitive\n      data-slot=\"command\"\n      className={cn(\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CommandDialog({\n  title = \"Command Palette\",\n  description = \"Search for a command to run...\",\n  children,\n  className,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof Dialog> & {\n  title?: string\n  description?: string\n  className?: string\n  showCloseButton?: boolean\n}) {\n  return (\n    <Dialog {...props}>\n      <DialogHeader className=\"sr-only\">\n        <DialogTitle>{title}</DialogTitle>\n        <DialogDescription>{description}</DialogDescription>\n      </DialogHeader>\n      <DialogContent\n        className={cn(\"overflow-hidden p-0\", className)}\n        showCloseButton={showCloseButton}\n      >\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\n          {children}\n        </Command>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\nfunction CommandInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\n  return (\n    <div\n      data-slot=\"command-input-wrapper\"\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\n    >\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\n      <CommandPrimitive.Input\n        data-slot=\"command-input\"\n        className={cn(\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction CommandList({\n  className,\n  ...props\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\n  return (\n    <CommandPrimitive.List\n      data-slot=\"command-list\"\n      className={cn(\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CommandEmpty({\n  ...props\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\n  return (\n    <CommandPrimitive.Empty\n      data-slot=\"command-empty\"\n      className=\"py-6 text-center text-sm\"\n      {...props}\n    />\n  )\n}\n\nfunction CommandGroup({\n  className,\n  ...props\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\n  return (\n    <CommandPrimitive.Group\n      data-slot=\"command-group\"\n      className={cn(\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CommandSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\n  return (\n    <CommandPrimitive.Separator\n      data-slot=\"command-separator\"\n      className={cn(\"bg-border -mx-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CommandItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\n  return (\n    <CommandPrimitive.Item\n      data-slot=\"command-item\"\n      className={cn(\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CommandShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"command-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Command,\n  CommandDialog,\n  CommandInput,\n  CommandList,\n  CommandEmpty,\n  CommandGroup,\n  CommandItem,\n  CommandShortcut,\n  CommandSeparator,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,8OAAC,sIAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,SAAS,EACT,kBAAkB,IAAI,EACtB,GAAG,OAMJ;IACC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,8OAAC,kIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,8OAAC,kIAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,8OAAC,kIAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;gBACrC,iBAAiB;0BAEjB,cAAA,8OAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;;0BAEV,8OAAC,0MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1694, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/search/global-search.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef, useCallback } from \"react\"\nimport { useRouter } from \"next/navigation\"\nimport { useTranslations } from \"next-intl\"\nimport { Search, X, ArrowRight, Clock, TrendingUp, FileText, Hash, User, Calendar } from \"lucide-react\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Badge } from \"@/components/ui/badge\"\nimport {\n  CommandDialog,\n  CommandEmpty,\n  CommandGroup,\n  CommandInput,\n  CommandItem,\n  CommandList,\n  CommandSeparator\n} from \"@/components/ui/command\"\nimport { debounce, cn } from \"@/lib/utils\"\n\ninterface SearchResult {\n  id: string\n  type: 'article' | 'tag' | 'category' | 'user'\n  title: string\n  excerpt?: string\n  slug: string\n  category?: string\n  tags?: string[]\n  publishedAt?: string\n  author?: string\n  highlight?: string\n}\n\ninterface SearchHistory {\n  query: string\n  timestamp: number\n  resultCount: number\n}\n\nexport function GlobalSearch() {\n  const router = useRouter()\n  const t = useTranslations('search')\n  const [open, setOpen] = useState(false)\n  const [query, setQuery] = useState(\"\")\n  const [results, setResults] = useState<SearchResult[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([])\n  const [suggestions, setSuggestions] = useState<string[]>([])\n  const [selectedIndex, setSelectedIndex] = useState(-1)\n  \n  // 加载搜索历史\n  useEffect(() => {\n    const savedHistory = localStorage.getItem('searchHistory')\n    if (savedHistory) {\n      try {\n        setSearchHistory(JSON.parse(savedHistory))\n      } catch (error) {\n        console.error('Failed to parse search history:', error)\n      }\n    }\n  }, [])\n\n  // 保存搜索历史\n  const saveSearchHistory = useCallback((searchQuery: string, resultCount: number) => {\n    const newHistory: SearchHistory = {\n      query: searchQuery,\n      timestamp: Date.now(),\n      resultCount,\n    }\n\n    const updatedHistory = [\n      newHistory,\n      ...searchHistory.filter(item => item.query !== searchQuery)\n    ].slice(0, 10) // 保留最近10条\n\n    setSearchHistory(updatedHistory)\n    localStorage.setItem('searchHistory', JSON.stringify(updatedHistory))\n  }, [searchHistory])\n\n  // 清除搜索历史\n  const clearSearchHistory = useCallback(() => {\n    setSearchHistory([])\n    localStorage.removeItem('searchHistory')\n  }, [])\n\n  // 监听快捷键\n  useEffect(() => {\n    const down = (e: KeyboardEvent) => {\n      if (e.key === \"k\" && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault()\n        setOpen((open) => !open)\n      }\n\n      if (open) {\n        if (e.key === \"ArrowDown\") {\n          e.preventDefault()\n          setSelectedIndex(prev =>\n            prev < results.length - 1 ? prev + 1 : prev\n          )\n        } else if (e.key === \"ArrowUp\") {\n          e.preventDefault()\n          setSelectedIndex(prev => prev > -1 ? prev - 1 : prev)\n        } else if (e.key === \"Enter\" && selectedIndex >= 0) {\n          e.preventDefault()\n          const selectedResult = results[selectedIndex]\n          if (selectedResult) {\n            handleResultClick(selectedResult)\n          }\n        }\n      }\n    }\n\n    document.addEventListener(\"keydown\", down)\n    return () => document.removeEventListener(\"keydown\", down)\n  }, [open, results, selectedIndex])\n  \n  // 获取搜索建议\n  const getSuggestions = useCallback(async (searchQuery: string) => {\n    if (!searchQuery.trim() || searchQuery.length < 2) {\n      setSuggestions([])\n      return\n    }\n\n    try {\n      const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(searchQuery)}`)\n      if (response.ok) {\n        const data = await response.json()\n        if (data.success) {\n          setSuggestions(data.data.suggestions || [])\n        }\n      }\n    } catch (error) {\n      console.error('Failed to get suggestions:', error)\n    }\n  }, [])\n\n  // 执行搜索\n  const performSearch = debounce(async (searchQuery: string) => {\n    if (!searchQuery.trim()) {\n      setResults([])\n      setSuggestions([])\n      return\n    }\n\n    setIsLoading(true)\n    setSelectedIndex(-1)\n\n    try {\n      const params = new URLSearchParams({\n        q: searchQuery,\n        limit: \"8\",\n        highlight: \"true\"\n      })\n\n      const response = await fetch(`/api/search?${params.toString()}`)\n\n      if (!response.ok) {\n        throw new Error(t('searchFailed'))\n      }\n\n      const data = await response.json()\n\n      if (data.success) {\n        const searchResults = data.data.results || []\n        setResults(searchResults)\n\n        // 保存搜索历史\n        if (searchResults.length > 0) {\n          saveSearchHistory(searchQuery, searchResults.length)\n        }\n      } else {\n        throw new Error(data.error || t('searchFailed'))\n      }\n    } catch (err) {\n      console.error(\"Search error:\", err)\n      setResults([])\n    } finally {\n      setIsLoading(false)\n    }\n  }, 300)\n  \n  // 当查询变化时执行搜索和获取建议\n  useEffect(() => {\n    if (open) {\n      performSearch(query)\n      getSuggestions(query)\n    }\n  }, [query, open, performSearch, getSuggestions])\n\n  // 处理搜索结果点击\n  const handleResultClick = (result: SearchResult) => {\n    let path = ''\n    switch (result.type) {\n      case 'article':\n        path = `/articles/${result.slug}`\n        break\n      case 'tag':\n        path = `/tags/${result.slug}`\n        break\n      case 'category':\n        path = `/categories/${result.slug}`\n        break\n      case 'user':\n        path = `/users/${result.slug}`\n        break\n      default:\n        path = `/search?q=${encodeURIComponent(query)}`\n    }\n\n    setOpen(false)\n    router.push(path)\n  }\n\n  // 处理历史记录点击\n  const handleHistoryClick = (historyQuery: string) => {\n    setQuery(historyQuery)\n  }\n\n  // 处理建议点击\n  const handleSuggestionClick = (suggestion: string) => {\n    setQuery(suggestion)\n  }\n\n  // 处理查看所有结果\n  const handleViewAllResults = () => {\n    setOpen(false)\n    router.push(`/search?q=${encodeURIComponent(query)}`)\n  }\n\n  // 高亮搜索关键词\n  const highlightText = (text: string, highlight: string) => {\n    if (!highlight || !text) return text\n\n    const regex = new RegExp(`(${highlight})`, 'gi')\n    const parts = text.split(regex)\n\n    return parts.map((part, index) =>\n      regex.test(part) ? (\n        <mark key={index} className=\"bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded\">\n          {part}\n        </mark>\n      ) : part\n    )\n  }\n\n  // 获取结果图标\n  const getResultIcon = (type: string) => {\n    switch (type) {\n      case 'article':\n        return <FileText className=\"h-4 w-4\" />\n      case 'tag':\n        return <Hash className=\"h-4 w-4\" />\n      case 'category':\n        return <Hash className=\"h-4 w-4\" />\n      case 'user':\n        return <User className=\"h-4 w-4\" />\n      default:\n        return <FileText className=\"h-4 w-4\" />\n    }\n  }\n  \n  return (\n    <>\n      <Button\n        variant=\"outline\"\n        className=\"relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64 hover:bg-accent transition-colors\"\n        onClick={() => setOpen(true)}\n      >\n        <Search className=\"h-4 w-4 mr-2\" />\n        <span className=\"hidden lg:inline-flex\">{t('placeholder')}</span>\n        <span className=\"inline-flex lg:hidden\">{t('search')}</span>\n        <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\n          <span className=\"text-xs\">⌘</span>K\n        </kbd>\n      </Button>\n      \n      <CommandDialog open={open} onOpenChange={setOpen}>\n        <CommandInput\n          placeholder={t('inputPlaceholder')}\n          value={query}\n          onValueChange={setQuery}\n        />\n        <CommandList>\n          <CommandEmpty>\n            {isLoading ? (\n              <div className=\"py-6 text-center text-sm\">\n                <div className=\"animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent mx-auto\"></div>\n                <p className=\"mt-2\">搜索中...</p>\n              </div>\n            ) : (\n              <div className=\"py-6 text-center text-sm\">\n                没有找到与 \"{query}\" 相关的内容\n              </div>\n            )}\n          </CommandEmpty>\n          \n          {results.length > 0 && (\n            <>\n              <CommandGroup heading=\"文章\">\n                {results.map((article) => (\n                  <CommandItem\n                    key={article.id}\n                    onSelect={() => handleResultClick(article.slug)}\n                    className=\"flex items-center justify-between\"\n                  >\n                    <div>\n                      <div className=\"font-medium\">{article.title}</div>\n                      {article.excerpt && (\n                        <div className=\"text-xs text-muted-foreground line-clamp-1\">\n                          {article.excerpt}\n                        </div>\n                      )}\n                    </div>\n                    <ArrowRight className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\n                  </CommandItem>\n                ))}\n              </CommandGroup>\n              \n              <CommandSeparator />\n              \n              <CommandGroup>\n                <CommandItem\n                  onSelect={handleViewAllResults}\n                  className=\"justify-center text-center\"\n                >\n                  <div className=\"flex items-center\">\n                    <Search className=\"mr-2 h-4 w-4\" />\n                    查看所有结果\n                  </div>\n                </CommandItem>\n              </CommandGroup>\n            </>\n          )}\n        </CommandList>\n      </CommandDialog>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAGA;AASA;AAnBA;;;;;;;;;AAwCO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAEpD,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,cAAc;YAChB,IAAI;gBACF,iBAAiB,KAAK,KAAK,CAAC;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF;IACF,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,aAAqB;QAC1D,MAAM,aAA4B;YAChC,OAAO;YACP,WAAW,KAAK,GAAG;YACnB;QACF;QAEA,MAAM,iBAAiB;YACrB;eACG,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;SAChD,CAAC,KAAK,CAAC,GAAG,IAAI,UAAU;;QAEzB,iBAAiB;QACjB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD,GAAG;QAAC;KAAc;IAElB,SAAS;IACT,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,iBAAiB,EAAE;QACnB,aAAa,UAAU,CAAC;IAC1B,GAAG,EAAE;IAEL,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,CAAC;YACZ,IAAI,EAAE,GAAG,KAAK,OAAO,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;gBAC7C,EAAE,cAAc;gBAChB,QAAQ,CAAC,OAAS,CAAC;YACrB;YAEA,IAAI,MAAM;gBACR,IAAI,EAAE,GAAG,KAAK,aAAa;oBACzB,EAAE,cAAc;oBAChB,iBAAiB,CAAA,OACf,OAAO,QAAQ,MAAM,GAAG,IAAI,OAAO,IAAI;gBAE3C,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;oBAC9B,EAAE,cAAc;oBAChB,iBAAiB,CAAA,OAAQ,OAAO,CAAC,IAAI,OAAO,IAAI;gBAClD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW,iBAAiB,GAAG;oBAClD,EAAE,cAAc;oBAChB,MAAM,iBAAiB,OAAO,CAAC,cAAc;oBAC7C,IAAI,gBAAgB;wBAClB,kBAAkB;oBACpB;gBACF;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAM;QAAS;KAAc;IAEjC,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI,CAAC,YAAY,IAAI,MAAM,YAAY,MAAM,GAAG,GAAG;YACjD,eAAe,EAAE;YACjB;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,0BAA0B,EAAE,mBAAmB,cAAc;YAC3F,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,eAAe,KAAK,IAAI,CAAC,WAAW,IAAI,EAAE;gBAC5C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QACpC,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,WAAW,EAAE;YACb,eAAe,EAAE;YACjB;QACF;QAEA,aAAa;QACb,iBAAiB,CAAC;QAElB,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,GAAG;gBACH,OAAO;gBACP,WAAW;YACb;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;YAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,EAAE;YACpB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,gBAAgB,KAAK,IAAI,CAAC,OAAO,IAAI,EAAE;gBAC7C,WAAW;gBAEX,SAAS;gBACT,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,kBAAkB,aAAa,cAAc,MAAM;gBACrD;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,EAAE;YAClC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,WAAW,EAAE;QACf,SAAU;YACR,aAAa;QACf;IACF,GAAG;IAEH,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,cAAc;YACd,eAAe;QACjB;IACF,GAAG;QAAC;QAAO;QAAM;QAAe;KAAe;IAE/C,WAAW;IACX,MAAM,oBAAoB,CAAC;QACzB,IAAI,OAAO;QACX,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,OAAO,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE;gBACjC;YACF,KAAK;gBACH,OAAO,CAAC,MAAM,EAAE,OAAO,IAAI,EAAE;gBAC7B;YACF,KAAK;gBACH,OAAO,CAAC,YAAY,EAAE,OAAO,IAAI,EAAE;gBACnC;YACF,KAAK;gBACH,OAAO,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE;gBAC9B;YACF;gBACE,OAAO,CAAC,UAAU,EAAE,mBAAmB,QAAQ;QACnD;QAEA,QAAQ;QACR,OAAO,IAAI,CAAC;IACd;IAEA,WAAW;IACX,MAAM,qBAAqB,CAAC;QAC1B,SAAS;IACX;IAEA,SAAS;IACT,MAAM,wBAAwB,CAAC;QAC7B,SAAS;IACX;IAEA,WAAW;IACX,MAAM,uBAAuB;QAC3B,QAAQ;QACR,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,QAAQ;IACtD;IAEA,UAAU;IACV,MAAM,gBAAgB,CAAC,MAAc;QACnC,IAAI,CAAC,aAAa,CAAC,MAAM,OAAO;QAEhC,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE;QAC3C,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,QACtB,MAAM,IAAI,CAAC,sBACT,8OAAC;gBAAiB,WAAU;0BACzB;eADQ;;;;uBAGT;IAER;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,qBACE;;0BACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,WAAU;gBACV,SAAS,IAAM,QAAQ;;kCAEvB,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAK,WAAU;kCAAyB,EAAE;;;;;;kCAC3C,8OAAC;wBAAK,WAAU;kCAAyB,EAAE;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;4BAAQ;;;;;;;;;;;;;0BAItC,8OAAC,mIAAA,CAAA,gBAAa;gBAAC,MAAM;gBAAM,cAAc;;kCACvC,8OAAC,mIAAA,CAAA,eAAY;wBACX,aAAa,EAAE;wBACf,OAAO;wBACP,eAAe;;;;;;kCAEjB,8OAAC,mIAAA,CAAA,cAAW;;0CACV,8OAAC,mIAAA,CAAA,eAAY;0CACV,0BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;yDAGtB,8OAAC;oCAAI,WAAU;;wCAA2B;wCAChC;wCAAM;;;;;;;;;;;;4BAKnB,QAAQ,MAAM,GAAG,mBAChB;;kDACE,8OAAC,mIAAA,CAAA,eAAY;wCAAC,SAAQ;kDACnB,QAAQ,GAAG,CAAC,CAAC,wBACZ,8OAAC,mIAAA,CAAA,cAAW;gDAEV,UAAU,IAAM,kBAAkB,QAAQ,IAAI;gDAC9C,WAAU;;kEAEV,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;4DAC1C,QAAQ,OAAO,kBACd,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,OAAO;;;;;;;;;;;;kEAItB,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;+CAZjB,QAAQ,EAAE;;;;;;;;;;kDAiBrB,8OAAC,mIAAA,CAAA,mBAAgB;;;;;kDAEjB,8OAAC,mIAAA,CAAA,eAAY;kDACX,cAAA,8OAAC,mIAAA,CAAA,cAAW;4CACV,UAAU;4CACV,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD", "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/config/site.ts"], "sourcesContent": ["export const siteConfig = {\n  name: \"现代博客系统\",\n  description: \"基于 Next.js 的现代化博客系统，支持 AI 摘要、多语言、深色模式等功能\",\n  url: \"https://your-domain.com\",\n  ogImage: \"https://your-domain.com/og.jpg\",\n  links: {\n    github: \"https://github.com/yourusername/your-repo\",\n    twitter: \"https://twitter.com/yourusername\",\n  },\n  author: {\n    name: \"Your Name\",\n    email: \"<EMAIL>\",\n    twitter: \"@yourusername\",\n  },\n  // 主题配置\n  theme: {\n    defaultTheme: \"light\" as const,\n    enableSystemTheme: true,\n  },\n  // 语言配置\n  i18n: {\n    defaultLocale: \"zh\" as const,\n    locales: [\"zh\", \"en\"] as const,\n  },\n  // 分页配置\n  pagination: {\n    articlesPerPage: 10,\n    commentsPerPage: 20,\n    filesPerPage: 20,\n  },\n  // 文件上传配置\n  upload: {\n    maxFileSize: 10 * 1024 * 1024, // 10MB\n    allowedTypes: [\n      \"image/jpeg\",\n      \"image/png\",\n      \"image/gif\",\n      \"image/webp\",\n      \"application/pdf\",\n      \"text/plain\",\n      \"text/markdown\",\n    ],\n  },\n  // SEO 配置\n  seo: {\n    titleTemplate: \"%s | 现代博客系统\",\n    defaultTitle: \"现代博客系统\",\n    description: \"基于 Next.js 的现代化博客系统\",\n    openGraph: {\n      type: \"website\",\n      locale: \"zh_CN\",\n      url: \"https://your-domain.com\",\n      siteName: \"现代博客系统\",\n    },\n    twitter: {\n      handle: \"@yourusername\",\n      site: \"@yourusername\",\n      cardType: \"summary_large_image\",\n    },\n  },\n  // 功能开关\n  features: {\n    enableComments: true,\n    enableSearch: true,\n    enableAnalytics: true,\n    enableAISummary: true,\n    enableFileManagement: true,\n    enableMultiLanguage: true,\n    enableDarkMode: true,\n  },\n  // 外部服务配置\n  services: {\n    cloudflareWorker: {\n      authUrl: process.env.CLOUDFLARE_AUTH_URL || \"\",\n      fileUrl: process.env.CLOUDFLARE_FILE_URL || \"\",\n    },\n    github: {\n      clientId: process.env.GITHUB_CLIENT_ID || \"\",\n      clientSecret: process.env.GITHUB_CLIENT_SECRET || \"\",\n    },\n    analytics: {\n      googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID || \"\",\n      plausibleDomain: process.env.PLAUSIBLE_DOMAIN || \"\",\n    },\n  },\n};\n\nexport type SiteConfig = typeof siteConfig;\n"], "names": [], "mappings": ";;;AAAO,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK;IACL,SAAS;IACT,OAAO;QACL,QAAQ;QACR,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,OAAO;IACP,OAAO;QACL,cAAc;QACd,mBAAmB;IACrB;IACA,OAAO;IACP,MAAM;QACJ,eAAe;QACf,SAAS;YAAC;YAAM;SAAK;IACvB;IACA,OAAO;IACP,YAAY;QACV,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;IAChB;IACA,SAAS;IACT,QAAQ;QACN,aAAa,KAAK,OAAO;QACzB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;IACT,KAAK;QACH,eAAe;QACf,cAAc;QACd,aAAa;QACb,WAAW;YACT,MAAM;YACN,QAAQ;YACR,KAAK;YACL,UAAU;QACZ;QACA,SAAS;YACP,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;IACF;IACA,OAAO;IACP,UAAU;QACR,gBAAgB;QAChB,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,sBAAsB;QACtB,qBAAqB;QACrB,gBAAgB;IAClB;IACA,SAAS;IACT,UAAU;QACR,kBAAkB;YAChB,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YAC5C,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QAC9C;QACA,QAAQ;YACN,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,WAAW;YACT,mBAAmB,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YACtD,iBAAiB,QAAQ,GAAG,CAAC,gBAAgB,IAAI;QACnD;IACF;AACF", "debugId": null}}, {"offset": {"line": 2269, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { useState } from \"react\"\nimport { Menu, Search, X } from \"lucide-react\"\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { She<PERSON>, Sheet<PERSON>ontent, SheetTrigger } from \"@/components/ui/sheet\"\nimport { ThemeToggle } from \"@/components/theme-toggle\"\nimport { UserNav } from \"@/components/auth/user-nav\"\nimport { GlobalSearch } from \"@/components/search/global-search\"\nimport { siteConfig } from \"@/config/site\"\nimport { useTranslations } from \"next-intl\"\nimport { LanguageToggle } from \"@/components/i18n/language-toggle\"\nimport { cn } from \"@/lib/utils\"\n\nexport function Header() {\n  const t = useTranslations('navigation')\n  const [isSearchOpen, setIsSearchOpen] = useState(false)\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const navigation = [\n    { name: t('home'), href: \"/\" },\n    { name: t('articles'), href: \"/articles\" },\n    { name: t('archive'), href: \"/archive\" },\n    { name: t('now'), href: \"/now\" },\n    { name: t('guestbook'), href: \"/guestbook\" },\n    { name: t('integrations'), href: \"/integrations\" },\n  ]\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center justify-between\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\n          <span className=\"text-lg sm:text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent truncate max-w-[200px] sm:max-w-none\">\n            {siteConfig.name}\n          </span>\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-6\">\n          {navigation.map((item) => (\n            <Link\n              key={item.href}\n              href={item.href}\n              className=\"text-sm font-medium transition-colors hover:text-primary\"\n            >\n              {item.name}\n            </Link>\n          ))}\n        </nav>\n\n        {/* Right Side Actions */}\n        <div className=\"flex items-center space-x-1 sm:space-x-2\">\n          {/* Mobile Search Toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"sm:hidden\"\n            onClick={() => setIsSearchOpen(!isSearchOpen)}\n            aria-label={t('search')}\n          >\n            <Search className=\"h-4 w-4\" />\n          </Button>\n\n          {/* Desktop Search */}\n          <div className=\"hidden sm:block\">\n            <GlobalSearch />\n          </div>\n\n          {/* Language Toggle */}\n          <div className=\"hidden sm:block\">\n            <LanguageToggle />\n          </div>\n\n          {/* Theme Toggle */}\n          <ThemeToggle />\n\n          {/* User Navigation */}\n          <div className=\"hidden sm:block\">\n            <UserNav />\n          </div>\n\n          {/* Mobile Menu */}\n          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>\n            <SheetTrigger asChild>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"md:hidden\"\n                aria-label={t('menu')}\n              >\n                <Menu className=\"h-4 w-4\" />\n              </Button>\n            </SheetTrigger>\n            <SheetContent\n              side=\"right\"\n              className=\"w-[300px] sm:w-[400px] p-0\"\n              onInteractOutside={() => setIsMobileMenuOpen(false)}\n            >\n              <div className=\"flex flex-col h-full\">\n                {/* Mobile Menu Header */}\n                <div className=\"flex items-center justify-between p-4 border-b\">\n                  <h2 className=\"text-lg font-semibold\">{t('menu')}</h2>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    aria-label={t('close')}\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n\n                {/* Navigation Links */}\n                <div className=\"flex-1 overflow-y-auto\">\n                  <nav className=\"p-4 space-y-2\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.href}\n                        href={item.href}\n                        className={cn(\n                          \"flex items-center px-3 py-3 text-base font-medium rounded-lg transition-colors\",\n                          \"hover:bg-accent hover:text-accent-foreground\",\n                          \"focus:bg-accent focus:text-accent-foreground focus:outline-none\",\n                          \"active:bg-accent/80\"\n                        )}\n                        onClick={() => setIsMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </nav>\n\n                  {/* Mobile Search */}\n                  <div className=\"p-4 border-t\">\n                    <div className=\"space-y-3\">\n                      <h3 className=\"text-sm font-medium text-muted-foreground\">\n                        {t('search')}\n                      </h3>\n                      <GlobalSearch />\n                    </div>\n                  </div>\n\n                  {/* Mobile User Actions */}\n                  <div className=\"p-4 border-t\">\n                    <div className=\"space-y-3\">\n                      <h3 className=\"text-sm font-medium text-muted-foreground\">\n                        {t('settings')}\n                      </h3>\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm\">{t('language')}</span>\n                        <LanguageToggle />\n                      </div>\n                      <div className=\"pt-2\">\n                        <UserNav />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </SheetContent>\n          </Sheet>\n        </div>\n\n        {/* Mobile Search Overlay */}\n        {isSearchOpen && (\n          <div className=\"absolute top-full left-0 right-0 bg-background border-b shadow-lg sm:hidden\">\n            <div className=\"container p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"flex-1\">\n                  <GlobalSearch />\n                </div>\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setIsSearchOpen(false)}\n                  aria-label={t('close')}\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;AAEA;AAfA;;;;;;;;;;;;;;AAiBO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAS,MAAM;QAAI;QAC7B;YAAE,MAAM,EAAE;YAAa,MAAM;QAAY;QACzC;YAAE,MAAM,EAAE;YAAY,MAAM;QAAW;QACvC;YAAE,MAAM,EAAE;YAAQ,MAAM;QAAO;QAC/B;YAAE,MAAM,EAAE;YAAc,MAAM;QAAa;QAC3C;YAAE,MAAM,EAAE;YAAiB,MAAM;QAAgB;KAClD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;8BACvB,cAAA,8OAAC;wBAAK,WAAU;kCACb,qHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;;;;;;8BAKpB,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,IAAI;2BAJL,KAAK,IAAI;;;;;;;;;;8BAUpB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,gBAAgB,CAAC;4BAChC,cAAY,EAAE;sCAEd,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAIpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gJAAA,CAAA,eAAY;;;;;;;;;;sCAIf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;;;;;;;;;sCAIH,8OAAC,qIAAA,CAAA,cAAW;;;;;sCAGZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,yIAAA,CAAA,UAAO;;;;;;;;;;sCAIV,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAkB,cAAc;;8CAC3C,8OAAC,iIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,cAAY,EAAE;kDAEd,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGpB,8OAAC,iIAAA,CAAA,eAAY;oCACX,MAAK;oCACL,WAAU;oCACV,mBAAmB,IAAM,oBAAoB;8CAE7C,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyB,EAAE;;;;;;kEACzC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,oBAAoB;wDACnC,cAAY,EAAE;kEAEd,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gEAEH,MAAM,KAAK,IAAI;gEACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,gDACA,mEACA;gEAEF,SAAS,IAAM,oBAAoB;0EAElC,KAAK,IAAI;+DAVL,KAAK,IAAI;;;;;;;;;;kEAgBpB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,EAAE;;;;;;8EAEL,8OAAC,gJAAA,CAAA,eAAY;;;;;;;;;;;;;;;;kEAKjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,EAAE;;;;;;8EAEL,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAW,EAAE;;;;;;sFAC7B,8OAAC;;;;;;;;;;;8EAEH,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,yIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWvB,8BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gJAAA,CAAA,eAAY;;;;;;;;;;8CAEf,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,cAAY,EAAE;8CAEd,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B", "debugId": null}}, {"offset": {"line": 2709, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Gith<PERSON>, Twitter, Mail } from \"lucide-react\"\nimport { siteConfig } from \"@/config/site\"\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container py-8 md:py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* 网站信息 */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">{siteConfig.name}</h3>\n            <p className=\"text-sm text-muted-foreground\">\n              {siteConfig.description}\n            </p>\n            <div className=\"flex space-x-4\">\n              {siteConfig.links.github && (\n                <Link\n                  href={siteConfig.links.github}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-muted-foreground hover:text-foreground\"\n                >\n                  <Github className=\"h-5 w-5\" />\n                </Link>\n              )}\n              {siteConfig.links.twitter && (\n                <Link\n                  href={siteConfig.links.twitter}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-muted-foreground hover:text-foreground\"\n                >\n                  <Twitter className=\"h-5 w-5\" />\n                </Link>\n              )}\n              <Link\n                href={`mailto:${siteConfig.author.email}`}\n                className=\"text-muted-foreground hover:text-foreground\"\n              >\n                <Mail className=\"h-5 w-5\" />\n              </Link>\n            </div>\n          </div>\n\n          {/* 快速链接 */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">快速链接</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/articles\" className=\"text-muted-foreground hover:text-foreground\">\n                  文章列表\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/archive\" className=\"text-muted-foreground hover:text-foreground\">\n                  文章归档\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/tags\" className=\"text-muted-foreground hover:text-foreground\">\n                  标签云\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-muted-foreground hover:text-foreground\">\n                  关于我们\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* 功能页面 */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">功能页面</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/now\" className=\"text-muted-foreground hover:text-foreground\">\n                  此刻\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/guestbook\" className=\"text-muted-foreground hover:text-foreground\">\n                  留言板\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/friends\" className=\"text-muted-foreground hover:text-foreground\">\n                  友情链接\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/changelog\" className=\"text-muted-foreground hover:text-foreground\">\n                  更新日志\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* 其他信息 */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">其他</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/rss\" className=\"text-muted-foreground hover:text-foreground\">\n                  RSS 订阅\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/sitemap.xml\" className=\"text-muted-foreground hover:text-foreground\">\n                  网站地图\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-muted-foreground hover:text-foreground\">\n                  隐私政策\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-muted-foreground hover:text-foreground\">\n                  使用条款\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"mt-8 pt-8 border-t text-center text-sm text-muted-foreground\">\n          <p>\n            © {new Date().getFullYear()} {siteConfig.name}. All rights reserved.\n          </p>\n          <p className=\"mt-2\">\n            Built with ❤️ using Next.js, Tailwind CSS, and Shadcn UI\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyB,qHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CACV,qHAAA,CAAA,aAAU,CAAC,WAAW;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;;wCACZ,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,kBACtB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;4CAC7B,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;wCAGrB,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO,kBACvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,qHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO;4CAC9B,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAGvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,OAAO,EAAE,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,EAAE;4CACzC,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAA8C;;;;;;;;;;;sDAIjF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8C;;;;;;;;;;;sDAIhF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAA8C;;;;;;;;;;;sDAI7E,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;;;;;;sCAQlF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAA8C;;;;;;;;;;;sDAI5E,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAA8C;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8C;;;;;;;;;;;sDAIhF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;;;;;;sCAQtF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAA8C;;;;;;;;;;;sDAI5E,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAA8C;;;;;;;;;;;sDAIpF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8C;;;;;;;;;;;sDAIhF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAE;gCACE,IAAI,OAAO,WAAW;gCAAG;gCAAE,qHAAA,CAAA,aAAU,CAAC,IAAI;gCAAC;;;;;;;sCAEhD,8OAAC;4BAAE,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAO9B", "debugId": null}}, {"offset": {"line": 3126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/layout/bottom-nav.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { useTranslations } from \"next-intl\"\nimport { Home, FileText, Archive, MessageSquare, User, Search, Clock } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { useState, useEffect } from \"react\"\n\nexport function BottomNav() {\n  const pathname = usePathname()\n  const t = useTranslations('navigation')\n  const [isVisible, setIsVisible] = useState(true)\n  const [lastScrollY, setLastScrollY] = useState(0)\n\n  const navigation = [\n    {\n      name: t('home'),\n      href: \"/\",\n      icon: Home,\n    },\n    {\n      name: t('articles'),\n      href: \"/articles\",\n      icon: FileText,\n    },\n    {\n      name: t('archive'),\n      href: \"/archive\",\n      icon: Archive,\n    },\n    {\n      name: t('now'),\n      href: \"/now\",\n      icon: Clock,\n    },\n    {\n      name: t('guestbook'),\n      href: \"/guestbook\",\n      icon: MessageSquare,\n    },\n  ]\n\n  // 滚动隐藏/显示底部导航\n  useEffect(() => {\n    const handleScroll = () => {\n      const currentScrollY = window.scrollY\n\n      if (currentScrollY < lastScrollY || currentScrollY < 100) {\n        setIsVisible(true)\n      } else if (currentScrollY > lastScrollY && currentScrollY > 100) {\n        setIsVisible(false)\n      }\n\n      setLastScrollY(currentScrollY)\n    }\n\n    window.addEventListener('scroll', handleScroll, { passive: true })\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [lastScrollY])\n\n  return (\n    <nav\n      className={cn(\n        \"fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-t md:hidden transition-transform duration-300\",\n        isVisible ? \"translate-y-0\" : \"translate-y-full\"\n      )}\n    >\n      <div className=\"safe-area-inset-bottom\">\n        <div className=\"grid grid-cols-5 h-16\">\n          {navigation.map((item) => {\n            const isActive = pathname === item.href ||\n              (item.href !== '/' && pathname.startsWith(item.href))\n            const Icon = item.icon\n\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  \"flex flex-col items-center justify-center space-y-1 text-xs transition-all duration-200\",\n                  \"active:scale-95 active:bg-accent/50\",\n                  \"focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background\",\n                  isActive\n                    ? \"text-primary\"\n                    : \"text-muted-foreground hover:text-foreground\"\n                )}\n                aria-label={item.name}\n              >\n                <div className={cn(\n                  \"relative p-1 rounded-lg transition-all duration-200\",\n                  isActive && \"bg-primary/10\"\n                )}>\n                  <Icon className={cn(\n                    \"h-5 w-5 transition-all duration-200\",\n                    isActive && \"text-primary scale-110\"\n                  )} />\n                  {isActive && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full\" />\n                  )}\n                </div>\n                <span className={cn(\n                  \"transition-all duration-200 truncate max-w-full\",\n                  isActive && \"text-primary font-medium scale-105\"\n                )}>\n                  {item.name}\n                </span>\n              </Link>\n            )\n          })}\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AARA;;;;;;;;AAUO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,aAAa;QACjB;YACE,MAAM,EAAE;YACR,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,MAAM,EAAE;YACR,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM,EAAE;YAC<PERSON>,MAAM;YACN,MAAM,wMAAA,CAAA,UAAO;QACf;QACA;YACE,MAAM,EAAE;YACR,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;QACb;QACA;YACE,MAAM,EAAE;YACR,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;QACrB;KACD;IAED,cAAc;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,iBAAiB,OAAO,OAAO;YAErC,IAAI,iBAAiB,eAAe,iBAAiB,KAAK;gBACxD,aAAa;YACf,OAAO,IAAI,iBAAiB,eAAe,iBAAiB,KAAK;gBAC/D,aAAa;YACf;YAEA,eAAe;QACjB;QAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAChE,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sKACA,YAAY,kBAAkB;kBAGhC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACpC,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;oBACrD,MAAM,OAAO,KAAK,IAAI;oBAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,uCACA,uGACA,WACI,iBACA;wBAEN,cAAY,KAAK,IAAI;;0CAErB,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uDACA,YAAY;;kDAEZ,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,uCACA,YAAY;;;;;;oCAEb,0BACC,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGnB,8OAAC;gCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,mDACA,YAAY;0CAEX,KAAK,IAAI;;;;;;;uBA5BP,KAAK,IAAI;;;;;gBAgCpB;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 3272, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/page-transition.tsx"], "sourcesContent": ["\"use client\"\n\nimport { ReactNode, useEffect, useState } from \"react\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\n\ninterface PageTransitionProps {\n  children: ReactNode\n  className?: string\n}\n\nexport function PageTransition({ children, className }: PageTransitionProps) {\n  const pathname = usePathname()\n  const [isLoading, setIsLoading] = useState(false)\n\n  useEffect(() => {\n    setIsLoading(true)\n    const timer = setTimeout(() => {\n      setIsLoading(false)\n    }, 150)\n\n    return () => clearTimeout(timer)\n  }, [pathname])\n\n  return (\n    <div className={cn(\"relative\", className)}>\n      {/* 页面过渡遮罩 */}\n      <div\n        className={cn(\n          \"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm transition-all duration-300\",\n          isLoading ? \"opacity-100\" : \"opacity-0 pointer-events-none\"\n        )}\n      >\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n            <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n            <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\"></div>\n          </div>\n        </div>\n      </div>\n\n      {/* 页面内容 */}\n      <div\n        className={cn(\n          \"transition-all duration-300\",\n          isLoading ? \"opacity-50 scale-95\" : \"opacity-100 scale-100\"\n        )}\n      >\n        {children}\n      </div>\n    </div>\n  )\n}\n\n// 简单的淡入动画组件\nexport function FadeIn({ \n  children, \n  delay = 0, \n  duration = 500,\n  className \n}: {\n  children: ReactNode\n  delay?: number\n  duration?: number\n  className?: string\n}) {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(true)\n    }, delay)\n\n    return () => clearTimeout(timer)\n  }, [delay])\n\n  return (\n    <div\n      className={cn(\n        \"transition-all ease-out\",\n        isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-4\",\n        className\n      )}\n      style={{\n        transitionDuration: `${duration}ms`,\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\n// 滑入动画组件\nexport function SlideIn({\n  children,\n  direction = \"up\",\n  delay = 0,\n  duration = 500,\n  className\n}: {\n  children: ReactNode\n  direction?: \"up\" | \"down\" | \"left\" | \"right\"\n  delay?: number\n  duration?: number\n  className?: string\n}) {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(true)\n    }, delay)\n\n    return () => clearTimeout(timer)\n  }, [delay])\n\n  const getTransform = () => {\n    if (isVisible) return \"translate-x-0 translate-y-0\"\n    \n    switch (direction) {\n      case \"up\":\n        return \"translate-y-8\"\n      case \"down\":\n        return \"-translate-y-8\"\n      case \"left\":\n        return \"translate-x-8\"\n      case \"right\":\n        return \"-translate-x-8\"\n      default:\n        return \"translate-y-8\"\n    }\n  }\n\n  return (\n    <div\n      className={cn(\n        \"transition-all ease-out\",\n        isVisible ? \"opacity-100\" : \"opacity-0\",\n        getTransform(),\n        className\n      )}\n      style={{\n        transitionDuration: `${duration}ms`,\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAuB;IACzE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAE7B,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA,YAAY,gBAAgB;0BAG9B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+BACA,YAAY,wBAAwB;0BAGrC;;;;;;;;;;;;AAIT;AAGO,SAAS,OAAO,EACrB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,SAAS,EAMV;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2BACA,YAAY,8BAA8B,2BAC1C;QAEF,OAAO;YACL,oBAAoB,GAAG,SAAS,EAAE,CAAC;QACrC;kBAEC;;;;;;AAGP;AAGO,SAAS,QAAQ,EACtB,QAAQ,EACR,YAAY,IAAI,EAChB,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,SAAS,EAOV;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAM;IAEV,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO;QAEtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2BACA,YAAY,gBAAgB,aAC5B,gBACA;QAEF,OAAO;YACL,oBAAoB,GAAG,SAAS,EAAE,CAAC;QACrC;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 3425, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/accessibility/skip-links.tsx"], "sourcesContent": ["'use client'\n\nimport { useTranslations } from 'next-intl'\nimport { cn } from '@/lib/utils'\n\ninterface SkipLink {\n  href: string\n  label: string\n}\n\ninterface SkipLinksProps {\n  links?: SkipLink[]\n  className?: string\n}\n\nexport function SkipLinks({ links, className }: SkipLinksProps) {\n  const t = useTranslations('accessibility')\n\n  const defaultLinks: SkipLink[] = [\n    { href: '#main-content', label: t('skipToMain') },\n    { href: '#navigation', label: t('skipToNavigation') },\n    { href: '#footer', label: t('skipToFooter') },\n  ]\n\n  const skipLinks = links || defaultLinks\n\n  return (\n    <div className={cn('sr-only focus-within:not-sr-only', className)}>\n      <div className=\"fixed top-0 left-0 z-50 bg-background border border-border p-2 m-2 rounded-md shadow-lg\">\n        <nav aria-label={t('skipLinks')}>\n          <ul className=\"flex flex-col gap-1\">\n            {skipLinks.map((link, index) => (\n              <li key={index}>\n                <a\n                  href={link.href}\n                  className=\"inline-block px-3 py-2 text-sm font-medium text-foreground bg-primary text-primary-foreground rounded hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n                  onClick={(e) => {\n                    e.preventDefault()\n                    const target = document.querySelector(link.href)\n                    if (target) {\n                      target.scrollIntoView({ behavior: 'smooth' })\n                      // 设置焦点到目标元素\n                      if (target instanceof HTMLElement) {\n                        target.focus()\n                      }\n                    }\n                  }}\n                >\n                  {link.label}\n                </a>\n              </li>\n            ))}\n          </ul>\n        </nav>\n      </div>\n    </div>\n  )\n}\n\n/**\n * 焦点管理 Hook\n */\nexport function useFocusManagement() {\n  const focusElement = (selector: string) => {\n    const element = document.querySelector(selector) as HTMLElement\n    if (element) {\n      element.focus()\n      element.scrollIntoView({ behavior: 'smooth', block: 'center' })\n    }\n  }\n\n  const focusFirstInteractive = (container?: HTMLElement) => {\n    const root = container || document\n    const focusableElements = root.querySelectorAll(\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n    )\n    const firstElement = focusableElements[0] as HTMLElement\n    if (firstElement) {\n      firstElement.focus()\n    }\n  }\n\n  const trapFocus = (container: HTMLElement) => {\n    const focusableElements = container.querySelectorAll(\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n    )\n    const firstElement = focusableElements[0] as HTMLElement\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === 'Tab') {\n        if (e.shiftKey) {\n          if (document.activeElement === firstElement) {\n            e.preventDefault()\n            lastElement.focus()\n          }\n        } else {\n          if (document.activeElement === lastElement) {\n            e.preventDefault()\n            firstElement.focus()\n          }\n        }\n      }\n    }\n\n    container.addEventListener('keydown', handleKeyDown)\n    return () => container.removeEventListener('keydown', handleKeyDown)\n  }\n\n  return {\n    focusElement,\n    focusFirstInteractive,\n    trapFocus,\n  }\n}\n\n/**\n * 键盘导航 Hook\n */\nexport function useKeyboardNavigation() {\n  const handleArrowNavigation = (\n    e: KeyboardEvent,\n    items: NodeListOf<HTMLElement> | HTMLElement[],\n    currentIndex: number,\n    onIndexChange: (index: number) => void\n  ) => {\n    switch (e.key) {\n      case 'ArrowDown':\n      case 'ArrowRight':\n        e.preventDefault()\n        const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0\n        onIndexChange(nextIndex)\n        items[nextIndex]?.focus()\n        break\n      case 'ArrowUp':\n      case 'ArrowLeft':\n        e.preventDefault()\n        const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1\n        onIndexChange(prevIndex)\n        items[prevIndex]?.focus()\n        break\n      case 'Home':\n        e.preventDefault()\n        onIndexChange(0)\n        items[0]?.focus()\n        break\n      case 'End':\n        e.preventDefault()\n        const lastIndex = items.length - 1\n        onIndexChange(lastIndex)\n        items[lastIndex]?.focus()\n        break\n    }\n  }\n\n  const handleEscapeKey = (e: KeyboardEvent, onEscape: () => void) => {\n    if (e.key === 'Escape') {\n      e.preventDefault()\n      onEscape()\n    }\n  }\n\n  return {\n    handleArrowNavigation,\n    handleEscapeKey,\n  }\n}\n\n/**\n * 屏幕阅读器公告组件\n */\nexport function ScreenReaderAnnouncement({\n  message,\n  priority = 'polite',\n}: {\n  message: string\n  priority?: 'polite' | 'assertive'\n}) {\n  return (\n    <div\n      aria-live={priority}\n      aria-atomic=\"true\"\n      className=\"sr-only\"\n    >\n      {message}\n    </div>\n  )\n}\n\n/**\n * 可访问的按钮组件\n */\nexport function AccessibleButton({\n  children,\n  onClick,\n  disabled = false,\n  ariaLabel,\n  ariaDescribedBy,\n  className,\n  ...props\n}: {\n  children: React.ReactNode\n  onClick?: () => void\n  disabled?: boolean\n  ariaLabel?: string\n  ariaDescribedBy?: string\n  className?: string\n  [key: string]: any\n}) {\n  return (\n    <button\n      type=\"button\"\n      onClick={onClick}\n      disabled={disabled}\n      aria-label={ariaLabel}\n      aria-describedby={ariaDescribedBy}\n      className={cn(\n        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',\n        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n        'disabled:pointer-events-none disabled:opacity-50',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n}\n\n/**\n * 可访问的链接组件\n */\nexport function AccessibleLink({\n  href,\n  children,\n  external = false,\n  ariaLabel,\n  className,\n  ...props\n}: {\n  href: string\n  children: React.ReactNode\n  external?: boolean\n  ariaLabel?: string\n  className?: string\n  [key: string]: any\n}) {\n  const externalProps = external\n    ? {\n        target: '_blank',\n        rel: 'noopener noreferrer',\n        'aria-label': ariaLabel || `${children} (在新窗口中打开)`,\n      }\n    : {}\n\n  return (\n    <a\n      href={href}\n      className={cn(\n        'text-primary underline-offset-4 hover:underline',\n        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n        className\n      )}\n      {...externalProps}\n      {...props}\n    >\n      {children}\n      {external && (\n        <span className=\"sr-only\"> (在新窗口中打开)</span>\n      )}\n    </a>\n  )\n}\n\n/**\n * 可访问的表单字段组件\n */\nexport function AccessibleFormField({\n  label,\n  children,\n  error,\n  description,\n  required = false,\n  className,\n}: {\n  label: string\n  children: React.ReactNode\n  error?: string\n  description?: string\n  required?: boolean\n  className?: string\n}) {\n  const fieldId = `field-${Math.random().toString(36).substr(2, 9)}`\n  const errorId = error ? `${fieldId}-error` : undefined\n  const descriptionId = description ? `${fieldId}-description` : undefined\n\n  return (\n    <div className={cn('space-y-2', className)}>\n      <label\n        htmlFor={fieldId}\n        className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n      >\n        {label}\n        {required && <span className=\"text-destructive ml-1\" aria-label=\"必填\">*</span>}\n      </label>\n      \n      {description && (\n        <p id={descriptionId} className=\"text-sm text-muted-foreground\">\n          {description}\n        </p>\n      )}\n      \n      <div>\n        {React.cloneElement(children as React.ReactElement, {\n          id: fieldId,\n          'aria-describedby': [descriptionId, errorId].filter(Boolean).join(' ') || undefined,\n          'aria-invalid': error ? 'true' : undefined,\n          'aria-required': required,\n        })}\n      </div>\n      \n      {error && (\n        <p id={errorId} className=\"text-sm text-destructive\" role=\"alert\">\n          {error}\n        </p>\n      )}\n    </div>\n  )\n}\n\n// 导入 React\nimport React from 'react'\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAuUA,WAAW;AACX;AA3UA;;;;AAeO,SAAS,UAAU,EAAE,KAAK,EAAE,SAAS,EAAkB;IAC5D,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,eAA2B;QAC/B;YAAE,MAAM;YAAiB,OAAO,EAAE;QAAc;QAChD;YAAE,MAAM;YAAe,OAAO,EAAE;QAAoB;QACpD;YAAE,MAAM;YAAW,OAAO,EAAE;QAAgB;KAC7C;IAED,MAAM,YAAY,SAAS;IAE3B,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,cAAY,EAAE;0BACjB,cAAA,8OAAC;oBAAG,WAAU;8BACX,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,MAAM,SAAS,SAAS,aAAa,CAAC,KAAK,IAAI;oCAC/C,IAAI,QAAQ;wCACV,OAAO,cAAc,CAAC;4CAAE,UAAU;wCAAS;wCAC3C,YAAY;wCACZ,IAAI,kBAAkB,aAAa;4CACjC,OAAO,KAAK;wCACd;oCACF;gCACF;0CAEC,KAAK,KAAK;;;;;;2BAhBN;;;;;;;;;;;;;;;;;;;;;;;;;AAyBvB;AAKO,SAAS;IACd,MAAM,eAAe,CAAC;QACpB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,KAAK;YACb,QAAQ,cAAc,CAAC;gBAAE,UAAU;gBAAU,OAAO;YAAS;QAC/D;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,OAAO,aAAa;QAC1B,MAAM,oBAAoB,KAAK,gBAAgB,CAC7C;QAEF,MAAM,eAAe,iBAAiB,CAAC,EAAE;QACzC,IAAI,cAAc;YAChB,aAAa,KAAK;QACpB;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,MAAM,oBAAoB,UAAU,gBAAgB,CAClD;QAEF,MAAM,eAAe,iBAAiB,CAAC,EAAE;QACzC,MAAM,cAAc,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;QAEnE,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,OAAO;gBACnB,IAAI,EAAE,QAAQ,EAAE;oBACd,IAAI,SAAS,aAAa,KAAK,cAAc;wBAC3C,EAAE,cAAc;wBAChB,YAAY,KAAK;oBACnB;gBACF,OAAO;oBACL,IAAI,SAAS,aAAa,KAAK,aAAa;wBAC1C,EAAE,cAAc;wBAChB,aAAa,KAAK;oBACpB;gBACF;YACF;QACF;QAEA,UAAU,gBAAgB,CAAC,WAAW;QACtC,OAAO,IAAM,UAAU,mBAAmB,CAAC,WAAW;IACxD;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,wBAAwB,CAC5B,GACA,OACA,cACA;QAEA,OAAQ,EAAE,GAAG;YACX,KAAK;YACL,KAAK;gBACH,EAAE,cAAc;gBAChB,MAAM,YAAY,eAAe,MAAM,MAAM,GAAG,IAAI,eAAe,IAAI;gBACvE,cAAc;gBACd,KAAK,CAAC,UAAU,EAAE;gBAClB;YACF,KAAK;YACL,KAAK;gBACH,EAAE,cAAc;gBAChB,MAAM,YAAY,eAAe,IAAI,eAAe,IAAI,MAAM,MAAM,GAAG;gBACvE,cAAc;gBACd,KAAK,CAAC,UAAU,EAAE;gBAClB;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,cAAc;gBACd,KAAK,CAAC,EAAE,EAAE;gBACV;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,MAAM,YAAY,MAAM,MAAM,GAAG;gBACjC,cAAc;gBACd,KAAK,CAAC,UAAU,EAAE;gBAClB;QACJ;IACF;IAEA,MAAM,kBAAkB,CAAC,GAAkB;QACzC,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,OAAO;QACL;QACA;IACF;AACF;AAKO,SAAS,yBAAyB,EACvC,OAAO,EACP,WAAW,QAAQ,EAIpB;IACC,qBACE,8OAAC;QACC,aAAW;QACX,eAAY;QACZ,WAAU;kBAET;;;;;;AAGP;AAKO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,OAAO,EACP,WAAW,KAAK,EAChB,SAAS,EACT,eAAe,EACf,SAAS,EACT,GAAG,OASJ;IACC,qBACE,8OAAC;QACC,MAAK;QACL,SAAS;QACT,UAAU;QACV,cAAY;QACZ,oBAAkB;QAClB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA,uGACA,oDACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAKO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,WAAW,KAAK,EAChB,SAAS,EACT,SAAS,EACT,GAAG,OAQJ;IACC,MAAM,gBAAgB,WAClB;QACE,QAAQ;QACR,KAAK;QACL,cAAc,aAAa,GAAG,SAAS,UAAU,CAAC;IACpD,IACA,CAAC;IAEL,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mDACA,uGACA;QAED,GAAG,aAAa;QAChB,GAAG,KAAK;;YAER;YACA,0BACC,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAIlC;AAKO,SAAS,oBAAoB,EAClC,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EACX,WAAW,KAAK,EAChB,SAAS,EAQV;IACC,MAAM,UAAU,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAClE,MAAM,UAAU,QAAQ,GAAG,QAAQ,MAAM,CAAC,GAAG;IAC7C,MAAM,gBAAgB,cAAc,GAAG,QAAQ,YAAY,CAAC,GAAG;IAE/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;wBAAwB,cAAW;kCAAK;;;;;;;;;;;;YAGtE,6BACC,8OAAC;gBAAE,IAAI;gBAAe,WAAU;0BAC7B;;;;;;0BAIL,8OAAC;0BACE,cAAA,qMAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAgC;oBAClD,IAAI;oBACJ,oBAAoB;wBAAC;wBAAe;qBAAQ,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;oBAC1E,gBAAgB,QAAQ,SAAS;oBACjC,iBAAiB;gBACnB;;;;;;YAGD,uBACC,8OAAC;gBAAE,IAAI;gBAAS,WAAU;gBAA2B,MAAK;0BACvD;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 3736, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/accessibility/keyboard-shortcuts.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useTranslations } from 'next-intl'\nimport { useRouter } from 'next/navigation'\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Keyboard, X } from 'lucide-react'\n\ninterface KeyboardShortcut {\n  key: string\n  description: string\n  action: () => void\n  category?: string\n  disabled?: boolean\n}\n\ninterface KeyboardShortcutsProps {\n  shortcuts?: KeyboardShortcut[]\n  disabled?: boolean\n}\n\nexport function KeyboardShortcuts({ shortcuts = [], disabled = false }: KeyboardShortcutsProps) {\n  const t = useTranslations('accessibility.shortcuts')\n  const router = useRouter()\n  const [showHelp, setShowHelp] = useState(false)\n\n  // 默认快捷键\n  const defaultShortcuts: KeyboardShortcut[] = [\n    {\n      key: '/',\n      description: t('search'),\n      action: () => {\n        const searchInput = document.querySelector('input[type=\"search\"]') as HTMLInputElement\n        if (searchInput) {\n          searchInput.focus()\n        }\n      },\n      category: t('categories.navigation'),\n    },\n    {\n      key: 'h',\n      description: t('home'),\n      action: () => router.push('/'),\n      category: t('categories.navigation'),\n    },\n    {\n      key: 'a',\n      description: t('articles'),\n      action: () => router.push('/articles'),\n      category: t('categories.navigation'),\n    },\n    {\n      key: 'd',\n      description: t('dashboard'),\n      action: () => router.push('/dashboard'),\n      category: t('categories.navigation'),\n    },\n    {\n      key: '?',\n      description: t('help'),\n      action: () => setShowHelp(true),\n      category: t('categories.help'),\n    },\n    {\n      key: 'Escape',\n      description: t('close'),\n      action: () => {\n        // 关闭模态框或返回上一页\n        const modal = document.querySelector('[role=\"dialog\"]')\n        if (modal) {\n          const closeButton = modal.querySelector('button[aria-label*=\"关闭\"], button[aria-label*=\"close\"]') as HTMLButtonElement\n          if (closeButton) {\n            closeButton.click()\n          }\n        }\n      },\n      category: t('categories.general'),\n    },\n  ]\n\n  const allShortcuts = [...defaultShortcuts, ...shortcuts]\n\n  useEffect(() => {\n    if (disabled) return\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      // 忽略在输入框中的按键\n      const target = e.target as HTMLElement\n      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {\n        // 只处理 Escape 键\n        if (e.key === 'Escape') {\n          const shortcut = allShortcuts.find(s => s.key === 'Escape' && !s.disabled)\n          if (shortcut) {\n            e.preventDefault()\n            shortcut.action()\n          }\n        }\n        return\n      }\n\n      // 查找匹配的快捷键\n      const shortcut = allShortcuts.find(s => {\n        if (s.disabled) return false\n        \n        // 处理修饰键组合\n        if (s.key.includes('+')) {\n          const keys = s.key.split('+')\n          const mainKey = keys[keys.length - 1].toLowerCase()\n          const hasCtrl = keys.includes('Ctrl') && e.ctrlKey\n          const hasAlt = keys.includes('Alt') && e.altKey\n          const hasShift = keys.includes('Shift') && e.shiftKey\n          const hasMeta = keys.includes('Meta') && e.metaKey\n          \n          return e.key.toLowerCase() === mainKey && \n                 (!keys.includes('Ctrl') || hasCtrl) &&\n                 (!keys.includes('Alt') || hasAlt) &&\n                 (!keys.includes('Shift') || hasShift) &&\n                 (!keys.includes('Meta') || hasMeta)\n        }\n        \n        return e.key === s.key\n      })\n\n      if (shortcut) {\n        e.preventDefault()\n        shortcut.action()\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [allShortcuts, disabled])\n\n  // 按类别分组快捷键\n  const groupedShortcuts = allShortcuts.reduce((groups, shortcut) => {\n    const category = shortcut.category || t('categories.general')\n    if (!groups[category]) {\n      groups[category] = []\n    }\n    groups[category].push(shortcut)\n    return groups\n  }, {} as Record<string, KeyboardShortcut[]>)\n\n  return (\n    <>\n      {/* 快捷键帮助按钮 */}\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        onClick={() => setShowHelp(true)}\n        className=\"fixed bottom-4 right-4 z-40 bg-background/80 backdrop-blur-sm border shadow-lg\"\n        aria-label={t('showHelp')}\n      >\n        <Keyboard className=\"h-4 w-4\" />\n      </Button>\n\n      {/* 快捷键帮助对话框 */}\n      <Dialog open={showHelp} onOpenChange={setShowHelp}>\n        <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Keyboard className=\"h-5 w-5\" />\n              {t('title')}\n            </DialogTitle>\n            <DialogDescription>\n              {t('description')}\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-6\">\n            {Object.entries(groupedShortcuts).map(([category, shortcuts]) => (\n              <div key={category}>\n                <h3 className=\"text-lg font-semibold mb-3\">{category}</h3>\n                <div className=\"space-y-2\">\n                  {shortcuts.map((shortcut, index) => (\n                    <div\n                      key={index}\n                      className=\"flex items-center justify-between p-2 rounded-lg bg-muted/50\"\n                    >\n                      <span className=\"text-sm\">{shortcut.description}</span>\n                      <KeyBadge keys={shortcut.key} />\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"flex justify-end pt-4 border-t\">\n            <Button onClick={() => setShowHelp(false)}>\n              {t('close')}\n            </Button>\n          </div>\n        </DialogContent>\n      </Dialog>\n    </>\n  )\n}\n\n/**\n * 按键徽章组件\n */\nfunction KeyBadge({ keys }: { keys: string }) {\n  const keyArray = keys.split('+').map(key => key.trim())\n\n  return (\n    <div className=\"flex items-center gap-1\">\n      {keyArray.map((key, index) => (\n        <div key={index} className=\"flex items-center gap-1\">\n          {index > 0 && <span className=\"text-muted-foreground\">+</span>}\n          <Badge variant=\"outline\" className=\"font-mono text-xs px-2 py-1\">\n            {formatKey(key)}\n          </Badge>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n/**\n * 格式化按键显示\n */\nfunction formatKey(key: string): string {\n  const keyMap: Record<string, string> = {\n    'Ctrl': '⌃',\n    'Alt': '⌥',\n    'Shift': '⇧',\n    'Meta': '⌘',\n    'Cmd': '⌘',\n    'Enter': '↵',\n    'Escape': 'Esc',\n    'ArrowUp': '↑',\n    'ArrowDown': '↓',\n    'ArrowLeft': '←',\n    'ArrowRight': '→',\n    'Backspace': '⌫',\n    'Delete': '⌦',\n    'Tab': '⇥',\n    'Space': '␣',\n  }\n\n  return keyMap[key] || key.toUpperCase()\n}\n\n/**\n * 自定义快捷键 Hook\n */\nexport function useKeyboardShortcut(\n  key: string,\n  callback: () => void,\n  options: {\n    disabled?: boolean\n    preventDefault?: boolean\n    stopPropagation?: boolean\n  } = {}\n) {\n  const { disabled = false, preventDefault = true, stopPropagation = false } = options\n\n  useEffect(() => {\n    if (disabled) return\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      // 忽略在输入框中的按键（除非是 Escape）\n      const target = e.target as HTMLElement\n      if (\n        key !== 'Escape' &&\n        (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true')\n      ) {\n        return\n      }\n\n      // 检查按键匹配\n      let matches = false\n      if (key.includes('+')) {\n        const keys = key.split('+')\n        const mainKey = keys[keys.length - 1].toLowerCase()\n        const hasCtrl = keys.includes('Ctrl') && e.ctrlKey\n        const hasAlt = keys.includes('Alt') && e.altKey\n        const hasShift = keys.includes('Shift') && e.shiftKey\n        const hasMeta = keys.includes('Meta') && e.metaKey\n        \n        matches = e.key.toLowerCase() === mainKey && \n                 (!keys.includes('Ctrl') || hasCtrl) &&\n                 (!keys.includes('Alt') || hasAlt) &&\n                 (!keys.includes('Shift') || hasShift) &&\n                 (!keys.includes('Meta') || hasMeta)\n      } else {\n        matches = e.key === key\n      }\n\n      if (matches) {\n        if (preventDefault) e.preventDefault()\n        if (stopPropagation) e.stopPropagation()\n        callback()\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [key, callback, disabled, preventDefault, stopPropagation])\n}\n\n/**\n * 焦点陷阱 Hook\n */\nexport function useFocusTrap(isActive: boolean) {\n  useEffect(() => {\n    if (!isActive) return\n\n    const focusableElements = document.querySelectorAll(\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n    )\n    const firstElement = focusableElements[0] as HTMLElement\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === 'Tab') {\n        if (e.shiftKey) {\n          if (document.activeElement === firstElement) {\n            e.preventDefault()\n            lastElement.focus()\n          }\n        } else {\n          if (document.activeElement === lastElement) {\n            e.preventDefault()\n            firstElement.focus()\n          }\n        }\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [isActive])\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAuBO,SAAS,kBAAkB,EAAE,YAAY,EAAE,EAAE,WAAW,KAAK,EAA0B;IAC5F,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,QAAQ;IACR,MAAM,mBAAuC;QAC3C;YACE,KAAK;YACL,aAAa,EAAE;YACf,QAAQ;gBACN,MAAM,cAAc,SAAS,aAAa,CAAC;gBAC3C,IAAI,aAAa;oBACf,YAAY,KAAK;gBACnB;YACF;YACA,UAAU,EAAE;QACd;QACA;YACE,KAAK;YACL,aAAa,EAAE;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU,EAAE;QACd;QACA;YACE,KAAK;YACL,aAAa,EAAE;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU,EAAE;QACd;QACA;YACE,KAAK;YACL,aAAa,EAAE;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU,EAAE;QACd;QACA;YACE,KAAK;YACL,aAAa,EAAE;YACf,QAAQ,IAAM,YAAY;YAC1B,UAAU,EAAE;QACd;QACA;YACE,KAAK;YACL,aAAa,EAAE;YACf,QAAQ;gBACN,cAAc;gBACd,MAAM,QAAQ,SAAS,aAAa,CAAC;gBACrC,IAAI,OAAO;oBACT,MAAM,cAAc,MAAM,aAAa,CAAC;oBACxC,IAAI,aAAa;wBACf,YAAY,KAAK;oBACnB;gBACF;YACF;YACA,UAAU,EAAE;QACd;KACD;IAED,MAAM,eAAe;WAAI;WAAqB;KAAU;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;QAEd,MAAM,gBAAgB,CAAC;YACrB,aAAa;YACb,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,cAAc,OAAO,eAAe,KAAK,QAAQ;gBACpG,eAAe;gBACf,IAAI,EAAE,GAAG,KAAK,UAAU;oBACtB,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,YAAY,CAAC,EAAE,QAAQ;oBACzE,IAAI,UAAU;wBACZ,EAAE,cAAc;wBAChB,SAAS,MAAM;oBACjB;gBACF;gBACA;YACF;YAEA,WAAW;YACX,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA;gBACjC,IAAI,EAAE,QAAQ,EAAE,OAAO;gBAEvB,UAAU;gBACV,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,MAAM;oBACvB,MAAM,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC;oBACzB,MAAM,UAAU,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,WAAW;oBACjD,MAAM,UAAU,KAAK,QAAQ,CAAC,WAAW,EAAE,OAAO;oBAClD,MAAM,SAAS,KAAK,QAAQ,CAAC,UAAU,EAAE,MAAM;oBAC/C,MAAM,WAAW,KAAK,QAAQ,CAAC,YAAY,EAAE,QAAQ;oBACrD,MAAM,UAAU,KAAK,QAAQ,CAAC,WAAW,EAAE,OAAO;oBAElD,OAAO,EAAE,GAAG,CAAC,WAAW,OAAO,WACxB,CAAC,CAAC,KAAK,QAAQ,CAAC,WAAW,OAAO,KAClC,CAAC,CAAC,KAAK,QAAQ,CAAC,UAAU,MAAM,KAChC,CAAC,CAAC,KAAK,QAAQ,CAAC,YAAY,QAAQ,KACpC,CAAC,CAAC,KAAK,QAAQ,CAAC,WAAW,OAAO;gBAC3C;gBAEA,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG;YACxB;YAEA,IAAI,UAAU;gBACZ,EAAE,cAAc;gBAChB,SAAS,MAAM;YACjB;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAc;KAAS;IAE3B,WAAW;IACX,MAAM,mBAAmB,aAAa,MAAM,CAAC,CAAC,QAAQ;QACpD,MAAM,WAAW,SAAS,QAAQ,IAAI,EAAE;QACxC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACrB,MAAM,CAAC,SAAS,GAAG,EAAE;QACvB;QACA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE;;0BAEE,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,YAAY;gBAC3B,WAAU;gBACV,cAAY,EAAE;0BAEd,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAItB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAU,cAAc;0BACpC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,EAAE;;;;;;;8CAEL,8OAAC,kIAAA,CAAA,oBAAiB;8CACf,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,UAAU,UAAU,iBAC1D,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAW,SAAS,WAAW;;;;;;sEAC/C,8OAAC;4DAAS,MAAM,SAAS,GAAG;;;;;;;mDAJvB;;;;;;;;;;;mCALH;;;;;;;;;;sCAiBd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,YAAY;0CAChC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;AAEA;;CAEC,GACD,SAAS,SAAS,EAAE,IAAI,EAAoB;IAC1C,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;IAEpD,qBACE,8OAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,8OAAC;gBAAgB,WAAU;;oBACxB,QAAQ,mBAAK,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACtD,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;kCAChC,UAAU;;;;;;;eAHL;;;;;;;;;;AASlB;AAEA;;CAEC,GACD,SAAS,UAAU,GAAW;IAC5B,MAAM,SAAiC;QACrC,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,SAAS;IACX;IAEA,OAAO,MAAM,CAAC,IAAI,IAAI,IAAI,WAAW;AACvC;AAKO,SAAS,oBACd,GAAW,EACX,QAAoB,EACpB,UAII,CAAC,CAAC;IAEN,MAAM,EAAE,WAAW,KAAK,EAAE,iBAAiB,IAAI,EAAE,kBAAkB,KAAK,EAAE,GAAG;IAE7E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;QAEd,MAAM,gBAAgB,CAAC;YACrB,yBAAyB;YACzB,MAAM,SAAS,EAAE,MAAM;YACvB,IACE,QAAQ,YACR,CAAC,OAAO,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,cAAc,OAAO,eAAe,KAAK,MAAM,GACjG;gBACA;YACF;YAEA,SAAS;YACT,IAAI,UAAU;YACd,IAAI,IAAI,QAAQ,CAAC,MAAM;gBACrB,MAAM,OAAO,IAAI,KAAK,CAAC;gBACvB,MAAM,UAAU,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,WAAW;gBACjD,MAAM,UAAU,KAAK,QAAQ,CAAC,WAAW,EAAE,OAAO;gBAClD,MAAM,SAAS,KAAK,QAAQ,CAAC,UAAU,EAAE,MAAM;gBAC/C,MAAM,WAAW,KAAK,QAAQ,CAAC,YAAY,EAAE,QAAQ;gBACrD,MAAM,UAAU,KAAK,QAAQ,CAAC,WAAW,EAAE,OAAO;gBAElD,UAAU,EAAE,GAAG,CAAC,WAAW,OAAO,WACzB,CAAC,CAAC,KAAK,QAAQ,CAAC,WAAW,OAAO,KAClC,CAAC,CAAC,KAAK,QAAQ,CAAC,UAAU,MAAM,KAChC,CAAC,CAAC,KAAK,QAAQ,CAAC,YAAY,QAAQ,KACpC,CAAC,CAAC,KAAK,QAAQ,CAAC,WAAW,OAAO;YAC7C,OAAO;gBACL,UAAU,EAAE,GAAG,KAAK;YACtB;YAEA,IAAI,SAAS;gBACX,IAAI,gBAAgB,EAAE,cAAc;gBACpC,IAAI,iBAAiB,EAAE,eAAe;gBACtC;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAK;QAAU;QAAU;QAAgB;KAAgB;AAC/D;AAKO,SAAS,aAAa,QAAiB;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,MAAM,oBAAoB,SAAS,gBAAgB,CACjD;QAEF,MAAM,eAAe,iBAAiB,CAAC,EAAE;QACzC,MAAM,cAAc,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;QAEnE,MAAM,gBAAgB,CAAC;YACrB,IAAI,EAAE,GAAG,KAAK,OAAO;gBACnB,IAAI,EAAE,QAAQ,EAAE;oBACd,IAAI,SAAS,aAAa,KAAK,cAAc;wBAC3C,EAAE,cAAc;wBAChB,YAAY,KAAK;oBACnB;gBACF,OAAO;oBACL,IAAI,SAAS,aAAa,KAAK,aAAa;wBAC1C,EAAE,cAAc;wBAChB,aAAa,KAAK;oBACpB;gBACF;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;KAAS;AACf", "debugId": null}}, {"offset": {"line": 4143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/toast.tsx"], "sourcesContent": ["'use client'\n\nimport { Toaster as Sonner } from 'sonner'\nimport { useTheme } from 'next-themes'\nimport { useTranslations } from 'next-intl'\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = 'system' } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps['theme']}\n      className=\"toaster group\"\n      toastOptions={{\n        classNames: {\n          toast:\n            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',\n          description: 'group-[.toast]:text-muted-foreground',\n          actionButton:\n            'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',\n          cancelButton:\n            'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',\n        },\n      }}\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n\n/**\n * Toast 工具函数\n */\nimport { toast } from 'sonner'\n\nexport const showToast = {\n  success: (message: string, options?: any) => {\n    toast.success(message, {\n      duration: 4000,\n      ...options,\n    })\n  },\n  \n  error: (message: string, options?: any) => {\n    toast.error(message, {\n      duration: 6000,\n      ...options,\n    })\n  },\n  \n  warning: (message: string, options?: any) => {\n    toast.warning(message, {\n      duration: 5000,\n      ...options,\n    })\n  },\n  \n  info: (message: string, options?: any) => {\n    toast.info(message, {\n      duration: 4000,\n      ...options,\n    })\n  },\n  \n  loading: (message: string, options?: any) => {\n    return toast.loading(message, {\n      duration: Infinity,\n      ...options,\n    })\n  },\n  \n  promise: <T>(\n    promise: Promise<T>,\n    messages: {\n      loading: string\n      success: string | ((data: T) => string)\n      error: string | ((error: any) => string)\n    },\n    options?: any\n  ) => {\n    return toast.promise(promise, messages, {\n      duration: 4000,\n      ...options,\n    })\n  },\n  \n  dismiss: (toastId?: string | number) => {\n    toast.dismiss(toastId)\n  },\n  \n  custom: (jsx: React.ReactNode, options?: any) => {\n    return toast.custom(jsx, options)\n  },\n}\n\n/**\n * 多语言 Toast Hook\n */\nexport function useToast() {\n  const t = useTranslations('toast')\n\n  return {\n    success: (key: string, values?: any) => showToast.success(t(key, values)),\n    error: (key: string, values?: any) => showToast.error(t(key, values)),\n    warning: (key: string, values?: any) => showToast.warning(t(key, values)),\n    info: (key: string, values?: any) => showToast.info(t(key, values)),\n    loading: (key: string, values?: any) => showToast.loading(t(key, values)),\n    promise: <T>(\n      promise: Promise<T>,\n      messages: {\n        loading: string\n        success: string | ((data: T) => string)\n        error: string | ((error: any) => string)\n      }\n    ) => showToast.promise(promise, {\n      loading: t(messages.loading),\n      success: typeof messages.success === 'string' ? t(messages.success) : messages.success,\n      error: typeof messages.error === 'string' ? t(messages.error) : messages.error,\n    }),\n    dismiss: showToast.dismiss,\n    custom: showToast.custom,\n  }\n}\n\n/**\n * API 请求 Toast Hook\n */\nexport function useApiToast() {\n  const t = useTranslations('api')\n\n  const handleApiCall = async <T>(\n    apiCall: () => Promise<T>,\n    options: {\n      loadingMessage?: string\n      successMessage?: string | ((data: T) => string)\n      errorMessage?: string | ((error: any) => string)\n      showLoading?: boolean\n      showSuccess?: boolean\n      showError?: boolean\n    } = {}\n  ): Promise<T> => {\n    const {\n      loadingMessage = t('loading'),\n      successMessage = t('success'),\n      errorMessage = t('error'),\n      showLoading = true,\n      showSuccess = true,\n      showError = true,\n    } = options\n\n    let loadingToast: string | number | undefined\n\n    try {\n      if (showLoading) {\n        loadingToast = showToast.loading(loadingMessage)\n      }\n\n      const result = await apiCall()\n\n      if (loadingToast) {\n        showToast.dismiss(loadingToast)\n      }\n\n      if (showSuccess) {\n        const message = typeof successMessage === 'function' \n          ? successMessage(result) \n          : successMessage\n        showToast.success(message)\n      }\n\n      return result\n    } catch (error) {\n      if (loadingToast) {\n        showToast.dismiss(loadingToast)\n      }\n\n      if (showError) {\n        const message = typeof errorMessage === 'function' \n          ? errorMessage(error) \n          : errorMessage\n        showToast.error(message)\n      }\n\n      throw error\n    }\n  }\n\n  return { handleApiCall }\n}\n\n/**\n * 表单提交 Toast Hook\n */\nexport function useFormToast() {\n  const t = useTranslations('form')\n\n  const handleFormSubmit = async <T>(\n    submitFn: () => Promise<T>,\n    options: {\n      successMessage?: string\n      errorMessage?: string\n      onSuccess?: (data: T) => void\n      onError?: (error: any) => void\n    } = {}\n  ) => {\n    const {\n      successMessage = t('submitSuccess'),\n      errorMessage = t('submitError'),\n      onSuccess,\n      onError,\n    } = options\n\n    try {\n      const result = await submitFn()\n      showToast.success(successMessage)\n      onSuccess?.(result)\n      return result\n    } catch (error) {\n      const message = error instanceof Error ? error.message : errorMessage\n      showToast.error(message)\n      onError?.(error)\n      throw error\n    }\n  }\n\n  return { handleFormSubmit }\n}\n\n/**\n * 文件上传 Toast Hook\n */\nexport function useFileUploadToast() {\n  const t = useTranslations('fileUpload')\n\n  const handleFileUpload = async (\n    uploadFn: (onProgress?: (progress: number) => void) => Promise<any>,\n    options: {\n      successMessage?: string\n      errorMessage?: string\n      showProgress?: boolean\n    } = {}\n  ) => {\n    const {\n      successMessage = t('uploadSuccess'),\n      errorMessage = t('uploadError'),\n      showProgress = true,\n    } = options\n\n    let progressToast: string | number | undefined\n\n    try {\n      if (showProgress) {\n        progressToast = showToast.loading(t('uploading'))\n      }\n\n      const result = await uploadFn((progress) => {\n        if (progressToast && showProgress) {\n          showToast.dismiss(progressToast)\n          progressToast = showToast.loading(`${t('uploading')} ${Math.round(progress)}%`)\n        }\n      })\n\n      if (progressToast) {\n        showToast.dismiss(progressToast)\n      }\n\n      showToast.success(successMessage)\n      return result\n    } catch (error) {\n      if (progressToast) {\n        showToast.dismiss(progressToast)\n      }\n\n      const message = error instanceof Error ? error.message : errorMessage\n      showToast.error(message)\n      throw error\n    }\n  }\n\n  return { handleFileUpload }\n}\n\n/**\n * 复制到剪贴板 Toast\n */\nexport function useCopyToast() {\n  const t = useTranslations('copy')\n\n  const copyToClipboard = async (text: string, successMessage?: string) => {\n    try {\n      await navigator.clipboard.writeText(text)\n      showToast.success(successMessage || t('success'))\n    } catch (error) {\n      showToast.error(t('error'))\n      throw error\n    }\n  }\n\n  return { copyToClipboard }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA;AACA;AAJA;;;;;AAQA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC;QACC,OAAO;QACP,WAAU;QACV,cAAc;YACZ,YAAY;gBACV,OACE;gBACF,aAAa;gBACb,cACE;gBACF,cACE;YACJ;QACF;QACC,GAAG,KAAK;;;;;;AAGf;;;AASO,MAAM,YAAY;IACvB,SAAS,CAAC,SAAiB;QACzB,MAAM,OAAO,CAAC,SAAS;YACrB,UAAU;YACV,GAAG,OAAO;QACZ;IACF;IAEA,OAAO,CAAC,SAAiB;QACvB,MAAM,KAAK,CAAC,SAAS;YACnB,UAAU;YACV,GAAG,OAAO;QACZ;IACF;IAEA,SAAS,CAAC,SAAiB;QACzB,MAAM,OAAO,CAAC,SAAS;YACrB,UAAU;YACV,GAAG,OAAO;QACZ;IACF;IAEA,MAAM,CAAC,SAAiB;QACtB,MAAM,IAAI,CAAC,SAAS;YAClB,UAAU;YACV,GAAG,OAAO;QACZ;IACF;IAEA,SAAS,CAAC,SAAiB;QACzB,OAAO,MAAM,OAAO,CAAC,SAAS;YAC5B,UAAU;YACV,GAAG,OAAO;QACZ;IACF;IAEA,SAAS,CACP,SACA,UAKA;QAEA,OAAO,MAAM,OAAO,CAAC,SAAS,UAAU;YACtC,UAAU;YACV,GAAG,OAAO;QACZ;IACF;IAEA,SAAS,CAAC;QACR,MAAM,OAAO,CAAC;IAChB;IAEA,QAAQ,CAAC,KAAsB;QAC7B,OAAO,MAAM,MAAM,CAAC,KAAK;IAC3B;AACF;AAKO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO;QACL,SAAS,CAAC,KAAa,SAAiB,UAAU,OAAO,CAAC,EAAE,KAAK;QACjE,OAAO,CAAC,KAAa,SAAiB,UAAU,KAAK,CAAC,EAAE,KAAK;QAC7D,SAAS,CAAC,KAAa,SAAiB,UAAU,OAAO,CAAC,EAAE,KAAK;QACjE,MAAM,CAAC,KAAa,SAAiB,UAAU,IAAI,CAAC,EAAE,KAAK;QAC3D,SAAS,CAAC,KAAa,SAAiB,UAAU,OAAO,CAAC,EAAE,KAAK;QACjE,SAAS,CACP,SACA,WAKG,UAAU,OAAO,CAAC,SAAS;gBAC9B,SAAS,EAAE,SAAS,OAAO;gBAC3B,SAAS,OAAO,SAAS,OAAO,KAAK,WAAW,EAAE,SAAS,OAAO,IAAI,SAAS,OAAO;gBACtF,OAAO,OAAO,SAAS,KAAK,KAAK,WAAW,EAAE,SAAS,KAAK,IAAI,SAAS,KAAK;YAChF;QACA,SAAS,UAAU,OAAO;QAC1B,QAAQ,UAAU,MAAM;IAC1B;AACF;AAKO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,gBAAgB,OACpB,SACA,UAOI,CAAC,CAAC;QAEN,MAAM,EACJ,iBAAiB,EAAE,UAAU,EAC7B,iBAAiB,EAAE,UAAU,EAC7B,eAAe,EAAE,QAAQ,EACzB,cAAc,IAAI,EAClB,cAAc,IAAI,EAClB,YAAY,IAAI,EACjB,GAAG;QAEJ,IAAI;QAEJ,IAAI;YACF,IAAI,aAAa;gBACf,eAAe,UAAU,OAAO,CAAC;YACnC;YAEA,MAAM,SAAS,MAAM;YAErB,IAAI,cAAc;gBAChB,UAAU,OAAO,CAAC;YACpB;YAEA,IAAI,aAAa;gBACf,MAAM,UAAU,OAAO,mBAAmB,aACtC,eAAe,UACf;gBACJ,UAAU,OAAO,CAAC;YACpB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,cAAc;gBAChB,UAAU,OAAO,CAAC;YACpB;YAEA,IAAI,WAAW;gBACb,MAAM,UAAU,OAAO,iBAAiB,aACpC,aAAa,SACb;gBACJ,UAAU,KAAK,CAAC;YAClB;YAEA,MAAM;QACR;IACF;IAEA,OAAO;QAAE;IAAc;AACzB;AAKO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,mBAAmB,OACvB,UACA,UAKI,CAAC,CAAC;QAEN,MAAM,EACJ,iBAAiB,EAAE,gBAAgB,EACnC,eAAe,EAAE,cAAc,EAC/B,SAAS,EACT,OAAO,EACR,GAAG;QAEJ,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,UAAU,OAAO,CAAC;YAClB,YAAY;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD,UAAU,KAAK,CAAC;YAChB,UAAU;YACV,MAAM;QACR;IACF;IAEA,OAAO;QAAE;IAAiB;AAC5B;AAKO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,mBAAmB,OACvB,UACA,UAII,CAAC,CAAC;QAEN,MAAM,EACJ,iBAAiB,EAAE,gBAAgB,EACnC,eAAe,EAAE,cAAc,EAC/B,eAAe,IAAI,EACpB,GAAG;QAEJ,IAAI;QAEJ,IAAI;YACF,IAAI,cAAc;gBAChB,gBAAgB,UAAU,OAAO,CAAC,EAAE;YACtC;YAEA,MAAM,SAAS,MAAM,SAAS,CAAC;gBAC7B,IAAI,iBAAiB,cAAc;oBACjC,UAAU,OAAO,CAAC;oBAClB,gBAAgB,UAAU,OAAO,CAAC,GAAG,EAAE,aAAa,CAAC,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC;gBAChF;YACF;YAEA,IAAI,eAAe;gBACjB,UAAU,OAAO,CAAC;YACpB;YAEA,UAAU,OAAO,CAAC;YAClB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,eAAe;gBACjB,UAAU,OAAO,CAAC;YACpB;YAEA,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD,UAAU,KAAK,CAAC;YAChB,MAAM;QACR;IACF;IAEA,OAAO;QAAE;IAAiB;AAC5B;AAKO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU,OAAO,CAAC,kBAAkB,EAAE;QACxC,EAAE,OAAO,OAAO;YACd,UAAU,KAAK,CAAC,EAAE;YAClB,MAAM;QACR;IACF;IAEA,OAAO;QAAE;IAAgB;AAC3B", "debugId": null}}, {"offset": {"line": 4364, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/layout/main-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>actNode } from \"react\"\nimport { Head<PERSON> } from \"./header\"\nimport { Footer } from \"./footer\"\nimport { BottomNav } from \"./bottom-nav\"\nimport { PageTransition } from \"@/components/ui/page-transition\"\nimport { SkipLinks } from \"@/components/accessibility/skip-links\"\nimport { KeyboardShortcuts } from \"@/components/accessibility/keyboard-shortcuts\"\nimport { Toaster } from \"@/components/ui/toast\"\nimport { PageErrorBoundary } from \"@/components/error/error-boundary\"\n\ninterface MainLayoutProps {\n  children: ReactNode\n  showBottomNav?: boolean\n  enableTransition?: boolean\n}\n\nexport function MainLayout({\n  children,\n  showBottomNav = true,\n  enableTransition = true\n}: MainLayoutProps) {\n  const content = (\n    <PageErrorBoundary>\n      <div className=\"min-h-screen flex flex-col\">\n        <SkipLinks />\n        <Header />\n        <main id=\"main-content\" className=\"flex-1 container mx-auto px-4 py-8 pb-20 md:pb-8\" tabIndex={-1}>\n          {children}\n        </main>\n        <Footer />\n        {showBottomNav && <BottomNav />}\n        <KeyboardShortcuts />\n        <Toaster />\n      </div>\n    </PageErrorBoundary>\n  )\n\n  if (enableTransition) {\n    return <PageTransition>{content}</PageTransition>\n  }\n\n  return content\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;AAkBO,SAAS,WAAW,EACzB,QAAQ,EACR,gBAAgB,IAAI,EACpB,mBAAmB,IAAI,EACP;IAChB,MAAM,wBACJ,8OAAC,gJAAA,CAAA,oBAAiB;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oJAAA,CAAA,YAAS;;;;;8BACV,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAK,IAAG;oBAAe,WAAU;oBAAmD,UAAU,CAAC;8BAC7F;;;;;;8BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;gBACN,+BAAiB,8OAAC,6IAAA,CAAA,YAAS;;;;;8BAC5B,8OAAC,4JAAA,CAAA,oBAAiB;;;;;8BAClB,8OAAC,iIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;IAKd,IAAI,kBAAkB;QACpB,qBAAO,8OAAC,8IAAA,CAAA,iBAAc;sBAAE;;;;;;IAC1B;IAEA,OAAO;AACT", "debugId": null}}]}
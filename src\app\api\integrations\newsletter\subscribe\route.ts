import { NextRequest, NextResponse } from 'next/server'
import { D1Database } from '@cloudflare/workers-types'
import { getRequestContext } from '@/lib/cloudflare'

export async function POST(request: NextRequest) {
  try {
    const { email, name, tags, language } = await request.json()
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!email || !emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email address' },
        { status: 400 }
      )
    }
    
    // 在实际应用中，这里应该连接到数据库
    // const { env } = getRequestContext()
    // const db = env.DB as D1Database
    
    // 检查邮箱是否已存在
    // const existingSubscriber = await db.prepare(
    //   'SELECT id FROM newsletter_subscribers WHERE email = ?'
    // ).bind(email).first()
    
    // if (existingSubscriber) {
    //   return NextResponse.json(
    //     { success: false, error: 'Em<PERSON> already subscribed' },
    //     { status: 409 }
    //   )
    // }
    
    // 创建订阅记录
    // const subscriberId = crypto.randomUUID()
    // await db.prepare(`
    //   INSERT INTO newsletter_subscribers (id, email, name, tags, language, created_at, active)
    //   VALUES (?, ?, ?, ?, ?, ?, ?)
    // `).bind(
    //   subscriberId,
    //   email,
    //   name || null,
    //   tags ? JSON.stringify(tags) : null,
    //   language || 'en',
    //   new Date().toISOString(),
    //   true
    // ).run()
    
    // 在实际应用中，这里应该调用邮件服务商API
    // 例如 Mailchimp, ConvertKit, Sendinblue 等
    
    // 模拟成功响应
    return NextResponse.json({
      success: true,
      message: 'Successfully subscribed to newsletter',
      data: {
        subscriberId: crypto.randomUUID(),
        email,
        name,
        tags,
        language
      }
    })
  } catch (error) {
    console.error('Newsletter subscription error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to subscribe to newsletter' },
      { status: 500 }
    )
  }
}

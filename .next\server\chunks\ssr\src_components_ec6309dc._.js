module.exports = {

"[project]/src/components/article/article-editor.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_prismjs_components_444051f8._.js",
  "server/chunks/ssr/[root-of-the-server]__85ce9a97._.js",
  "server/chunks/ssr/node_modules_f452a20a._.js",
  "server/chunks/ssr/node_modules_micromark-core-commonmark_dev_lib_fbbb9cae._.js",
  "server/chunks/ssr/node_modules_highlight_27b10e40.js",
  "server/chunks/ssr/node_modules_parse5_dist_2032ba6e._.js",
  "server/chunks/ssr/node_modules_@radix-ui_64eb0f83._.js",
  "server/chunks/ssr/node_modules_@floating-ui_90d70670._.js",
  "server/chunks/ssr/node_modules_bddb2c34._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/article/article-editor.tsx [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/components/files/file-manager.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_a2d41bc9._.js",
  "server/chunks/ssr/node_modules_e4a8f428._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/files/file-manager.tsx [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/components/search/search-interface.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_bf5f0baf._.js",
  "server/chunks/ssr/[root-of-the-server]__faf388f6._.js",
  "server/chunks/ssr/node_modules_1785af62._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/search/search-interface.tsx [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/components/article/ai-assistant.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_b0073bff._.js",
  "server/chunks/ssr/src_components_4911b283._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/article/ai-assistant.tsx [app-ssr] (ecmascript)");
    });
});
}}),

};
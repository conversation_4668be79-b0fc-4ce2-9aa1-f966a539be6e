'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Plus, Edit, Trash2, MoreHorizontal, Search, Globe, Eye, Menu } from 'lucide-react'
import Link from 'next/link'

interface Page {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  meta_title?: string
  meta_description?: string
  status: 'draft' | 'published' | 'private'
  template: string
  order_index: number
  is_in_menu: boolean
  menu_title?: string
  parent_id?: string
  created_at: string
  updated_at: string
  published_at?: string
  created_by: string
}

export function PageManager() {
  const t = useTranslations('admin.pages')
  const [pages, setPages] = useState<Page[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deletingPage, setDeletingPage] = useState<Page | null>(null)

  // 模拟数据加载
  useEffect(() => {
    const loadPages = async () => {
      setLoading(true)
      try {
        // 这里应该调用实际的 API
        const mockPages: Page[] = [
          {
            id: '1',
            title: '关于我们',
            slug: 'about',
            content: '# 关于我们\n\n这里是关于页面的内容...',
            excerpt: '了解更多关于我们的信息',
            status: 'published',
            template: 'default',
            order_index: 1,
            is_in_menu: true,
            menu_title: '关于',
            created_at: '2024-01-01',
            updated_at: '2024-01-01',
            published_at: '2024-01-01',
            created_by: 'user1',
          },
          {
            id: '2',
            title: '联系我们',
            slug: 'contact',
            content: '# 联系我们\n\n如有任何问题，请随时联系我们...',
            excerpt: '联系方式和反馈渠道',
            status: 'published',
            template: 'contact',
            order_index: 2,
            is_in_menu: true,
            menu_title: '联系',
            created_at: '2024-01-01',
            updated_at: '2024-01-01',
            published_at: '2024-01-01',
            created_by: 'user1',
          },
          {
            id: '3',
            title: '隐私政策',
            slug: 'privacy',
            content: '# 隐私政策\n\n我们重视您的隐私...',
            status: 'draft',
            template: 'default',
            order_index: 3,
            is_in_menu: false,
            created_at: '2024-01-01',
            updated_at: '2024-01-01',
            created_by: 'user1',
          },
        ]

        setPages(mockPages)
      } catch (error) {
        console.error('Failed to load pages:', error)
      } finally {
        setLoading(false)
      }
    }

    loadPages()
  }, [])

  const filteredPages = pages.filter(page =>
    page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.excerpt?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleDelete = async (page: Page) => {
    setDeletingPage(page)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!deletingPage) return

    try {
      // 这里应该调用删除 API
      setPages(prev => prev.filter(p => p.id !== deletingPage.id))
      setDeleteDialogOpen(false)
      setDeletingPage(null)
    } catch (error) {
      console.error('Failed to delete page:', error)
    }
  }

  const getStatusBadge = (status: Page['status']) => {
    const variants = {
      published: 'default',
      draft: 'secondary',
      private: 'outline',
    } as const

    const labels = {
      published: t('published'),
      draft: t('draft'),
      private: t('private'),
    }

    return (
      <Badge variant={variants[status]}>
        {labels[status]}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t('loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground">{t('subtitle')}</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/pages/new">
            <Plus className="h-4 w-4 mr-2" />
            {t('createPage')}
          </Link>
        </Button>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('pageList')}</CardTitle>
          <CardDescription>
            {t('pageListDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('title')}</TableHead>
                <TableHead>{t('slug')}</TableHead>
                <TableHead>{t('status')}</TableHead>
                <TableHead>{t('template')}</TableHead>
                <TableHead>{t('inMenu')}</TableHead>
                <TableHead>{t('updatedAt')}</TableHead>
                <TableHead className="w-[70px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPages.map((page) => (
                <TableRow key={page.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{page.title}</div>
                        {page.excerpt && (
                          <div className="text-sm text-muted-foreground">
                            {page.excerpt}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      /{page.slug}
                    </code>
                  </TableCell>
                  <TableCell>{getStatusBadge(page.status)}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{page.template}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {page.is_in_menu ? (
                        <>
                          <Menu className="h-4 w-4 text-green-600" />
                          <span className="text-sm">{page.menu_title || page.title}</span>
                        </>
                      ) : (
                        <span className="text-sm text-muted-foreground">-</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(page.updated_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/pages/${page.slug}`} target="_blank">
                            <Eye className="h-4 w-4 mr-2" />
                            {t('preview')}
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/pages/edit/${page.id}`}>
                            <Edit className="h-4 w-4 mr-2" />
                            {t('edit')}
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(page)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          {t('delete')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredPages.length === 0 && (
            <div className="text-center py-8">
              <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">{t('noPages')}</h3>
              <p className="text-muted-foreground mb-4">{t('noPagesDescription')}</p>
              <Button asChild>
                <Link href="/dashboard/pages/new">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('createFirstPage')}
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('deletePage')}</DialogTitle>
            <DialogDescription>
              {t('deletePageConfirm', { title: deletingPage?.title })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              {t('cancel')}
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              {t('delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

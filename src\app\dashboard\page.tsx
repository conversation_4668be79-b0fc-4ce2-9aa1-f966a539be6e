import { requireCollaborator } from "@/lib/auth-utils"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AdminLayout } from "@/components/admin/admin-layout"
import { DashboardStats } from "@/components/analytics/dashboard-stats"
import { ResponsiveGrid } from "@/components/ui/responsive-grid"
import { FileText, Plus, Users, BarChart3, Settings } from "lucide-react"
import Link from "next/link"

export default async function DashboardPage() {
  const user = await requireCollaborator()

  const stats = [
    {
      title: "总文章数",
      value: "12",
      description: "已发布的文章",
      icon: FileText,
      color: "text-blue-600",
    },
    {
      title: "草稿",
      value: "3",
      description: "未发布的草稿",
      icon: FileText,
      color: "text-orange-600",
    },
    {
      title: "总访问量",
      value: "1,234",
      description: "本月访问量",
      icon: BarChart3,
      color: "text-green-600",
    },
    {
      title: "用户数",
      value: "56",
      description: "注册用户",
      icon: Users,
      color: "text-purple-600",
    },
  ]

  const quickActions = [
    {
      title: "创建新文章",
      description: "开始写作新的博客文章",
      href: "/dashboard/articles/new",
      icon: Plus,
      color: "bg-blue-500 hover:bg-blue-600",
    },
    {
      title: "管理文章",
      description: "查看和编辑现有文章",
      href: "/dashboard/articles",
      icon: FileText,
      color: "bg-green-500 hover:bg-green-600",
    },
    {
      title: "文件管理",
      description: "上传和管理媒体文件",
      href: "/files",
      icon: Settings,
      color: "bg-purple-500 hover:bg-purple-600",
    },
  ]

  return (
    <AdminLayout>
      <div className="space-y-8">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">仪表板</h1>
              <p className="text-muted-foreground">
                欢迎回来，{user.name}！管理您的内容和设置。
              </p>
            </div>
            <Button asChild>
              <Link href="/dashboard/articles/new">
                <Plus className="w-4 h-4 mr-2" />
                创建文章
              </Link>
            </Button>
          </div>

          {/* 统计概览 */}
          <DashboardStats />

          {/* 统计卡片 */}
          <ResponsiveGrid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>
            {stats.map((stat) => {
              const Icon = stat.icon
              return (
                <Card key={stat.title}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {stat.title}
                    </CardTitle>
                    <Icon className={`h-4 w-4 ${stat.color}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stat.value}</div>
                    <p className="text-xs text-muted-foreground">
                      {stat.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </ResponsiveGrid>

          {/* 快速操作 */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">快速操作</h2>
            <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>
              {quickActions.map((action) => {
                const Icon = action.icon
                return (
                  <Card key={action.title} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className={`w-12 h-12 rounded-lg ${action.color} flex items-center justify-center mb-4`}>
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <CardTitle>{action.title}</CardTitle>
                      <CardDescription>{action.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button asChild className="w-full">
                        <Link href={action.href}>
                          开始
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </ResponsiveGrid>
          </div>

          {/* 最近活动 */}
          <Card>
            <CardHeader>
              <CardTitle>最近活动</CardTitle>
              <CardDescription>
                您最近的操作记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm">创建了新文章 "Next.js 博客系统开发指南"</p>
                    <p className="text-xs text-muted-foreground">2 小时前</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm">发布了文章 "现代化前端开发实践"</p>
                    <p className="text-xs text-muted-foreground">1 天前</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm">上传了 3 个媒体文件</p>
                    <p className="text-xs text-muted-foreground">2 天前</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
    </AdminLayout>
  )
}

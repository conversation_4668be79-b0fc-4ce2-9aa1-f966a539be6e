'use client'

import { useState, useTransition } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useLocale } from 'next-intl'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Globe, Check } from 'lucide-react'
import { locales, localeConfig, type Locale } from '@/i18n/config'

interface LanguageSwitcherProps {
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg'
  showLabel?: boolean
  className?: string
}

export function LanguageSwitcher({
  variant = 'ghost',
  size = 'default',
  showLabel = false,
  className = '',
}: LanguageSwitcherProps) {
  const router = useRouter()
  const pathname = usePathname()
  const locale = useLocale() as Locale
  const [isPending, startTransition] = useTransition()
  const [isOpen, setIsOpen] = useState(false)

  const currentLocaleConfig = localeConfig[locale]

  const handleLocaleChange = (newLocale: Locale) => {
    if (newLocale === locale) return

    startTransition(() => {
      // 替换当前路径中的语言代码
      const segments = pathname.split('/')
      segments[1] = newLocale
      const newPath = segments.join('/')
      
      router.push(newPath)
      setIsOpen(false)
    })
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={`gap-2 ${className}`}
          disabled={isPending}
        >
          <Globe className="h-4 w-4" />
          <span className="flex items-center gap-1">
            <span>{currentLocaleConfig.flag}</span>
            {showLabel && (
              <span className="hidden sm:inline">
                {currentLocaleConfig.name}
              </span>
            )}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[150px]">
        {locales.map((localeOption) => {
          const config = localeConfig[localeOption]
          const isSelected = localeOption === locale
          
          return (
            <DropdownMenuItem
              key={localeOption}
              onClick={() => handleLocaleChange(localeOption)}
              className="flex items-center justify-between cursor-pointer"
              disabled={isPending}
            >
              <div className="flex items-center gap-2">
                <span>{config.flag}</span>
                <span>{config.name}</span>
              </div>
              {isSelected && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 简化版语言切换器，只显示图标
 */
export function LanguageSwitcherCompact({
  className = '',
}: {
  className?: string
}) {
  return (
    <LanguageSwitcher
      variant="ghost"
      size="sm"
      showLabel={false}
      className={className}
    />
  )
}

/**
 * 带标签的语言切换器
 */
export function LanguageSwitcherWithLabel({
  className = '',
}: {
  className?: string
}) {
  return (
    <LanguageSwitcher
      variant="outline"
      size="default"
      showLabel={true}
      className={className}
    />
  )
}

/**
 * 移动端友好的语言切换器
 */
export function LanguageSwitcherMobile({
  className = '',
}: {
  className?: string
}) {
  const locale = useLocale() as Locale
  const currentConfig = localeConfig[locale]

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Globe className="h-4 w-4 text-muted-foreground" />
      <LanguageSwitcher
        variant="ghost"
        size="sm"
        showLabel={true}
        className="justify-start"
      />
    </div>
  )
}

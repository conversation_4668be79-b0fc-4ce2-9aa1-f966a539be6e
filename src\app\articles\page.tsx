import { Suspense } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { PageContainer } from "@/components/layout/page-container"
import { ArticleCard } from "@/components/ui/article-card"
import { ArticleCardSkeleton } from "@/components/ui/loading"
import { ResponsiveGrid } from "@/components/ui/responsive-grid"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter } from "lucide-react"
import { WebsiteStructuredData, BreadcrumbStructuredData } from "@/components/seo/structured-data"
import { getArticles, mockCategories, mockTags } from "@/lib/mock-data"
import { generateBaseMetadata } from "@/lib/seo"
import type { Metadata } from "next"

interface ArticlesPageProps {
  searchParams: {
    page?: string
    search?: string
    category?: string
    tag?: string
  }
}

// 生成页面元数据
export async function generateMetadata({ searchParams }: ArticlesPageProps): Promise<Metadata> {
  const { search, category, tag } = searchParams

  let title = "文章列表"
  let description = "探索我们精心整理的技术文章和经验分享"
  let keywords = ["文章", "博客", "技术", "分享"]

  if (search) {
    title = `搜索: ${search}`
    description = `搜索 "${search}" 的文章结果`
    keywords.push(search)
  } else if (category) {
    title = `${category} 分类文章`
    description = `查看 ${category} 分类下的所有文章`
    keywords.push(category)
  } else if (tag) {
    title = `${tag} 标签文章`
    description = `查看包含 ${tag} 标签的所有文章`
    keywords.push(tag)
  }

  return generateBaseMetadata({
    title,
    description,
    keywords,
  })
}

export default function ArticlesPage({ searchParams }: ArticlesPageProps) {
  const page = parseInt(searchParams.page || "1")
  const search = searchParams.search || ""
  const category = searchParams.category || ""
  const tag = searchParams.tag || ""

  const { articles, pagination } = getArticles({
    page,
    search,
    category,
    tag,
    limit: 9,
  })

  // 面包屑导航数据
  const breadcrumbItems = [
    { name: "首页", url: "/" },
    { name: "文章", url: "/articles" },
  ]

  if (category) {
    breadcrumbItems.push({ name: category, url: `/categories/${category}` })
  }
  if (tag) {
    breadcrumbItems.push({ name: tag, url: `/tags/${tag}` })
  }

  return (
    <>
      {/* 结构化数据 */}
      <WebsiteStructuredData />
      <BreadcrumbStructuredData items={breadcrumbItems} />

      <MainLayout>
      <PageContainer maxWidth="full">
        <div className="space-y-8">
          {/* 页面标题 */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold">文章列表</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              探索我们精心整理的技术文章和经验分享
            </p>
          </div>

          {/* 搜索和筛选 */}
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
              {/* 搜索框 */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="搜索文章..."
                  className="pl-10 w-full sm:w-80"
                  defaultValue={search}
                />
              </div>

              {/* 分类筛选 */}
              <Select defaultValue={category}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">所有分类</SelectItem>
                  {mockCategories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.name}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 统计信息 */}
            <div className="text-sm text-muted-foreground">
              共找到 {pagination.total} 篇文章
            </div>
          </div>

          {/* 热门标签 */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Filter className="h-4 w-4" />
              热门标签
            </h2>
            <div className="flex flex-wrap gap-2">
              {mockTags.map((tagItem) => (
                <Badge
                  key={tagItem.id}
                  variant={tag === tagItem.name ? "default" : "secondary"}
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                >
                  {tagItem.name} ({tagItem.articleCount})
                </Badge>
              ))}
            </div>
          </div>

          {/* 文章列表 */}
          <Suspense fallback={
            <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>
              {Array.from({ length: 6 }).map((_, i) => (
                <ArticleCardSkeleton key={i} />
              ))}
            </ResponsiveGrid>
          }>
            {articles.length > 0 ? (
              <ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }} gap={6}>
                {articles.map((article, index) => (
                  <ArticleCard
                    key={article.id}
                    article={article}
                    variant={index === 0 ? "featured" : "default"}
                  />
                ))}
              </ResponsiveGrid>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📝</div>
                <h3 className="text-xl font-semibold mb-2">暂无文章</h3>
                <p className="text-muted-foreground">
                  {search || category || tag
                    ? "没有找到符合条件的文章，请尝试其他搜索条件"
                    : "还没有发布任何文章"}
                </p>
              </div>
            )}
          </Suspense>

          {/* 分页 */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center gap-2">
              <Button
                variant="outline"
                disabled={pagination.page <= 1}
              >
                上一页
              </Button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, pagination.totalPages) }).map((_, i) => {
                  const pageNum = i + 1
                  return (
                    <Button
                      key={pageNum}
                      variant={pageNum === pagination.page ? "default" : "outline"}
                      size="sm"
                    >
                      {pageNum}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                disabled={pagination.page >= pagination.totalPages}
              >
                下一页
              </Button>
            </div>
          )}
        </div>
      </PageContainer>
    </MainLayout>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { D1Database } from '@cloudflare/workers-types'
import { getRequestContext } from '@/lib/cloudflare'

// 默认站点地图配置
const defaultConfig = {
  enabled: true,
  autoGenerate: true,
  includeImages: true,
  includeNews: false,
  includeVideos: false,
  changeFreq: 'weekly',
  priority: 0.8,
  languages: ['zh', 'en', 'ja'],
  excludePaths: ['/admin', '/api', '/private'],
  customUrls: []
}

export async function GET(request: NextRequest) {
  try {
    // 在实际应用中，这里应该从数据库中获取配置
    // const { env } = getRequestContext()
    // const db = env.DB as D1Database
    // const result = await db.prepare('SELECT * FROM sitemap_config LIMIT 1').first()
    // const config = result || defaultConfig
    
    // 目前使用默认配置
    const config = defaultConfig
    
    return NextResponse.json({
      success: true,
      data: { config }
    })
  } catch (error) {
    console.error('Sitemap config API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch sitemap configuration' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const config = await request.json()
    
    // 验证配置
    if (typeof config.enabled !== 'boolean' || 
        typeof config.autoGenerate !== 'boolean' ||
        typeof config.includeImages !== 'boolean' ||
        typeof config.includeNews !== 'boolean' ||
        typeof config.includeVideos !== 'boolean' ||
        typeof config.priority !== 'number' ||
        !Array.isArray(config.languages) ||
        !Array.isArray(config.excludePaths) ||
        !Array.isArray(config.customUrls)) {
      return NextResponse.json(
        { success: false, error: 'Invalid configuration format' },
        { status: 400 }
      )
    }
    
    // 在实际应用中，这里应该将配置保存到数据库
    // const { env } = getRequestContext()
    // const db = env.DB as D1Database
    // await db.prepare('UPDATE sitemap_config SET ...').bind(...).run()
    
    return NextResponse.json({
      success: true,
      message: 'Sitemap configuration updated successfully'
    })
  } catch (error) {
    console.error('Update sitemap config error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update sitemap configuration' },
      { status: 500 }
    )
  }
}

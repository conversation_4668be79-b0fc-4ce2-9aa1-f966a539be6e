"use client"

import * as React from "react"
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "next-themes"
import { useTranslations } from "next-intl"
import { motion, AnimatePresence } from "framer-motion"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

export function ThemeToggle() {
  const { setTheme, theme, systemTheme } = useTheme()
  const t = useTranslations('theme')
  const [mounted, setMounted] = React.useState(false)
  const [isChanging, setIsChanging] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  // 主题切换动画
  const handleThemeChange = async (newTheme: string) => {
    setIsChanging(true)

    // 添加页面切换动画
    if (document.startViewTransition) {
      await document.startViewTransition(() => {
        setTheme(newTheme)
      }).finished
    } else {
      setTheme(newTheme)
    }

    setTimeout(() => setIsChanging(false), 300)
  }

  // 获取当前实际主题
  const currentTheme = theme === 'system' ? systemTheme : theme

  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" disabled>
        <div className="h-[1.2rem] w-[1.2rem] animate-pulse bg-muted rounded" />
        <span className="sr-only">Loading theme</span>
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "relative overflow-hidden transition-all duration-300 hover:scale-105 hover:bg-accent",
            isChanging && "animate-pulse"
          )}
          disabled={isChanging}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={currentTheme}
              initial={{ rotate: -90, scale: 0 }}
              animate={{ rotate: 0, scale: 1 }}
              exit={{ rotate: 90, scale: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="absolute inset-0 flex items-center justify-center"
            >
              {currentTheme === 'dark' ? (
                <Moon className="h-[1.2rem] w-[1.2rem]" />
              ) : (
                <Sun className="h-[1.2rem] w-[1.2rem]" />
              )}
            </motion.div>
          </AnimatePresence>
          <span className="sr-only">{t('toggle')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          {t('appearance')}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => handleThemeChange("light")}
          className="flex items-center gap-3 cursor-pointer"
          disabled={isChanging}
        >
          <Sun className="h-4 w-4" />
          <span className="flex-1">{t('light')}</span>
          <AnimatePresence>
            {theme === "light" && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Check className="h-4 w-4 text-primary" />
              </motion.div>
            )}
          </AnimatePresence>
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleThemeChange("dark")}
          className="flex items-center gap-3 cursor-pointer"
          disabled={isChanging}
        >
          <Moon className="h-4 w-4" />
          <span className="flex-1">{t('dark')}</span>
          <AnimatePresence>
            {theme === "dark" && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Check className="h-4 w-4 text-primary" />
              </motion.div>
            )}
          </AnimatePresence>
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleThemeChange("system")}
          className="flex items-center gap-3 cursor-pointer"
          disabled={isChanging}
        >
          <Monitor className="h-4 w-4" />
          <span className="flex-1">{t('system')}</span>
          <AnimatePresence>
            {theme === "system" && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Check className="h-4 w-4 text-primary" />
              </motion.div>
            )}
          </AnimatePresence>
        </DropdownMenuItem>

        {theme === "system" && (
          <>
            <DropdownMenuSeparator />
            <div className="px-2 py-1.5 text-xs text-muted-foreground">
              {t('systemDetected')}: {systemTheme === 'dark' ? t('dark') : t('light')}
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

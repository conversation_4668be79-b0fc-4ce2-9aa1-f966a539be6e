import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { Suspense } from 'react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Skeleton } from '@/components/ui/skeleton'
import { FollowSystem } from '@/components/social/follow-system'
import { ActivityTimeline } from '@/components/social/activity-timeline'
import { MessagingSystem } from '@/components/social/messaging-system'
import { 
  Users, 
  Activity, 
  MessageCircle, 
  TrendingUp,
  Heart,
  Bookmark,
  Share2
} from 'lucide-react'

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'social' })
  
  return {
    title: t('title'),
    description: t('description'),
  }
}

// 模拟当前用户数据
const currentUser = {
  id: 'user-1',
  username: 'johndo<PERSON>',
  displayName: '<PERSON>',
  avatar: '/avatars/john-doe.jpg',
  verified: true,
  followersCount: 1234,
  followingCount: 567,
  articlesCount: 89,
  role: 'author' as const
}

// 模拟社交统计数据
const socialStats = {
  totalFollowers: 1234,
  totalFollowing: 567,
  totalLikes: 8901,
  totalBookmarks: 2345,
  totalShares: 1567,
  totalComments: 4321
}

export default async function SocialPage({ params }: { params: { locale: string } }) {
  const t = await getTranslations({ locale: params.locale, namespace: 'social' })

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* 页面头部 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
        <p className="text-muted-foreground">{t('description')}</p>
      </div>

      {/* 社交统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 mx-auto mb-2 text-blue-500" />
            <div className="text-2xl font-bold">{socialStats.totalFollowers.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">{t('followers')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <div className="text-2xl font-bold">{socialStats.totalFollowing.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">{t('following')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Heart className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <div className="text-2xl font-bold">{socialStats.totalLikes.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">{t('likes')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Bookmark className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
            <div className="text-2xl font-bold">{socialStats.totalBookmarks.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">{t('bookmarks')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Share2 className="h-8 w-8 mx-auto mb-2 text-purple-500" />
            <div className="text-2xl font-bold">{socialStats.totalShares.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">{t('shares')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 text-orange-500" />
            <div className="text-2xl font-bold">{socialStats.totalComments.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">{t('comments')}</div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="timeline" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="timeline" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            {t('timeline')}
          </TabsTrigger>
          <TabsTrigger value="following" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            {t('following')}
          </TabsTrigger>
          <TabsTrigger value="messages" className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            {t('messages')}
          </TabsTrigger>
          <TabsTrigger value="trending" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            {t('trending')}
          </TabsTrigger>
        </TabsList>

        {/* 时间线标签页 */}
        <TabsContent value="timeline">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    {t('activityFeed')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Suspense fallback={<TimelineSkeleton />}>
                    <ActivityTimeline
                      feedType="following"
                      showFilters={true}
                      maxItems={20}
                    />
                  </Suspense>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              {/* 关注建议 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    {t('suggestedUsers')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Suspense fallback={<FollowSkeleton />}>
                    <FollowSystem
                      currentUser={currentUser}
                      onFollow={async (userId) => {
                        // 实现关注逻辑
                        console.log('Following user:', userId)
                      }}
                      onUnfollow={async (userId) => {
                        // 实现取消关注逻辑
                        console.log('Unfollowing user:', userId)
                      }}
                    />
                  </Suspense>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* 关注标签页 */}
        <TabsContent value="following">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t('followingManagement')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<FollowSkeleton />}>
                <FollowSystem
                  currentUser={currentUser}
                  targetUser={currentUser}
                  onFollow={async (userId) => {
                    console.log('Following user:', userId)
                  }}
                  onUnfollow={async (userId) => {
                    console.log('Unfollowing user:', userId)
                  }}
                  onBlock={async (userId) => {
                    console.log('Blocking user:', userId)
                  }}
                />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 消息标签页 */}
        <TabsContent value="messages">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                {t('messaging')}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <Suspense fallback={<MessagingSkeleton />}>
                <MessagingSystem
                  currentUserId={currentUser.id}
                  onConversationSelect={(conversationId) => {
                    console.log('Selected conversation:', conversationId)
                  }}
                />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 热门标签页 */}
        <TabsContent value="trending">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  {t('trendingActivities')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Suspense fallback={<TimelineSkeleton />}>
                  <ActivityTimeline
                    feedType="trending"
                    showFilters={false}
                    compact={true}
                    maxItems={10}
                  />
                </Suspense>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t('popularUsers')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Suspense fallback={<FollowSkeleton />}>
                  <FollowSystem
                    currentUser={currentUser}
                    onFollow={async (userId) => {
                      console.log('Following user:', userId)
                    }}
                    onUnfollow={async (userId) => {
                      console.log('Unfollowing user:', userId)
                    }}
                  />
                </Suspense>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// 骨架屏组件
function TimelineSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="flex items-start gap-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
      ))}
    </div>
  )
}

function FollowSkeleton() {
  return (
    <div className="space-y-3">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
          <Skeleton className="h-8 w-16" />
        </div>
      ))}
    </div>
  )
}

function MessagingSkeleton() {
  return (
    <div className="h-[600px] flex">
      <div className="w-80 border-r p-4 space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        ))}
      </div>
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Skeleton className="h-12 w-12 rounded-full mx-auto mb-4" />
          <Skeleton className="h-4 w-48 mx-auto mb-2" />
          <Skeleton className="h-3 w-32 mx-auto" />
        </div>
      </div>
    </div>
  )
}

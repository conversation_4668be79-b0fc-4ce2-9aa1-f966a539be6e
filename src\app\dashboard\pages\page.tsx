import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { AdminLayout } from '@/components/admin/admin-layout'
import { PageManager } from '@/components/admin/page-manager'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('admin.pages')
  
  return {
    title: t('title'),
    description: t('subtitle'),
  }
}

export default function PagesManagementPage() {
  return (
    <AdminLayout>
      <PageManager />
    </AdminLayout>
  )
}

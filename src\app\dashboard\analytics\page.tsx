import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AnalyticsCharts } from '@/components/analytics/analytics-charts'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('analytics')
  
  return {
    title: t('title'),
    description: t('subtitle'),
  }
}

export default function AnalyticsPage() {
  return (
    <AdminLayout>
      <AnalyticsCharts />
    </AdminLayout>
  )
}

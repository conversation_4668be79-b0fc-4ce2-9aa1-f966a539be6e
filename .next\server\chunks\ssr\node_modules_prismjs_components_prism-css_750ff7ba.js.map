{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/node_modules/prismjs/components/prism-css.js"], "sourcesContent": ["(function (Prism) {\n\n\tvar string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n\n\tPrism.languages.css = {\n\t\t'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n\t\t'atrule': {\n\t\t\tpattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n\t\t\tinside: {\n\t\t\t\t'rule': /^@[\\w-]+/,\n\t\t\t\t'selector-function-argument': {\n\t\t\t\t\tpattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n\t\t\t\t\tlookbehind: true,\n\t\t\t\t\talias: 'selector'\n\t\t\t\t},\n\t\t\t\t'keyword': {\n\t\t\t\t\tpattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n\t\t\t\t\tlookbehind: true\n\t\t\t\t}\n\t\t\t\t// See rest below\n\t\t\t}\n\t\t},\n\t\t'url': {\n\t\t\t// https://drafts.csswg.org/css-values-3/#urls\n\t\t\tpattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^url/i,\n\t\t\t\t'punctuation': /^\\(|\\)$/,\n\t\t\t\t'string': {\n\t\t\t\t\tpattern: RegExp('^' + string.source + '$'),\n\t\t\t\t\talias: 'url'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t'selector': {\n\t\t\tpattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n\t\t\tlookbehind: true\n\t\t},\n\t\t'string': {\n\t\t\tpattern: string,\n\t\t\tgreedy: true\n\t\t},\n\t\t'property': {\n\t\t\tpattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'important': /!important\\b/i,\n\t\t'function': {\n\t\t\tpattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n\t\t\tlookbehind: true\n\t\t},\n\t\t'punctuation': /[(){};:,]/\n\t};\n\n\tPrism.languages.css['atrule'].inside.rest = Prism.languages.css;\n\n\tvar markup = Prism.languages.markup;\n\tif (markup) {\n\t\tmarkup.tag.addInlined('style', 'css');\n\t\tmarkup.tag.addAttribute('style', 'css');\n\t}\n\n}(Prism));\n"], "names": [], "mappings": "AAAC,CAAA,SAAU,MAAK;IAEf,IAAI,SAAS;IAEb,OAAM,SAAS,CAAC,GAAG,GAAG;QACrB,WAAW;QACX,UAAU;YACT,SAAS,OAAO,eAAe,sBAAsB,MAAM,GAAG,MAAM,OAAO,MAAM,GAAG,QAAQ,kBAAkB,MAAM;YACpH,QAAQ;gBACP,QAAQ;gBACR,8BAA8B;oBAC7B,SAAS;oBACT,YAAY;oBACZ,OAAO;gBACR;gBACA,WAAW;oBACV,SAAS;oBACT,YAAY;gBACb;YAED;QACD;QACA,OAAO;YACN,8CAA8C;YAC9C,SAAS,OAAO,iBAAiB,OAAO,MAAM,GAAG,MAAM,8BAA8B,MAAM,GAAG,QAAQ;YACtG,QAAQ;YACR,QAAQ;gBACP,YAAY;gBACZ,eAAe;gBACf,UAAU;oBACT,SAAS,OAAO,MAAM,OAAO,MAAM,GAAG;oBACtC,OAAO;gBACR;YACD;QACD;QACA,YAAY;YACX,SAAS,OAAO,uDAAuD,OAAO,MAAM,GAAG;YACvF,YAAY;QACb;QACA,UAAU;YACT,SAAS;YACT,QAAQ;QACT;QACA,YAAY;YACX,SAAS;YACT,YAAY;QACb;QACA,aAAa;QACb,YAAY;YACX,SAAS;YACT,YAAY;QACb;QACA,eAAe;IAChB;IAEA,OAAM,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,OAAM,SAAS,CAAC,GAAG;IAE/D,IAAI,SAAS,OAAM,SAAS,CAAC,MAAM;IACnC,IAAI,QAAQ;QACX,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS;QAC/B,OAAO,GAAG,CAAC,YAAY,CAAC,SAAS;IAClC;AAED,CAAA,EAAE", "ignoreList": [0], "debugId": null}}]}
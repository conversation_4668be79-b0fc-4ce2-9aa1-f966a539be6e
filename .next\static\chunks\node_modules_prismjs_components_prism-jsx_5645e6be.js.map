{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/node_modules/prismjs/components/prism-jsx.js"], "sourcesContent": ["(function (Prism) {\n\n\tvar javascript = Prism.util.clone(Prism.languages.javascript);\n\n\tvar space = /(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source;\n\tvar braces = /(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source;\n\tvar spread = /(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source;\n\n\t/**\n\t * @param {string} source\n\t * @param {string} [flags]\n\t */\n\tfunction re(source, flags) {\n\t\tsource = source\n\t\t\t.replace(/<S>/g, function () { return space; })\n\t\t\t.replace(/<BRACES>/g, function () { return braces; })\n\t\t\t.replace(/<SPREAD>/g, function () { return spread; });\n\t\treturn RegExp(source, flags);\n\t}\n\n\tspread = re(spread).source;\n\n\n\tPrism.languages.jsx = Prism.languages.extend('markup', javascript);\n\tPrism.languages.jsx.tag.pattern = re(\n\t\t/<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/.source\n\t);\n\n\tPrism.languages.jsx.tag.inside['tag'].pattern = /^<\\/?[^\\s>\\/]*/;\n\tPrism.languages.jsx.tag.inside['attr-value'].pattern = /=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/;\n\tPrism.languages.jsx.tag.inside['tag'].inside['class-name'] = /^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/;\n\tPrism.languages.jsx.tag.inside['comment'] = javascript['comment'];\n\n\tPrism.languages.insertBefore('inside', 'attr-name', {\n\t\t'spread': {\n\t\t\tpattern: re(/<SPREAD>/.source),\n\t\t\tinside: Prism.languages.jsx\n\t\t}\n\t}, Prism.languages.jsx.tag);\n\n\tPrism.languages.insertBefore('inside', 'special-attr', {\n\t\t'script': {\n\t\t\t// Allow for two levels of nesting\n\t\t\tpattern: re(/=<BRACES>/.source),\n\t\t\talias: 'language-javascript',\n\t\t\tinside: {\n\t\t\t\t'script-punctuation': {\n\t\t\t\t\tpattern: /^=(?=\\{)/,\n\t\t\t\t\talias: 'punctuation'\n\t\t\t\t},\n\t\t\t\trest: Prism.languages.jsx\n\t\t\t},\n\t\t}\n\t}, Prism.languages.jsx.tag);\n\n\t// The following will handle plain text inside tags\n\tvar stringifyToken = function (token) {\n\t\tif (!token) {\n\t\t\treturn '';\n\t\t}\n\t\tif (typeof token === 'string') {\n\t\t\treturn token;\n\t\t}\n\t\tif (typeof token.content === 'string') {\n\t\t\treturn token.content;\n\t\t}\n\t\treturn token.content.map(stringifyToken).join('');\n\t};\n\n\tvar walkTokens = function (tokens) {\n\t\tvar openedTags = [];\n\t\tfor (var i = 0; i < tokens.length; i++) {\n\t\t\tvar token = tokens[i];\n\t\t\tvar notTagNorBrace = false;\n\n\t\t\tif (typeof token !== 'string') {\n\t\t\t\tif (token.type === 'tag' && token.content[0] && token.content[0].type === 'tag') {\n\t\t\t\t\t// We found a tag, now find its kind\n\n\t\t\t\t\tif (token.content[0].content[0].content === '</') {\n\t\t\t\t\t\t// Closing tag\n\t\t\t\t\t\tif (openedTags.length > 0 && openedTags[openedTags.length - 1].tagName === stringifyToken(token.content[0].content[1])) {\n\t\t\t\t\t\t\t// Pop matching opening tag\n\t\t\t\t\t\t\topenedTags.pop();\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (token.content[token.content.length - 1].content === '/>') {\n\t\t\t\t\t\t\t// Autoclosed tag, ignore\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Opening tag\n\t\t\t\t\t\t\topenedTags.push({\n\t\t\t\t\t\t\t\ttagName: stringifyToken(token.content[0].content[1]),\n\t\t\t\t\t\t\t\topenedBraces: 0\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (openedTags.length > 0 && token.type === 'punctuation' && token.content === '{') {\n\n\t\t\t\t\t// Here we might have entered a JSX context inside a tag\n\t\t\t\t\topenedTags[openedTags.length - 1].openedBraces++;\n\n\t\t\t\t} else if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces > 0 && token.type === 'punctuation' && token.content === '}') {\n\n\t\t\t\t\t// Here we might have left a JSX context inside a tag\n\t\t\t\t\topenedTags[openedTags.length - 1].openedBraces--;\n\n\t\t\t\t} else {\n\t\t\t\t\tnotTagNorBrace = true;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (notTagNorBrace || typeof token === 'string') {\n\t\t\t\tif (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces === 0) {\n\t\t\t\t\t// Here we are inside a tag, and not inside a JSX context.\n\t\t\t\t\t// That's plain text: drop any tokens matched.\n\t\t\t\t\tvar plainText = stringifyToken(token);\n\n\t\t\t\t\t// And merge text with adjacent text\n\t\t\t\t\tif (i < tokens.length - 1 && (typeof tokens[i + 1] === 'string' || tokens[i + 1].type === 'plain-text')) {\n\t\t\t\t\t\tplainText += stringifyToken(tokens[i + 1]);\n\t\t\t\t\t\ttokens.splice(i + 1, 1);\n\t\t\t\t\t}\n\t\t\t\t\tif (i > 0 && (typeof tokens[i - 1] === 'string' || tokens[i - 1].type === 'plain-text')) {\n\t\t\t\t\t\tplainText = stringifyToken(tokens[i - 1]) + plainText;\n\t\t\t\t\t\ttokens.splice(i - 1, 1);\n\t\t\t\t\t\ti--;\n\t\t\t\t\t}\n\n\t\t\t\t\ttokens[i] = new Prism.Token('plain-text', plainText, null, plainText);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (token.content && typeof token.content !== 'string') {\n\t\t\t\twalkTokens(token.content);\n\t\t\t}\n\t\t}\n\t};\n\n\tPrism.hooks.add('after-tokenize', function (env) {\n\t\tif (env.language !== 'jsx' && env.language !== 'tsx') {\n\t\t\treturn;\n\t\t}\n\t\twalkTokens(env.tokens);\n\t});\n\n}(Prism));\n"], "names": [], "mappings": "AAAC,CAAA,SAAU,MAAK;IAEf,IAAI,aAAa,OAAM,IAAI,CAAC,KAAK,CAAC,OAAM,SAAS,CAAC,UAAU;IAE5D,IAAI,QAAQ,+CAA+C,MAAM;IACjE,IAAI,SAAS,+CAA+C,MAAM;IAClE,IAAI,SAAS,uCAAuC,MAAM;IAE1D;;;EAGC,GACD,SAAS,GAAG,MAAM,EAAE,KAAK;QACxB,SAAS,OACP,OAAO,CAAC,QAAQ;YAAc,OAAO;QAAO,GAC5C,OAAO,CAAC,aAAa;YAAc,OAAO;QAAQ,GAClD,OAAO,CAAC,aAAa;YAAc,OAAO;QAAQ;QACpD,OAAO,OAAO,QAAQ;IACvB;IAEA,SAAS,GAAG,QAAQ,MAAM;IAG1B,OAAM,SAAS,CAAC,GAAG,GAAG,OAAM,SAAS,CAAC,MAAM,CAAC,UAAU;IACvD,OAAM,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,GACjC,wIAAwI,MAAM;IAG/I,OAAM,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG;IAChD,OAAM,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,GAAG;IACvD,OAAM,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,GAAG;IAC7D,OAAM,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU;IAEjE,OAAM,SAAS,CAAC,YAAY,CAAC,UAAU,aAAa;QACnD,UAAU;YACT,SAAS,GAAG,WAAW,MAAM;YAC7B,QAAQ,OAAM,SAAS,CAAC,GAAG;QAC5B;IACD,GAAG,OAAM,SAAS,CAAC,GAAG,CAAC,GAAG;IAE1B,OAAM,SAAS,CAAC,YAAY,CAAC,UAAU,gBAAgB;QACtD,UAAU;YACT,kCAAkC;YAClC,SAAS,GAAG,YAAY,MAAM;YAC9B,OAAO;YACP,QAAQ;gBACP,sBAAsB;oBACrB,SAAS;oBACT,OAAO;gBACR;gBACA,MAAM,OAAM,SAAS,CAAC,GAAG;YAC1B;QACD;IACD,GAAG,OAAM,SAAS,CAAC,GAAG,CAAC,GAAG;IAE1B,mDAAmD;IACnD,IAAI,iBAAiB,SAAU,KAAK;QACnC,IAAI,CAAC,OAAO;YACX,OAAO;QACR;QACA,IAAI,OAAO,UAAU,UAAU;YAC9B,OAAO;QACR;QACA,IAAI,OAAO,MAAM,OAAO,KAAK,UAAU;YACtC,OAAO,MAAM,OAAO;QACrB;QACA,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC;IAC/C;IAEA,IAAI,aAAa,SAAU,MAAM;QAChC,IAAI,aAAa,EAAE;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACvC,IAAI,QAAQ,MAAM,CAAC,EAAE;YACrB,IAAI,iBAAiB;YAErB,IAAI,OAAO,UAAU,UAAU;gBAC9B,IAAI,MAAM,IAAI,KAAK,SAAS,MAAM,OAAO,CAAC,EAAE,IAAI,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO;oBAChF,oCAAoC;oBAEpC,IAAI,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,KAAK,MAAM;wBACjD,cAAc;wBACd,IAAI,WAAW,MAAM,GAAG,KAAK,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,OAAO,KAAK,eAAe,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG;4BACvH,2BAA2B;4BAC3B,WAAW,GAAG;wBACf;oBACD,OAAO;wBACN,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,OAAO,KAAK,MAAM;wBAC7D,yBAAyB;wBAC1B,OAAO;4BACN,cAAc;4BACd,WAAW,IAAI,CAAC;gCACf,SAAS,eAAe,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;gCACnD,cAAc;4BACf;wBACD;oBACD;gBACD,OAAO,IAAI,WAAW,MAAM,GAAG,KAAK,MAAM,IAAI,KAAK,iBAAiB,MAAM,OAAO,KAAK,KAAK;oBAE1F,wDAAwD;oBACxD,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,YAAY;gBAE/C,OAAO,IAAI,WAAW,MAAM,GAAG,KAAK,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,YAAY,GAAG,KAAK,MAAM,IAAI,KAAK,iBAAiB,MAAM,OAAO,KAAK,KAAK;oBAEhJ,qDAAqD;oBACrD,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,YAAY;gBAE/C,OAAO;oBACN,iBAAiB;gBAClB;YACD;YACA,IAAI,kBAAkB,OAAO,UAAU,UAAU;gBAChD,IAAI,WAAW,MAAM,GAAG,KAAK,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,YAAY,KAAK,GAAG;oBAClF,0DAA0D;oBAC1D,8CAA8C;oBAC9C,IAAI,YAAY,eAAe;oBAE/B,oCAAoC;oBACpC,IAAI,IAAI,OAAO,MAAM,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,YAAY,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,YAAY,GAAG;wBACxG,aAAa,eAAe,MAAM,CAAC,IAAI,EAAE;wBACzC,OAAO,MAAM,CAAC,IAAI,GAAG;oBACtB;oBACA,IAAI,IAAI,KAAK,CAAC,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,YAAY,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,YAAY,GAAG;wBACxF,YAAY,eAAe,MAAM,CAAC,IAAI,EAAE,IAAI;wBAC5C,OAAO,MAAM,CAAC,IAAI,GAAG;wBACrB;oBACD;oBAEA,MAAM,CAAC,EAAE,GAAG,IAAI,OAAM,KAAK,CAAC,cAAc,WAAW,MAAM;gBAC5D;YACD;YAEA,IAAI,MAAM,OAAO,IAAI,OAAO,MAAM,OAAO,KAAK,UAAU;gBACvD,WAAW,MAAM,OAAO;YACzB;QACD;IACD;IAEA,OAAM,KAAK,CAAC,GAAG,CAAC,kBAAkB,SAAU,GAAG;QAC9C,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,OAAO;YACrD;QACD;QACA,WAAW,IAAI,MAAM;IACtB;AAED,CAAA,EAAE", "ignoreList": [0], "debugId": null}}]}
{
  "common": {
    "loading": "読み込み中...",
    "save": "保存",
    "cancel": "キャンセル",
    "delete": "削除",
    "edit": "編集",
    "view": "表示",
    "search": "検索",
    "filter": "フィルター",
    "upload": "アップロード",
    "download": "ダウンロード",
    "copy": "コピー",
    "share": "共有",
    "back": "戻る",
    "next": "次へ",
    "previous": "前へ",
    "home": "ホーム",
    "login": "ログイン",
    "logout": "ログアウト",
    "profile": "プロフィール",
    "settings": "設定",
    "admin": "管理",
    "dashboard": "ダッシュボード"
  },
  "navigation": {
    "home": "ホーム",
    "articles": "記事",
    "archive": "アーカイブ",
    "now": "今",
    "guestbook": "ゲストブック",
    "files": "ファイル",
    "profile": "プロフィール"
  },
  "auth": {
    "signIn": "ログイン",
    "signOut": "ログアウト",
    "signInWith": "{provider}でログイン",
    "welcomeBack": "おかえりなさい",
    "signInToAccess": "{siteName}にログイン",
    "authFailed": "認証に失敗しました",
    "authError": "認証中に問題が発生しました",
    "accessDenied": "アクセス拒否",
    "noPermission": "このページにアクセスする権限がありません",
    "needHelp": "ヘルプが必要ですか？",
    "contactAdmin": "管理者に連絡"
  },
  "articles": {
    "title": "記事一覧",
    "subtitle": "厳選された技術記事と洞察のコレクションをご覧ください",
    "searchPlaceholder": "記事を検索...",
    "allCategories": "すべてのカテゴリ",
    "allStatuses": "すべてのステータス",
    "published": "公開済み",
    "draft": "下書き",
    "archived": "アーカイブ済み",
    "noArticles": "記事がありません",
    "noArticlesFound": "条件に一致する記事が見つかりません",
    "notPublished": "まだ記事が公開されていません",
    "readingTime": "{minutes}分で読める",
    "viewCount": "{count}回表示",
    "likeCount": "{count}いいね",
    "aiSummary": "AI要約",
    "articleEnd": "記事終了",
    "tableOfContents": "目次",
    "readingProgress": "読書進捗",
    "shareArticle": "記事を共有",
    "copyLink": "リンクをコピー",
    "relatedArticles": "関連記事"
  },
  "dashboard": {
    "title": "ダッシュボード",
    "welcome": "おかえりなさい、{name}さん！コンテンツと設定を管理しましょう。",
    "createArticle": "記事を作成",
    "quickActions": "クイックアクション",
    "recentActivity": "最近のアクティビティ",
    "statistics": {
      "totalArticles": "総記事数",
      "publishedArticles": "公開済み",
      "draftArticles": "下書き",
      "totalViews": "総表示数",
      "totalUsers": "総ユーザー数"
    },
    "actions": {
      "createNew": "新しい記事を作成",
      "createNewDesc": "新しいブログ記事の執筆を開始",
      "manageArticles": "記事を管理",
      "manageArticlesDesc": "既存の記事を表示・編集",
      "fileManagement": "ファイル管理",
      "fileManagementDesc": "メディアファイルのアップロードと管理"
    }
  },
  "files": {
    "title": "ファイル管理",
    "subtitle": "メディアファイルのアップロード、管理、整理",
    "uploadFiles": "ファイルをアップロード",
    "uploading": "アップロード中...",
    "searchFiles": "ファイルを検索...",
    "allTypes": "すべてのタイプ",
    "images": "画像",
    "videos": "動画",
    "audio": "音声",
    "documents": "文書",
    "gridView": "グリッド表示",
    "listView": "リスト表示",
    "noFiles": "ファイルがありません",
    "noFilesFound": "条件に一致するファイルが見つかりません",
    "uploadFirst": "最初のファイルをアップロード",
    "public": "公開",
    "private": "非公開",
    "copyUrl": "URLをコピー",
    "fileSize": "ファイルサイズ",
    "uploadedAt": "アップロード日時",
    "statistics": {
      "totalFiles": "総ファイル数",
      "imageFiles": "画像ファイル",
      "documentFiles": "文書ファイル",
      "totalSize": "総サイズ"
    }
  },
  "editor": {
    "title": "新しい記事を作成",
    "editTitle": "記事を編集",
    "subtitle": "Markdownエディターを使用して記事コンテンツを作成",
    "articleInfo": "記事情報",
    "articleTitle": "タイトル",
    "titlePlaceholder": "記事のタイトルを入力...",
    "urlSlug": "URLスラッグ",
    "slugPlaceholder": "article-slug",
    "excerpt": "記事の抜粋",
    "excerptPlaceholder": "記事の抜粋を入力...",
    "aiGenerate": "AI生成",
    "generating": "生成中...",
    "content": "記事コンテンツ",
    "contentDesc": "Markdown記法を使用して記事コンテンツを書く",
    "contentPlaceholder": "記事コンテンツの執筆を開始...",
    "preview": "プレビュー",
    "edit": "編集",
    "saveDraft": "下書きを保存",
    "saving": "保存中...",
    "publish": "記事を公開",
    "publishSettings": "公開設定",
    "category": "カテゴリ",
    "selectCategory": "カテゴリを選択",
    "tags": "タグ",
    "coverImage": "カバー画像URL",
    "coverImagePlaceholder": "https://example.com/image.jpg",
    "statistics": {
      "characters": "文字数",
      "readingTime": "読書時間",
      "status": "ステータス"
    }
  },
  "profile": {
    "title": "プロフィール",
    "subtitle": "アカウント情報と設定を管理",
    "basicInfo": "基本情報",
    "basicInfoDesc": "アカウントの基本情報",
    "userId": "ユーザーID",
    "role": "役割",
    "permissions": "権限",
    "permissionsDesc": "システム内での権限レベル",
    "viewArticles": "記事を表示",
    "createArticles": "記事を作成",
    "deleteArticles": "記事を削除",
    "fileManagement": "ファイル管理",
    "systemAdmin": "システム管理",
    "allowed": "✓ 許可",
    "denied": "✗ 拒否",
    "roles": {
      "admin": "管理者",
      "collaborator": "協力者",
      "user": "ユーザー"
    }
  },
  "errors": {
    "notFound": "ページが見つかりません",
    "articleNotFound": "記事が見つかりません",
    "articleNotFoundDesc": "申し訳ございませんが、お探しの記事は存在しないか削除されています",
    "possibleReasons": "考えられる理由：",
    "wrongLink": "記事リンクが間違っている",
    "articleDeleted": "記事が削除されている",
    "articleNotPublished": "記事がまだ公開されていない",
    "backToArticles": "記事一覧に戻る",
    "backToHome": "ホームに戻る",
    "serverError": "サーバーエラー",
    "somethingWrong": "何かが間違っています",
    "tryAgain": "後でもう一度お試しください"
  },
  "footer": {
    "description": "Next.jsで構築されたモダンなブログシステム",
    "quickLinks": "クイックリンク",
    "features": "機能",
    "other": "その他",
    "builtWith": "❤️を込めて、Next.js、Tailwind CSS、Shadcn UIで構築",
    "allRightsReserved": "すべての権利を保有"
  },
  "theme": {
    "toggle": "テーマを切り替え",
    "light": "ライトモード",
    "dark": "ダークモード",
    "system": "システム"
  },
  "admin": {
    "loading": "読み込み中...",
    "accessDenied": "アクセス拒否",
    "loginRequired": "管理パネルにアクセスするにはサインインしてください",
    "signIn": "サインイン",
    "insufficientPermissions": "権限不足",
    "adminAccessRequired": "このページにアクセスするには管理者または協力者の権限が必要です",
    "backToHome": "ホームに戻る",
    "collaboratorNotice": "現在協力者としてサインインしています。一部の機能が制限される場合があります。",
    "sidebar": {
      "title": "管理パネル",
      "dashboard": "ダッシュボード",
      "content": "コンテンツ管理",
      "articles": "記事",
      "pages": "ページ",
      "categories": "カテゴリ",
      "tags": "タグ",
      "media": "メディアファイル",
      "users": "ユーザー管理",
      "comments": "コメント管理",
      "links": "友達リンク",
      "analytics": "分析",
      "settings": "システム設定",
      "version": "バージョン",
      "lastUpdate": "最終更新"
    },
    "header": {
      "searchPlaceholder": "検索...",
      "unknownUser": "不明なユーザー",
      "admin": "管理者",
      "collaborator": "協力者",
      "profile": "プロフィール",
      "settings": "設定",
      "signOut": "サインアウト"
    },
    "categoryTag": {
      "title": "カテゴリ・タグ管理",
      "subtitle": "ブログのカテゴリとタグを管理してコンテンツ構造を整理",
      "loading": "読み込み中...",
      "categories": "カテゴリ",
      "tags": "タグ",
      "createCategory": "カテゴリを作成",
      "createTag": "タグを作成",
      "editCategory": "カテゴリを編集",
      "editTag": "タグを編集",
      "categoryDescription": "カテゴリは記事の主要なコンテンツタイプを整理するために使用されます",
      "tagDescription": "タグは記事の具体的なトピックとキーワードをマークするために使用されます",
      "searchPlaceholder": "カテゴリまたはタグを検索...",
      "name": "名前",
      "slug": "URLスラッグ",
      "description": "説明",
      "color": "色",
      "articleCount": "記事数",
      "updatedAt": "更新日時",
      "namePlaceholder": "名前を入力",
      "slugPlaceholder": "url-slug",
      "descriptionPlaceholder": "説明を入力（オプション）",
      "edit": "編集",
      "delete": "削除",
      "cancel": "キャンセル",
      "create": "作成",
      "update": "更新",
      "confirmDelete": "このアイテムを削除してもよろしいですか？この操作は元に戻せません。"
    }
  },
  "comments": {
    "title": "コメント",
    "loading": "コメントを読み込み中...",
    "configRequired": "コメント機能にはGiscus環境変数の設定が必要です",
    "configInstructions": ".env.localで設定してください：",
    "noComments": "まだコメントがありません",
    "writeComment": "コメントを書く",
    "reply": "返信",
    "edit": "編集",
    "delete": "削除",
    "reactions": "リアクション",
    "signInToComment": "ディスカッションに参加するにはサインインしてください"
  },
  "analytics": {
    "title": "分析",
    "subtitle": "ウェブサイトのトラフィック統計とコンテンツパフォーマンスを表示",
    "loading": "読み込み中...",
    "noData": "データがありません",
    "periods": {
      "7d": "過去7日間",
      "30d": "過去30日間",
      "90d": "過去90日間",
      "1y": "過去1年間"
    },
    "metrics": {
      "totalViews": "総ビュー数",
      "uniqueVisitors": "ユニーク訪問者",
      "articlesViewed": "閲覧された記事",
      "countries": "国",
      "views": "ビュー",
      "visitors": "訪問者"
    },
    "charts": {
      "viewsTrend": "ビュー数トレンド",
      "viewsTrendDesc": "時間経過によるウェブサイトトラフィック",
      "topArticles": "人気記事",
      "topArticlesDesc": "最も閲覧された記事",
      "geography": "地理的分布",
      "geographyDesc": "訪問者の地理的分布"
    }
  },
  "dashboard": {
    "stats": {
      "articles": "記事",
      "totalViews": "総ビュー数",
      "users": "ユーザー",
      "files": "ファイル",
      "published": "公開済み",
      "draft": "下書き",
      "admins": "管理者",
      "collaborators": "協力者",
      "images": "画像",
      "uniqueVisitors": "ユニーク訪問者",
      "contentStats": "コンテンツ統計",
      "contentStatsDesc": "記事の公開と管理状況",
      "viewStats": "ビュー統計",
      "viewStatsDesc": "ウェブサイトのビューと訪問者データ",
      "publishedArticles": "公開済み記事",
      "articlesViewed": "閲覧された記事",
      "recentArticles": "最近の記事",
      "recentViews": "最近のビュー",
      "popularArticles": "人気記事",
      "popularArticlesDesc": "最も閲覧された記事",
      "views": "ビュー",
      "noData": "データがありません"
    }
  },
  "toast": {
    "success": "成功",
    "error": "エラー",
    "warning": "警告",
    "info": "情報",
    "loading": "読み込み中..."
  },
  "api": {
    "loading": "リクエスト処理中...",
    "success": "リクエスト成功",
    "error": "リクエスト失敗"
  },
  "form": {
    "submitSuccess": "送信成功",
    "submitError": "送信失敗",
    "validationError": "フォーム検証失敗"
  },
  "fileUpload": {
    "uploading": "アップロード中",
    "uploadSuccess": "アップロード成功",
    "uploadError": "アップロード失敗"
  },
  "copy": {
    "success": "コピー成功",
    "error": "コピー失敗"
  },
  "search": {
    "title": "検索",
    "placeholder": "記事を検索...",
    "inputPlaceholder": "記事、タグ、キーワードを検索...",
    "search": "検索",
    "searchFailed": "検索に失敗しました",
    "noResults": "結果が見つかりません",
    "noResultsDesc": "\"{query}\" に関連するコンテンツが見つかりません",
    "noResultsDescription": "\"{query}\" に関連するコンテンツが見つかりません。他のキーワードをお試しください",
    "results": "件の結果",
    "resultsFor": "検索結果：{query}",
    "resultsTitle": "検索：{query}",
    "resultsDescription": "\"{query}\" に関連するコンテンツを検索",
    "resultsCount": "\"{query}\" に関連する {count} 件の結果が見つかりました",
    "viewAllResults": "\"{query}\" のすべての検索結果を表示",
    "recentSearches": "最近の検索",
    "suggestions": "検索候補",
    "clearHistory": "履歴をクリア",
    "filters": "フィルター",
    "activeFilters": "アクティブなフィルター",
    "clearAll": "すべてクリア",
    "contentType": "コンテンツタイプ",
    "allTypes": "すべてのタイプ",
    "articles": "記事",
    "tags": "タグ",
    "categories": "カテゴリー",
    "users": "ユーザー",
    "category": "カテゴリー",
    "tag": "タグ",
    "author": "著者",
    "date": "日付",
    "type": "タイプ",
    "selectCategory": "カテゴリーを選択",
    "allCategories": "すべてのカテゴリー",
    "selectTag": "タグを選択",
    "allTags": "すべてのタグ",
    "selectAuthor": "著者を選択",
    "allAuthors": "すべての著者",
    "dateRange": "日付範囲",
    "selectDateRange": "日付範囲を選択",
    "allTime": "すべての期間",
    "today": "今日",
    "thisWeek": "今週",
    "thisMonth": "今月",
    "thisYear": "今年",
    "page": "ページ",
    "previousPage": "前へ",
    "nextPage": "次へ",
    "moreTags": "個のタグ",
    "startSearching": "検索を開始",
    "startSearchingDescription": "キーワードを入力して記事、タグ、その他のコンテンツを検索",
    "searchTips": "検索のコツ",
    "tip1": "引用符を使用して正確なフレーズを検索",
    "tip2": "+ を使用して必須の単語を含める",
    "tip3": "- を使用して特定の単語を除外",
    "tip4": "タグとカテゴリーを使用して結果をフィルター",
    "searchSuggestions": "検索の提案",
    "suggestion1": "スペルが正しいか確認してください",
    "suggestion2": "より一般的なキーワードを試してください",
    "suggestion3": "異なるキーワードの組み合わせを使用してください",
    "searchDescription": "\"{query}\" に関連するコンテンツを検索"
  },
  "editor": {
    "bold": "太字",
    "italic": "斜体",
    "strikethrough": "取り消し線",
    "heading1": "見出し1",
    "heading2": "見出し2",
    "heading3": "見出し3",
    "bulletList": "箇条書きリスト",
    "numberedList": "番号付きリスト",
    "quote": "引用",
    "link": "リンク",
    "image": "画像",
    "code": "コード",
    "undo": "元に戻す",
    "redo": "やり直し",
    "search": "検索",
    "edit": "編集",
    "split": "分割",
    "previewMode": "プレビューモード",
    "editMode": "編集モード",
    "splitView": "分割表示",
    "settings": "設定",
    "fullscreen": "全画面",
    "exitFullscreen": "全画面を終了",
    "save": "保存",
    "words": "単語数",
    "characters": "文字数",
    "lines": "行数",
    "readingTime": "読了時間",
    "minutes": "分",
    "lastSaved": "最後の保存",
    "autoSave": "自動保存",
    "searchAndReplace": "検索と置換",
    "searchPlaceholder": "検索内容...",
    "replacePlaceholder": "置換後...",
    "findNext": "次を検索",
    "replaceAll": "すべて置換",
    "editorSettings": "エディター設定",
    "fontSize": "フォントサイズ",
    "lineHeight": "行の高さ",
    "wordWrap": "ワードラップ",
    "spellCheck": "スペルチェック",
    "autoComplete": "自動補完"
  },
  "imageUpload": {
    "insertImage": "画像を挿入",
    "upload": "アップロード",
    "fromUrl": "URLから",
    "gallery": "ギャラリー",
    "dragDropOrClick": "ファイルをここにドラッグするかクリックしてアップロード",
    "supportedFormats": "対応形式",
    "maxFileSize": "最大ファイルサイズ",
    "invalidFileType": "対応していないファイル形式",
    "fileTooLarge": "ファイルサイズが {size}MB を超えています",
    "uploadFailed": "アップロードに失敗しました",
    "success": "成功",
    "error": "エラー",
    "imageUrl": "画像URL",
    "altText": "代替テキスト",
    "altTextPlaceholder": "画像の内容を説明...",
    "title": "タイトル",
    "optional": "任意",
    "titlePlaceholder": "画像のタイトル...",
    "insertFromUrl": "URLから挿入",
    "noImagesYet": "まだ画像がありません",
    "uploadFirstImage": "最初の画像をアップロード",
    "insert": "挿入",
    "imageDetails": "画像の詳細",
    "filename": "ファイル名",
    "dimensions": "サイズ",
    "fileSize": "ファイルサイズ",
    "url": "URL",
    "copyUrl": "URLをコピー",
    "urlCopied": "URLをコピーしました",
    "copyFailed": "コピーに失敗しました",
    "pleaseEnterUrl": "画像URLを入力してください",
    "image": "画像"
  },
  "versionControl": {
    "versionHistory": "バージョン履歴",
    "versions": "個のバージョン",
    "compare": "比較",
    "createVersion": "バージョンを作成",
    "createNewVersion": "新しいバージョンを作成",
    "versionSummary": "バージョンの概要",
    "versionSummaryPlaceholder": "このバージョンの変更点を説明...",
    "version": "バージョン",
    "author": "作成者",
    "date": "日付",
    "words": "単語数",
    "status": "ステータス",
    "all": "すべて",
    "allAuthors": "すべての作成者",
    "draft": "下書き",
    "published": "公開済み",
    "archived": "アーカイブ済み",
    "noVersions": "バージョンがありません",
    "noVersionsDescription": "まだバージョンが作成されていません",
    "restore": "復元",
    "preview": "プレビュー",
    "justNow": "たった今",
    "minutesAgo": "{count} 分前",
    "hoursAgo": "{count} 時間前",
    "daysAgo": "{count} 日前",
    "compareVersions": "バージョンを比較",
    "contentDifferences": "コンテンツの差分",
    "noChanges": "変更なし",
    "versionCreated": "バージョンが作成されました",
    "createVersionFailed": "バージョンの作成に失敗しました",
    "versionRestored": "バージョンが復元されました",
    "restoreVersionFailed": "バージョンの復元に失敗しました",
    "compareVersionsFailed": "バージョンの比較に失敗しました",
    "loadVersionsFailed": "バージョンの読み込みに失敗しました",
    "title": "タイトル",
    "contentPreview": "コンテンツプレビュー"
  },
  "draftManager": {
    "draftManager": "下書き管理",
    "drafts": "個の下書き",
    "deleteSelected": "選択したものを削除",
    "saveDraft": "下書きを保存",
    "autoSave": "自動保存",
    "interval": "間隔",
    "enabled": "有効",
    "disabled": "無効",
    "searchDrafts": "下書きを検索...",
    "allStatuses": "すべてのステータス",
    "autoSaved": "自動保存",
    "manualSaved": "手動保存",
    "lastSaved": "最後の保存",
    "title": "タイトル",
    "wordCount": "単語数",
    "noDrafts": "下書きがありません",
    "noDraftsDescription": "まだ下書きが保存されていません",
    "untitled": "無題",
    "recoverable": "復旧可能",
    "loadDraft": "下書きを読み込み",
    "restoreDraft": "下書きを復元",
    "previewDraft": "下書きをプレビュー",
    "deleteDraft": "下書きを削除",
    "draftSaved": "下書きが保存されました",
    "saveDraftFailed": "下書きの保存に失敗しました",
    "draftDeleted": "下書きが削除されました",
    "deleteDraftFailed": "下書きの削除に失敗しました",
    "draftsDeleted": "{count} 個の下書きを削除しました",
    "deleteDraftsFailed": "下書きの削除に失敗しました",
    "draftRestored": "下書きが復元されました",
    "loadDraftsFailed": "下書きの読み込みに失敗しました"
  },
  "publishScheduler": {
    "publishScheduler": "公開スケジューラー",
    "scheduled": "スケジュール済み",
    "schedulePost": "投稿をスケジュール",
    "editSchedule": "スケジュールを編集",
    "noScheduledPosts": "スケジュールされた投稿がありません",
    "noScheduledPostsDescription": "まだ公開がスケジュールされた投稿がありません",
    "publishDateTime": "公開日時",
    "visibility": "公開範囲",
    "public": "公開",
    "unlisted": "非公開",
    "private": "プライベート",
    "basic": "基本設定",
    "seo": "SEO",
    "social": "ソーシャル",
    "titlePlaceholder": "記事のタイトル...",
    "seoTitle": "SEOタイトル",
    "seoTitlePlaceholder": "検索エンジンに表示されるタイトル...",
    "seoDescription": "SEO説明",
    "seoDescriptionPlaceholder": "検索エンジンに表示される説明...",
    "socialMediaSharing": "ソーシャルメディア共有",
    "shareToTwitter": "Twitterに共有",
    "shareToFacebook": "Facebookに共有",
    "shareToLinkedIn": "LinkedInに共有",
    "notifications": "通知設定",
    "emailNotifications": "メール通知",
    "pushNotifications": "プッシュ通知",
    "updateSchedule": "スケジュールを更新",
    "publishNow": "今すぐ公開",
    "cancelSchedule": "スケジュールをキャンセル",
    "delete": "削除",
    "preview": "プレビュー",
    "socialMediaEnabled": "ソーシャルメディアが有効",
    "pleaseSelectDateTime": "公開日時を選択してください",
    "scheduleDateMustBeFuture": "スケジュール日時は未来の時間である必要があります",
    "postScheduled": "投稿がスケジュールされました",
    "schedulePostFailed": "投稿のスケジュールに失敗しました",
    "scheduleUpdated": "スケジュールが更新されました",
    "updateScheduleFailed": "スケジュールの更新に失敗しました",
    "scheduleCancelled": "スケジュールがキャンセルされました",
    "cancelScheduleFailed": "スケジュールのキャンセルに失敗しました",
    "postPublished": "投稿が公開されました",
    "publishNowFailed": "今すぐ公開に失敗しました",
    "loadScheduledPostsFailed": "スケジュールされた投稿の読み込みに失敗しました"
  },
  "contentWorkflow": {
    "contentWorkflow": "コンテンツワークフロー",
    "pending": "保留中",
    "submitForReview": "レビューに提出",
    "workflowSettings": "ワークフロー設定",
    "allStatuses": "すべてのステータス",
    "inReview": "レビュー中",
    "approved": "承認済み",
    "rejected": "却下",
    "published": "公開済み",
    "allAssignees": "すべての担当者",
    "assignedToMe": "自分に割り当て",
    "unassigned": "未割り当て",
    "noWorkflowItems": "ワークフローアイテムがありません",
    "noWorkflowItemsDescription": "まだレビューに提出されたコンテンツがありません",
    "review": "レビュー",
    "viewDetails": "詳細を表示",
    "unassign": "割り当て解除",
    "approveItem": "アイテムを承認",
    "rejectItem": "アイテムを却下",
    "approve": "承認",
    "reject": "却下",
    "approvalComment": "承認コメント",
    "rejectionComment": "却下コメント",
    "approvalCommentPlaceholder": "承認コメントを追加（任意）...",
    "rejectionCommentPlaceholder": "却下理由を説明してください...",
    "cancel": "キャンセル",
    "content": "コンテンツ",
    "comments": "コメント",
    "history": "履歴",
    "workflowHistoryPlaceholder": "ワークフロー履歴がここに表示されます",
    "workflowSettingsDescription": "コンテンツレビューワークフローを設定",
    "submittedForReview": "レビューに提出されました",
    "submitForReviewFailed": "レビューへの提出に失敗しました",
    "itemApproved": "アイテムが承認されました",
    "itemRejected": "アイテムが却下されました",
    "rejectionCommentRequired": "却下コメントは必須です",
    "reviewFailed": "レビューに失敗しました",
    "itemAssigned": "アイテムが割り当てられました",
    "assignFailed": "割り当てに失敗しました",
    "loadWorkflowItemsFailed": "ワークフローアイテムの読み込みに失敗しました",
    "low": "低",
    "medium": "中",
    "high": "高",
    "urgent": "緊急"
  },
  "articleEditor": {
    "editArticle": "記事を編集",
    "newArticle": "新しい記事",
    "unsavedChanges": "未保存の変更",
    "draft": "下書き",
    "published": "公開済み",
    "scheduled": "スケジュール済み",
    "lastSaved": "最後の保存",
    "exitPreview": "プレビューを終了",
    "preview": "プレビュー",
    "save": "保存",
    "publish": "公開",
    "submitForReview": "レビューに提出",
    "titlePlaceholder": "記事のタイトルを入力...",
    "selectCategory": "カテゴリーを選択",
    "technology": "テクノロジー",
    "design": "デザイン",
    "business": "ビジネス",
    "lifestyle": "ライフスタイル",
    "addTag": "タグを追加",
    "contentPlaceholder": "書き始める...",
    "untitled": "無題",
    "noContent": "コンテンツなし",
    "tools": "ツール",
    "articleSaved": "記事が保存されました",
    "saveArticleFailed": "記事の保存に失敗しました",
    "articlePublished": "記事が公開されました",
    "publishArticleFailed": "記事の公開に失敗しました",
    "submittedForReview": "レビューに提出されました",
    "submitForReviewFailed": "レビューへの提出に失敗しました"
  }
  "accessibility": {
    "skipToMain": "メインコンテンツにスキップ",
    "skipToNavigation": "ナビゲーションにスキップ",
    "skipToFooter": "フッターにスキップ",
    "skipLinks": "スキップリンク",
    "shortcuts": {
      "title": "キーボードショートカット",
      "description": "以下のショートカットを使用して素早くナビゲートし操作できます",
      "showHelp": "キーボードショートカットヘルプを表示",
      "search": "検索",
      "home": "ホームに移動",
      "articles": "記事一覧",
      "dashboard": "管理ダッシュボード",
      "help": "ヘルプを表示",
      "close": "閉じる",
      "categories": {
        "navigation": "ナビゲーション",
        "general": "一般",
        "help": "ヘルプ"
      }
    }
  },
  "rss": {
    "title": "RSS購読",
    "subtitle": "RSSリーダーで最新コンテンツを購読",
    "subscribe": "購読",
    "copied": "{type}リンクをコピーしました",
    "copyFailed": "コピーに失敗しました",
    "filtered": "フィルター",
    "rssDescription": "標準のRSS 2.0形式",
    "atomDescription": "モダンなAtom 1.0形式",
    "jsonDescription": "軽量なJSON Feed形式",
    "howToUse": "RSSの使い方",
    "readers": {
      "title": "RSSリーダー",
      "description": "最高の体験のために専門のRSSリーダーを使用することをお勧めします"
    },
    "browsers": {
      "title": "ブラウザサポート",
      "description": "モダンブラウザはRSSリンクを直接開いてコンテンツをプレビューできます"
    },
    "apps": {
      "title": "モバイルアプリ",
      "description": "スマートフォンでRSSアプリを使用していつでもどこでも更新を読むことができます"
    }
  },
  "friendLinks": {
    "title": "友達リンク",
    "description": "優秀なウェブサイトとブログの推薦",
    "loading": "読み込み中...",
    "featured": "おすすめリンク",
    "allLinks": "すべてのリンク",
    "visit": "訪問",
    "categories": {
      "tech": "技術",
      "blog": "ブログ",
      "friend": "友達",
      "other": "その他"
    },
    "applyTitle": "友達リンク申請",
    "applyDescription": "質の高いウェブサイトの友達リンク申請を歓迎します。一緒により良いウェブエコシステムを構築しましょう",
    "requirements": {
      "title": "申請要件",
      "content": "健全なウェブサイトコンテンツ、違法または不適切な情報なし",
      "update": "ウェブサイトが定期的に更新されている、長期間非アクティブでない",
      "friendly": "ユーザーフレンドリーなウェブサイトデザインと良好なユーザーエクスペリエンス",
      "https": "HTTPS アクセスをサポート"
    },
    "applyNow": "今すぐ申請"
  },
  "meta": {
    "defaultTitle": "モダンブログシステム",
    "defaultDescription": "AI要約、多言語サポート、ダークモードなどの機能を備えたNext.js製モダンブログシステム"
  }
}
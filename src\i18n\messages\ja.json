{
  "common": {
    "loading": "読み込み中...",
    "save": "保存",
    "cancel": "キャンセル",
    "delete": "削除",
    "edit": "編集",
    "view": "表示",
    "search": "検索",
    "filter": "フィルター",
    "upload": "アップロード",
    "download": "ダウンロード",
    "copy": "コピー",
    "share": "共有",
    "back": "戻る",
    "next": "次へ",
    "previous": "前へ",
    "home": "ホーム",
    "login": "ログイン",
    "logout": "ログアウト",
    "profile": "プロフィール",
    "settings": "設定",
    "admin": "管理",
    "dashboard": "ダッシュボード"
  },
  "navigation": {
    "home": "ホーム",
    "articles": "記事",
    "archive": "アーカイブ",
    "now": "今",
    "guestbook": "ゲストブック",
    "files": "ファイル",
    "profile": "プロフィール"
  },
  "auth": {
    "signIn": "ログイン",
    "signOut": "ログアウト",
    "signInWith": "{provider}でログイン",
    "welcomeBack": "おかえりなさい",
    "signInToAccess": "{siteName}にログイン",
    "authFailed": "認証に失敗しました",
    "authError": "認証中に問題が発生しました",
    "accessDenied": "アクセス拒否",
    "noPermission": "このページにアクセスする権限がありません",
    "needHelp": "ヘルプが必要ですか？",
    "contactAdmin": "管理者に連絡"
  },
  "articles": {
    "title": "記事一覧",
    "subtitle": "厳選された技術記事と洞察のコレクションをご覧ください",
    "searchPlaceholder": "記事を検索...",
    "allCategories": "すべてのカテゴリ",
    "allStatuses": "すべてのステータス",
    "published": "公開済み",
    "draft": "下書き",
    "archived": "アーカイブ済み",
    "noArticles": "記事がありません",
    "noArticlesFound": "条件に一致する記事が見つかりません",
    "notPublished": "まだ記事が公開されていません",
    "readingTime": "{minutes}分で読める",
    "viewCount": "{count}回表示",
    "likeCount": "{count}いいね",
    "aiSummary": "AI要約",
    "articleEnd": "記事終了",
    "tableOfContents": "目次",
    "readingProgress": "読書進捗",
    "shareArticle": "記事を共有",
    "copyLink": "リンクをコピー",
    "relatedArticles": "関連記事"
  },
  "dashboard": {
    "title": "ダッシュボード",
    "welcome": "おかえりなさい、{name}さん！コンテンツと設定を管理しましょう。",
    "createArticle": "記事を作成",
    "quickActions": "クイックアクション",
    "recentActivity": "最近のアクティビティ",
    "statistics": {
      "totalArticles": "総記事数",
      "publishedArticles": "公開済み",
      "draftArticles": "下書き",
      "totalViews": "総表示数",
      "totalUsers": "総ユーザー数"
    },
    "actions": {
      "createNew": "新しい記事を作成",
      "createNewDesc": "新しいブログ記事の執筆を開始",
      "manageArticles": "記事を管理",
      "manageArticlesDesc": "既存の記事を表示・編集",
      "fileManagement": "ファイル管理",
      "fileManagementDesc": "メディアファイルのアップロードと管理"
    }
  },
  "files": {
    "title": "ファイル管理",
    "subtitle": "メディアファイルのアップロード、管理、整理",
    "uploadFiles": "ファイルをアップロード",
    "uploading": "アップロード中...",
    "searchFiles": "ファイルを検索...",
    "allTypes": "すべてのタイプ",
    "images": "画像",
    "videos": "動画",
    "audio": "音声",
    "documents": "文書",
    "gridView": "グリッド表示",
    "listView": "リスト表示",
    "noFiles": "ファイルがありません",
    "noFilesFound": "条件に一致するファイルが見つかりません",
    "uploadFirst": "最初のファイルをアップロード",
    "public": "公開",
    "private": "非公開",
    "copyUrl": "URLをコピー",
    "fileSize": "ファイルサイズ",
    "uploadedAt": "アップロード日時",
    "statistics": {
      "totalFiles": "総ファイル数",
      "imageFiles": "画像ファイル",
      "documentFiles": "文書ファイル",
      "totalSize": "総サイズ"
    }
  },
  "editor": {
    "title": "新しい記事を作成",
    "editTitle": "記事を編集",
    "subtitle": "Markdownエディターを使用して記事コンテンツを作成",
    "articleInfo": "記事情報",
    "articleTitle": "タイトル",
    "titlePlaceholder": "記事のタイトルを入力...",
    "urlSlug": "URLスラッグ",
    "slugPlaceholder": "article-slug",
    "excerpt": "記事の抜粋",
    "excerptPlaceholder": "記事の抜粋を入力...",
    "aiGenerate": "AI生成",
    "generating": "生成中...",
    "content": "記事コンテンツ",
    "contentDesc": "Markdown記法を使用して記事コンテンツを書く",
    "contentPlaceholder": "記事コンテンツの執筆を開始...",
    "preview": "プレビュー",
    "edit": "編集",
    "saveDraft": "下書きを保存",
    "saving": "保存中...",
    "publish": "記事を公開",
    "publishSettings": "公開設定",
    "category": "カテゴリ",
    "selectCategory": "カテゴリを選択",
    "tags": "タグ",
    "coverImage": "カバー画像URL",
    "coverImagePlaceholder": "https://example.com/image.jpg",
    "statistics": {
      "characters": "文字数",
      "readingTime": "読書時間",
      "status": "ステータス"
    }
  },
  "profile": {
    "title": "プロフィール",
    "subtitle": "アカウント情報と設定を管理",
    "basicInfo": "基本情報",
    "basicInfoDesc": "アカウントの基本情報",
    "userId": "ユーザーID",
    "role": "役割",
    "permissions": "権限",
    "permissionsDesc": "システム内での権限レベル",
    "viewArticles": "記事を表示",
    "createArticles": "記事を作成",
    "deleteArticles": "記事を削除",
    "fileManagement": "ファイル管理",
    "systemAdmin": "システム管理",
    "allowed": "✓ 許可",
    "denied": "✗ 拒否",
    "roles": {
      "admin": "管理者",
      "collaborator": "協力者",
      "user": "ユーザー"
    }
  },
  "errors": {
    "notFound": "ページが見つかりません",
    "articleNotFound": "記事が見つかりません",
    "articleNotFoundDesc": "申し訳ございませんが、お探しの記事は存在しないか削除されています",
    "possibleReasons": "考えられる理由：",
    "wrongLink": "記事リンクが間違っている",
    "articleDeleted": "記事が削除されている",
    "articleNotPublished": "記事がまだ公開されていない",
    "backToArticles": "記事一覧に戻る",
    "backToHome": "ホームに戻る",
    "serverError": "サーバーエラー",
    "somethingWrong": "何かが間違っています",
    "tryAgain": "後でもう一度お試しください"
  },
  "footer": {
    "description": "Next.jsで構築されたモダンなブログシステム",
    "quickLinks": "クイックリンク",
    "features": "機能",
    "other": "その他",
    "builtWith": "❤️を込めて、Next.js、Tailwind CSS、Shadcn UIで構築",
    "allRightsReserved": "すべての権利を保有"
  },
  "theme": {
    "toggle": "テーマを切り替え",
    "light": "ライトモード",
    "dark": "ダークモード",
    "system": "システム"
  },
  "admin": {
    "loading": "読み込み中...",
    "accessDenied": "アクセス拒否",
    "loginRequired": "管理パネルにアクセスするにはサインインしてください",
    "signIn": "サインイン",
    "insufficientPermissions": "権限不足",
    "adminAccessRequired": "このページにアクセスするには管理者または協力者の権限が必要です",
    "backToHome": "ホームに戻る",
    "collaboratorNotice": "現在協力者としてサインインしています。一部の機能が制限される場合があります。",
    "sidebar": {
      "title": "管理パネル",
      "dashboard": "ダッシュボード",
      "content": "コンテンツ管理",
      "articles": "記事",
      "pages": "ページ",
      "categories": "カテゴリ",
      "tags": "タグ",
      "media": "メディアファイル",
      "users": "ユーザー管理",
      "comments": "コメント管理",
      "links": "友達リンク",
      "analytics": "分析",
      "settings": "システム設定",
      "version": "バージョン",
      "lastUpdate": "最終更新"
    },
    "header": {
      "searchPlaceholder": "検索...",
      "unknownUser": "不明なユーザー",
      "admin": "管理者",
      "collaborator": "協力者",
      "profile": "プロフィール",
      "settings": "設定",
      "signOut": "サインアウト"
    },
    "categoryTag": {
      "title": "カテゴリ・タグ管理",
      "subtitle": "ブログのカテゴリとタグを管理してコンテンツ構造を整理",
      "loading": "読み込み中...",
      "categories": "カテゴリ",
      "tags": "タグ",
      "createCategory": "カテゴリを作成",
      "createTag": "タグを作成",
      "editCategory": "カテゴリを編集",
      "editTag": "タグを編集",
      "categoryDescription": "カテゴリは記事の主要なコンテンツタイプを整理するために使用されます",
      "tagDescription": "タグは記事の具体的なトピックとキーワードをマークするために使用されます",
      "searchPlaceholder": "カテゴリまたはタグを検索...",
      "name": "名前",
      "slug": "URLスラッグ",
      "description": "説明",
      "color": "色",
      "articleCount": "記事数",
      "updatedAt": "更新日時",
      "namePlaceholder": "名前を入力",
      "slugPlaceholder": "url-slug",
      "descriptionPlaceholder": "説明を入力（オプション）",
      "edit": "編集",
      "delete": "削除",
      "cancel": "キャンセル",
      "create": "作成",
      "update": "更新",
      "confirmDelete": "このアイテムを削除してもよろしいですか？この操作は元に戻せません。"
    }
  },
  "comments": {
    "title": "コメント",
    "loading": "コメントを読み込み中...",
    "configRequired": "コメント機能にはGiscus環境変数の設定が必要です",
    "configInstructions": ".env.localで設定してください：",
    "noComments": "まだコメントがありません",
    "writeComment": "コメントを書く",
    "reply": "返信",
    "edit": "編集",
    "delete": "削除",
    "reactions": "リアクション",
    "signInToComment": "ディスカッションに参加するにはサインインしてください"
  },
  "analytics": {
    "title": "分析",
    "subtitle": "ウェブサイトのトラフィック統計とコンテンツパフォーマンスを表示",
    "loading": "読み込み中...",
    "noData": "データがありません",
    "periods": {
      "7d": "過去7日間",
      "30d": "過去30日間",
      "90d": "過去90日間",
      "1y": "過去1年間"
    },
    "metrics": {
      "totalViews": "総ビュー数",
      "uniqueVisitors": "ユニーク訪問者",
      "articlesViewed": "閲覧された記事",
      "countries": "国",
      "views": "ビュー",
      "visitors": "訪問者"
    },
    "charts": {
      "viewsTrend": "ビュー数トレンド",
      "viewsTrendDesc": "時間経過によるウェブサイトトラフィック",
      "topArticles": "人気記事",
      "topArticlesDesc": "最も閲覧された記事",
      "geography": "地理的分布",
      "geographyDesc": "訪問者の地理的分布"
    }
  },
  "dashboard": {
    "stats": {
      "articles": "記事",
      "totalViews": "総ビュー数",
      "users": "ユーザー",
      "files": "ファイル",
      "published": "公開済み",
      "draft": "下書き",
      "admins": "管理者",
      "collaborators": "協力者",
      "images": "画像",
      "uniqueVisitors": "ユニーク訪問者",
      "contentStats": "コンテンツ統計",
      "contentStatsDesc": "記事の公開と管理状況",
      "viewStats": "ビュー統計",
      "viewStatsDesc": "ウェブサイトのビューと訪問者データ",
      "publishedArticles": "公開済み記事",
      "articlesViewed": "閲覧された記事",
      "recentArticles": "最近の記事",
      "recentViews": "最近のビュー",
      "popularArticles": "人気記事",
      "popularArticlesDesc": "最も閲覧された記事",
      "views": "ビュー",
      "noData": "データがありません"
    }
  },
  "toast": {
    "success": "成功",
    "error": "エラー",
    "warning": "警告",
    "info": "情報",
    "loading": "読み込み中..."
  },
  "api": {
    "loading": "リクエスト処理中...",
    "success": "リクエスト成功",
    "error": "リクエスト失敗"
  },
  "form": {
    "submitSuccess": "送信成功",
    "submitError": "送信失敗",
    "validationError": "フォーム検証失敗"
  },
  "fileUpload": {
    "uploading": "アップロード中",
    "uploadSuccess": "アップロード成功",
    "uploadError": "アップロード失敗"
  },
  "copy": {
    "success": "コピー成功",
    "error": "コピー失敗"
  },
  "search": {
    "title": "検索",
    "placeholder": "記事を検索...",
    "inputPlaceholder": "記事、タグ、キーワードを検索...",
    "search": "検索",
    "searchFailed": "検索に失敗しました",
    "noResults": "結果が見つかりません",
    "noResultsDesc": "\"{query}\" に関連するコンテンツが見つかりません",
    "noResultsDescription": "\"{query}\" に関連するコンテンツが見つかりません。他のキーワードをお試しください",
    "results": "件の結果",
    "resultsFor": "検索結果：{query}",
    "resultsTitle": "検索：{query}",
    "resultsDescription": "\"{query}\" に関連するコンテンツを検索",
    "resultsCount": "\"{query}\" に関連する {count} 件の結果が見つかりました",
    "viewAllResults": "\"{query}\" のすべての検索結果を表示",
    "recentSearches": "最近の検索",
    "suggestions": "検索候補",
    "clearHistory": "履歴をクリア",
    "filters": "フィルター",
    "activeFilters": "アクティブなフィルター",
    "clearAll": "すべてクリア",
    "contentType": "コンテンツタイプ",
    "allTypes": "すべてのタイプ",
    "articles": "記事",
    "tags": "タグ",
    "categories": "カテゴリー",
    "users": "ユーザー",
    "category": "カテゴリー",
    "tag": "タグ",
    "author": "著者",
    "date": "日付",
    "type": "タイプ",
    "selectCategory": "カテゴリーを選択",
    "allCategories": "すべてのカテゴリー",
    "selectTag": "タグを選択",
    "allTags": "すべてのタグ",
    "selectAuthor": "著者を選択",
    "allAuthors": "すべての著者",
    "dateRange": "日付範囲",
    "selectDateRange": "日付範囲を選択",
    "allTime": "すべての期間",
    "today": "今日",
    "thisWeek": "今週",
    "thisMonth": "今月",
    "thisYear": "今年",
    "page": "ページ",
    "previousPage": "前へ",
    "nextPage": "次へ",
    "moreTags": "個のタグ",
    "startSearching": "検索を開始",
    "startSearchingDescription": "キーワードを入力して記事、タグ、その他のコンテンツを検索",
    "searchTips": "検索のコツ",
    "tip1": "引用符を使用して正確なフレーズを検索",
    "tip2": "+ を使用して必須の単語を含める",
    "tip3": "- を使用して特定の単語を除外",
    "tip4": "タグとカテゴリーを使用して結果をフィルター",
    "searchSuggestions": "検索の提案",
    "suggestion1": "スペルが正しいか確認してください",
    "suggestion2": "より一般的なキーワードを試してください",
    "suggestion3": "異なるキーワードの組み合わせを使用してください",
    "searchDescription": "\"{query}\" に関連するコンテンツを検索"
  },
  "editor": {
    "bold": "太字",
    "italic": "斜体",
    "strikethrough": "取り消し線",
    "heading1": "見出し1",
    "heading2": "見出し2",
    "heading3": "見出し3",
    "bulletList": "箇条書きリスト",
    "numberedList": "番号付きリスト",
    "quote": "引用",
    "link": "リンク",
    "image": "画像",
    "code": "コード",
    "undo": "元に戻す",
    "redo": "やり直し",
    "search": "検索",
    "edit": "編集",
    "split": "分割",
    "previewMode": "プレビューモード",
    "editMode": "編集モード",
    "splitView": "分割表示",
    "settings": "設定",
    "fullscreen": "全画面",
    "exitFullscreen": "全画面を終了",
    "save": "保存",
    "words": "単語数",
    "characters": "文字数",
    "lines": "行数",
    "readingTime": "読了時間",
    "minutes": "分",
    "lastSaved": "最後の保存",
    "autoSave": "自動保存",
    "searchAndReplace": "検索と置換",
    "searchPlaceholder": "検索内容...",
    "replacePlaceholder": "置換後...",
    "findNext": "次を検索",
    "replaceAll": "すべて置換",
    "editorSettings": "エディター設定",
    "fontSize": "フォントサイズ",
    "lineHeight": "行の高さ",
    "wordWrap": "ワードラップ",
    "spellCheck": "スペルチェック",
    "autoComplete": "自動補完"
  },
  "imageUpload": {
    "insertImage": "画像を挿入",
    "upload": "アップロード",
    "fromUrl": "URLから",
    "gallery": "ギャラリー",
    "dragDropOrClick": "ファイルをここにドラッグするかクリックしてアップロード",
    "supportedFormats": "対応形式",
    "maxFileSize": "最大ファイルサイズ",
    "invalidFileType": "対応していないファイル形式",
    "fileTooLarge": "ファイルサイズが {size}MB を超えています",
    "uploadFailed": "アップロードに失敗しました",
    "success": "成功",
    "error": "エラー",
    "imageUrl": "画像URL",
    "altText": "代替テキスト",
    "altTextPlaceholder": "画像の内容を説明...",
    "title": "タイトル",
    "optional": "任意",
    "titlePlaceholder": "画像のタイトル...",
    "insertFromUrl": "URLから挿入",
    "noImagesYet": "まだ画像がありません",
    "uploadFirstImage": "最初の画像をアップロード",
    "insert": "挿入",
    "imageDetails": "画像の詳細",
    "filename": "ファイル名",
    "dimensions": "サイズ",
    "fileSize": "ファイルサイズ",
    "url": "URL",
    "copyUrl": "URLをコピー",
    "urlCopied": "URLをコピーしました",
    "copyFailed": "コピーに失敗しました",
    "pleaseEnterUrl": "画像URLを入力してください",
    "image": "画像"
  },
  "versionControl": {
    "versionHistory": "バージョン履歴",
    "versions": "個のバージョン",
    "compare": "比較",
    "createVersion": "バージョンを作成",
    "createNewVersion": "新しいバージョンを作成",
    "versionSummary": "バージョンの概要",
    "versionSummaryPlaceholder": "このバージョンの変更点を説明...",
    "version": "バージョン",
    "author": "作成者",
    "date": "日付",
    "words": "単語数",
    "status": "ステータス",
    "all": "すべて",
    "allAuthors": "すべての作成者",
    "draft": "下書き",
    "published": "公開済み",
    "archived": "アーカイブ済み",
    "noVersions": "バージョンがありません",
    "noVersionsDescription": "まだバージョンが作成されていません",
    "restore": "復元",
    "preview": "プレビュー",
    "justNow": "たった今",
    "minutesAgo": "{count} 分前",
    "hoursAgo": "{count} 時間前",
    "daysAgo": "{count} 日前",
    "compareVersions": "バージョンを比較",
    "contentDifferences": "コンテンツの差分",
    "noChanges": "変更なし",
    "versionCreated": "バージョンが作成されました",
    "createVersionFailed": "バージョンの作成に失敗しました",
    "versionRestored": "バージョンが復元されました",
    "restoreVersionFailed": "バージョンの復元に失敗しました",
    "compareVersionsFailed": "バージョンの比較に失敗しました",
    "loadVersionsFailed": "バージョンの読み込みに失敗しました",
    "title": "タイトル",
    "contentPreview": "コンテンツプレビュー"
  },
  "draftManager": {
    "draftManager": "下書き管理",
    "drafts": "個の下書き",
    "deleteSelected": "選択したものを削除",
    "saveDraft": "下書きを保存",
    "autoSave": "自動保存",
    "interval": "間隔",
    "enabled": "有効",
    "disabled": "無効",
    "searchDrafts": "下書きを検索...",
    "allStatuses": "すべてのステータス",
    "autoSaved": "自動保存",
    "manualSaved": "手動保存",
    "lastSaved": "最後の保存",
    "title": "タイトル",
    "wordCount": "単語数",
    "noDrafts": "下書きがありません",
    "noDraftsDescription": "まだ下書きが保存されていません",
    "untitled": "無題",
    "recoverable": "復旧可能",
    "loadDraft": "下書きを読み込み",
    "restoreDraft": "下書きを復元",
    "previewDraft": "下書きをプレビュー",
    "deleteDraft": "下書きを削除",
    "draftSaved": "下書きが保存されました",
    "saveDraftFailed": "下書きの保存に失敗しました",
    "draftDeleted": "下書きが削除されました",
    "deleteDraftFailed": "下書きの削除に失敗しました",
    "draftsDeleted": "{count} 個の下書きを削除しました",
    "deleteDraftsFailed": "下書きの削除に失敗しました",
    "draftRestored": "下書きが復元されました",
    "loadDraftsFailed": "下書きの読み込みに失敗しました"
  },
  "publishScheduler": {
    "publishScheduler": "公開スケジューラー",
    "scheduled": "スケジュール済み",
    "schedulePost": "投稿をスケジュール",
    "editSchedule": "スケジュールを編集",
    "noScheduledPosts": "スケジュールされた投稿がありません",
    "noScheduledPostsDescription": "まだ公開がスケジュールされた投稿がありません",
    "publishDateTime": "公開日時",
    "visibility": "公開範囲",
    "public": "公開",
    "unlisted": "非公開",
    "private": "プライベート",
    "basic": "基本設定",
    "seo": "SEO",
    "social": "ソーシャル",
    "titlePlaceholder": "記事のタイトル...",
    "seoTitle": "SEOタイトル",
    "seoTitlePlaceholder": "検索エンジンに表示されるタイトル...",
    "seoDescription": "SEO説明",
    "seoDescriptionPlaceholder": "検索エンジンに表示される説明...",
    "socialMediaSharing": "ソーシャルメディア共有",
    "shareToTwitter": "Twitterに共有",
    "shareToFacebook": "Facebookに共有",
    "shareToLinkedIn": "LinkedInに共有",
    "notifications": "通知設定",
    "emailNotifications": "メール通知",
    "pushNotifications": "プッシュ通知",
    "updateSchedule": "スケジュールを更新",
    "publishNow": "今すぐ公開",
    "cancelSchedule": "スケジュールをキャンセル",
    "delete": "削除",
    "preview": "プレビュー",
    "socialMediaEnabled": "ソーシャルメディアが有効",
    "pleaseSelectDateTime": "公開日時を選択してください",
    "scheduleDateMustBeFuture": "スケジュール日時は未来の時間である必要があります",
    "postScheduled": "投稿がスケジュールされました",
    "schedulePostFailed": "投稿のスケジュールに失敗しました",
    "scheduleUpdated": "スケジュールが更新されました",
    "updateScheduleFailed": "スケジュールの更新に失敗しました",
    "scheduleCancelled": "スケジュールがキャンセルされました",
    "cancelScheduleFailed": "スケジュールのキャンセルに失敗しました",
    "postPublished": "投稿が公開されました",
    "publishNowFailed": "今すぐ公開に失敗しました",
    "loadScheduledPostsFailed": "スケジュールされた投稿の読み込みに失敗しました"
  },
  "contentWorkflow": {
    "contentWorkflow": "コンテンツワークフロー",
    "pending": "保留中",
    "submitForReview": "レビューに提出",
    "workflowSettings": "ワークフロー設定",
    "allStatuses": "すべてのステータス",
    "inReview": "レビュー中",
    "approved": "承認済み",
    "rejected": "却下",
    "published": "公開済み",
    "allAssignees": "すべての担当者",
    "assignedToMe": "自分に割り当て",
    "unassigned": "未割り当て",
    "noWorkflowItems": "ワークフローアイテムがありません",
    "noWorkflowItemsDescription": "まだレビューに提出されたコンテンツがありません",
    "review": "レビュー",
    "viewDetails": "詳細を表示",
    "unassign": "割り当て解除",
    "approveItem": "アイテムを承認",
    "rejectItem": "アイテムを却下",
    "approve": "承認",
    "reject": "却下",
    "approvalComment": "承認コメント",
    "rejectionComment": "却下コメント",
    "approvalCommentPlaceholder": "承認コメントを追加（任意）...",
    "rejectionCommentPlaceholder": "却下理由を説明してください...",
    "cancel": "キャンセル",
    "content": "コンテンツ",
    "comments": "コメント",
    "history": "履歴",
    "workflowHistoryPlaceholder": "ワークフロー履歴がここに表示されます",
    "workflowSettingsDescription": "コンテンツレビューワークフローを設定",
    "submittedForReview": "レビューに提出されました",
    "submitForReviewFailed": "レビューへの提出に失敗しました",
    "itemApproved": "アイテムが承認されました",
    "itemRejected": "アイテムが却下されました",
    "rejectionCommentRequired": "却下コメントは必須です",
    "reviewFailed": "レビューに失敗しました",
    "itemAssigned": "アイテムが割り当てられました",
    "assignFailed": "割り当てに失敗しました",
    "loadWorkflowItemsFailed": "ワークフローアイテムの読み込みに失敗しました",
    "low": "低",
    "medium": "中",
    "high": "高",
    "urgent": "緊急"
  },
  "articleEditor": {
    "editArticle": "記事を編集",
    "newArticle": "新しい記事",
    "unsavedChanges": "未保存の変更",
    "draft": "下書き",
    "published": "公開済み",
    "scheduled": "スケジュール済み",
    "lastSaved": "最後の保存",
    "exitPreview": "プレビューを終了",
    "preview": "プレビュー",
    "save": "保存",
    "publish": "公開",
    "submitForReview": "レビューに提出",
    "titlePlaceholder": "記事のタイトルを入力...",
    "selectCategory": "カテゴリーを選択",
    "technology": "テクノロジー",
    "design": "デザイン",
    "business": "ビジネス",
    "lifestyle": "ライフスタイル",
    "addTag": "タグを追加",
    "contentPlaceholder": "書き始める...",
    "untitled": "無題",
    "noContent": "コンテンツなし",
    "tools": "ツール",
    "articleSaved": "記事が保存されました",
    "saveArticleFailed": "記事の保存に失敗しました",
    "articlePublished": "記事が公開されました",
    "publishArticleFailed": "記事の公開に失敗しました",
    "submittedForReview": "レビューに提出されました",
    "submitForReviewFailed": "レビューへの提出に失敗しました"
  },
  "followSystem": {
    "follow": "フォロー",
    "following": "フォロー中",
    "followers": "フォロワー",
    "followBack": "フォローバック",
    "unfollow": "フォロー解除",
    "verified": "認証済み",
    "articles": "記事",
    "mutualFollowers": "相互フォロー",
    "lastActive": "最終アクティブ",
    "neverActive": "アクティブなし",
    "activeNow": "現在アクティブ",
    "activeHoursAgo": "{count} 時間前にアクティブ",
    "activeDaysAgo": "{count} 日前にアクティブ",
    "activeWeeksAgo": "数週間前にアクティブ",
    "suggestedUsers": "おすすめユーザー",
    "userActions": "ユーザーアクション",
    "blockUser": "ユーザーをブロック",
    "searchUsers": "ユーザーを検索",
    "noUsersFound": "ユーザーが見つかりません",
    "noUsers": "ユーザーなし",
    "enableNotifications": "通知を有効にする",
    "disableNotifications": "通知を無効にする",
    "followSuccess": "フォローしました",
    "followFailed": "フォローに失敗しました",
    "unfollowSuccess": "フォロー解除しました",
    "unfollowFailed": "フォロー解除に失敗しました",
    "blockSuccess": "ブロックしました",
    "blockFailed": "ブロックに失敗しました",
    "notificationsEnabled": "通知が有効になりました",
    "notificationsDisabled": "通知が無効になりました",
    "notificationToggleFailed": "通知設定に失敗しました"
  },
  "articleInteractions": {
    "likes": "いいね",
    "bookmarks": "ブックマーク",
    "share": "シェア",
    "comments": "コメント",
    "views": "表示",
    "shareArticle": "記事をシェア",
    "shareOn": "シェア先",
    "email": "メール",
    "copyLink": "リンクをコピー",
    "reportArticle": "記事を報告",
    "reportReason": "報告理由",
    "reportDescription": "詳細",
    "reportDescriptionPlaceholder": "報告理由を詳しく説明してください...",
    "cancel": "キャンセル",
    "submitReport": "報告を送信",
    "reportReason.spam": "スパム",
    "reportReason.harassment": "嫌がらせ",
    "reportReason.inappropriate": "不適切なコンテンツ",
    "reportReason.copyright": "著作権侵害",
    "reportReason.misinformation": "誤情報",
    "reportReason.other": "その他",
    "loginRequired": "ログインが必要です",
    "likeSuccess": "いいねしました",
    "unlikeSuccess": "いいねを取り消しました",
    "likeFailed": "いいねに失敗しました",
    "bookmarkSuccess": "ブックマークしました",
    "unbookmarkSuccess": "ブックマークを取り消しました",
    "bookmarkFailed": "ブックマークに失敗しました",
    "linkCopied": "リンクをコピーしました",
    "copyFailed": "コピーに失敗しました",
    "reportSuccess": "報告しました",
    "reportFailed": "報告に失敗しました",
    "reportReasonRequired": "理由を選択してください"
  },
  "activityTimeline": {
    "activityFeed": "アクティビティフィード",
    "allActivities": "すべてのアクティビティ",
    "articles": "記事",
    "interactions": "インタラクション",
    "follows": "フォロー",
    "comments": "コメント",
    "justNow": "たった今",
    "minutesAgo": "{count} 分前",
    "hoursAgo": "{count} 時間前",
    "daysAgo": "{count} 日前",
    "noMoreActivities": "これ以上のアクティビティはありません",
    "noActivities": "アクティビティなし",
    "noActivitiesDescription": "ユーザーをフォローしてアクティビティを見る",
    "loadActivitiesFailed": "アクティビティの読み込みに失敗しました",
    "activityDescription.articlePublished": "{user} が記事「{article}」を公開しました",
    "activityDescription.articleLiked": "{user} が記事「{article}」にいいねしました",
    "activityDescription.articleBookmarked": "{user} が記事「{article}」をブックマークしました",
    "activityDescription.articleShared": "{user} が記事「{article}」を {platform} でシェアしました",
    "activityDescription.userFollowed": "{user} が {target} をフォローしました",
    "activityDescription.commentPosted": "{user} が記事「{article}」にコメントしました",
    "activityDescription.articleUpdated": "{user} が記事「{article}」を更新しました",
    "activityDescription.milestoneReached": "{user} がマイルストーンを達成しました：{milestone} ({count})",
    "activityDescription.unknown": "不明なアクティビティ"
  },
  "messaging": {
    "messages": "メッセージ",
    "searchConversations": "会話を検索",
    "noConversationsFound": "会話が見つかりません",
    "noConversations": "会話なし",
    "selectConversation": "会話を選択",
    "selectConversationDescription": "会話を選択してチャットを開始",
    "online": "オンライン",
    "lastSeen": "最終ログイン：{time}",
    "typing": "入力中...",
    "typeMessage": "メッセージを入力...",
    "replyingTo": "返信先",
    "replyTo": "返信",
    "edited": "編集済み",
    "sendMessageFailed": "メッセージの送信に失敗しました",
    "uploadFailed": "アップロードに失敗しました",
    "noMessages": "メッセージなし"
  },
  "comments": {
    "comments": "コメント",
    "newest": "最新",
    "oldest": "最古",
    "popular": "人気",
    "controversial": "議論の多い",
    "write": "書く",
    "preview": "プレビュー",
    "writeComment": "コメントを書く...",
    "writeReply": "返信を書く...",
    "replyingTo": "返信先",
    "cancel": "キャンセル",
    "comment": "コメント",
    "reply": "返信",
    "share": "シェア",
    "edit": "編集",
    "delete": "削除",
    "pin": "ピン留め",
    "unpin": "ピン留め解除",
    "report": "報告",
    "pinned": "ピン留め済み",
    "edited": "編集済み",
    "justNow": "たった今",
    "minutesAgo": "{count} 分前",
    "hoursAgo": "{count} 時間前",
    "daysAgo": "{count} 日前",
    "markdownSupported": "Markdown対応",
    "mentionWithAt": "@ でユーザーをメンション",
    "nothingToPreview": "プレビューするものがありません",
    "noComments": "コメントなし",
    "beFirstToComment": "最初にコメントする",
    "commentActions": "コメントアクション",
    "loginRequired": "ログインが必要です",
    "loadCommentsFailed": "コメントの読み込みに失敗しました",
    "commentSubmitted": "コメントを投稿しました",
    "submitCommentFailed": "コメントの投稿に失敗しました",
    "likeCommentFailed": "コメントのいいねに失敗しました",
    "dislikeCommentFailed": "コメントの評価に失敗しました",
    "commentReported": "コメントを報告しました",
    "reportCommentFailed": "コメントの報告に失敗しました",
    "commentDeleted": "コメントを削除しました",
    "deleteCommentFailed": "コメントの削除に失敗しました",
    "commentUpdated": "コメントを更新しました",
    "editCommentFailed": "コメントの編集に失敗しました",
    "commentPinned": "コメントをピン留めしました",
    "pinCommentFailed": "コメントのピン留めに失敗しました"
  },
  "social": {
    "title": "ソーシャルハブ",
    "description": "他のユーザーと交流し、興味深いコンテンツクリエイターをフォロー",
    "followers": "フォロワー",
    "following": "フォロー中",
    "likes": "いいね",
    "bookmarks": "ブックマーク",
    "shares": "シェア",
    "comments": "コメント",
    "timeline": "タイムライン",
    "messages": "メッセージ",
    "trending": "トレンド",
    "activityFeed": "アクティビティフィード",
    "suggestedUsers": "おすすめユーザー",
    "followingManagement": "フォロー管理",
    "messaging": "メッセージング",
    "trendingActivities": "トレンドアクティビティ",
    "popularUsers": "人気ユーザー"
  },
  "notifications": {
    "notifications": "通知",
    "unread": "未読",
    "markAllRead": "すべて既読にする",
    "notificationSettings": "通知設定",
    "all": "すべて",
    "interactions": "インタラクション",
    "system": "システム",
    "noNotifications": "通知なし",
    "markAsRead": "既読にする",
    "delete": "削除",
    "justNow": "たった今",
    "minutesAgo": "{count} 分前",
    "hoursAgo": "{count} 時間前",
    "daysAgo": "{count} 日前",
    "notificationDeleted": "通知を削除しました",
    "deleteNotificationFailed": "通知の削除に失敗しました",
    "settingsUpdated": "設定を更新しました",
    "updateSettingsFailed": "設定の更新に失敗しました",
    "notificationTypes": "通知タイプ",
    "notificationMethods": "通知方法",
    "notificationFrequency": "通知頻度",
    "emailNotifications": "メール通知",
    "pushNotifications": "プッシュ通知",
    "immediate": "即座に",
    "hourly": "毎時",
    "daily": "毎日",
    "weekly": "毎週",
    "notificationType.likes": "いいね",
    "notificationType.comments": "コメント",
    "notificationType.follows": "フォロー",
    "notificationType.shares": "シェア",
    "notificationType.bookmarks": "ブックマーク",
    "notificationType.mentions": "メンション",
    "notificationType.system": "システム通知"
  },
  "analytics": {
    "title": "アナリティクス",
    "description": "ウェブサイトのパフォーマンスとユーザー行動の詳細な洞察",
    "analytics": "アナリティクス",
    "analyticsDescription": "包括的なウェブサイト分析と洞察",
    "dashboard": "ダッシュボード",
    "seoOptimization": "SEO最適化",
    "keywordResearch": "キーワード調査",
    "sitemap": "サイトマップ",
    "totalViews": "総ページビュー",
    "uniqueVisitors": "ユニークビジター",
    "avgSessionDuration": "平均セッション時間",
    "bounceRate": "直帰率",
    "fromLastPeriod": "前期比",
    "traffic": "トラフィック",
    "sources": "ソース",
    "content": "コンテンツ",
    "audience": "オーディエンス",
    "realtime": "リアルタイム",
    "trafficTrend": "トラフィック推移",
    "views": "ページビュー",
    "visitors": "ビジター",
    "sessions": "セッション",
    "trafficSources": "トラフィックソース",
    "deviceTypes": "デバイスタイプ",
    "topPages": "人気ページ",
    "avgTime": "平均時間",
    "geography": "地理的分布",
    "activeUsers": "アクティブユーザー",
    "currentlyOnline": "現在オンライン",
    "recentActivity": "最近のアクティビティ",
    "noData": "データなし",
    "noDataDescription": "分析結果を表示するのに十分なデータがありません",
    "loadDataFailed": "データの読み込みに失敗しました",
    "export": "エクスポート",
    "exportSuccess": "エクスポート成功",
    "exportFailed": "エクスポート失敗",
    "seoTips": "SEOのヒント",
    "tip1Title": "タイトルタグの最適化",
    "tip1Description": "各ページに独自で説明的なタイトルタグを設定する",
    "tip2Title": "ページ速度の改善",
    "tip2Description": "高速読み込みページはより良いユーザー体験を提供する",
    "tip3Title": "質の高いコンテンツの作成",
    "tip3Description": "定期的に価値のあるオリジナルコンテンツを公開する",
    "topCountries": "主要国"
  },
  "seo": {
    "seoAnalyzer": "SEOアナライザー",
    "targetUrl": "対象URL",
    "targetKeywords": "対象キーワード",
    "keywordsPlaceholder": "キーワード1, キーワード2, キーワード3",
    "analyzing": "分析中",
    "analyzeSEO": "SEO分析",
    "exportReport": "レポートエクスポート",
    "urlRequired": "URLを入力してください",
    "analysisComplete": "分析完了",
    "analysisFailed": "分析失敗",
    "suggestionApplied": "提案が適用されました",
    "applySuggestionFailed": "提案の適用に失敗しました",
    "reportExported": "レポートがエクスポートされました",
    "exportFailed": "エクスポート失敗",
    "seoScore": "SEOスコア",
    "overallPerformance": "全体的なパフォーマンス",
    "outOf100": "100点満点",
    "errors": "エラー",
    "warnings": "警告",
    "suggestions": "提案",
    "issues": "問題",
    "keywords": "キーワード",
    "metadata": "メタデータ",
    "performance": "パフォーマンス",
    "impact.high": "高影響",
    "impact.medium": "中影響",
    "impact.low": "低影響",
    "howToFix": "修正方法",
    "density": "密度",
    "position": "順位",
    "difficulty": "難易度",
    "volume": "検索ボリューム",
    "trend": "トレンド",
    "trend.up": "上昇",
    "trend.down": "下降",
    "trend.stable": "安定",
    "keywordSuggestions": "キーワード提案",
    "titleTag": "タイトルタグ",
    "metaDescription": "メタディスクリプション",
    "currentTitle": "現在のタイトル",
    "currentDescription": "現在の説明",
    "length": "長さ",
    "characters": "文字",
    "optimal": "最適",
    "needsImprovement": "改善が必要",
    "coreWebVitals": "コアウェブバイタル",
    "lcp": "最大コンテンツ描画",
    "fid": "初回入力遅延",
    "cls": "累積レイアウトシフト",
    "pagespeedScores": "PageSpeedスコア",
    "mobileScore": "モバイルスコア",
    "desktopScore": "デスクトップスコア",
    "implementation": "実装方法",
    "estimatedImpact": "推定影響",
    "apply": "適用",
    "priority.high": "高優先度",
    "priority.medium": "中優先度",
    "priority.low": "低優先度"
  },
  "sitemap": {
    "sitemapGenerator": "サイトマップジェネレーター",
    "sitemapDescription": "ウェブサイトのサイトマップを自動生成・管理",
    "generating": "生成中",
    "generateNow": "今すぐ生成",
    "submitToSearchEngines": "検索エンジンに送信",
    "totalUrls": "総URL数",
    "fileSize": "ファイルサイズ",
    "lastGenerated": "最終生成日",
    "status": "ステータス",
    "status.generating": "生成中",
    "status.success": "成功",
    "status.error": "エラー",
    "files": "ファイル",
    "configuration": "設定",
    "customUrls": "カスタムURL",
    "urls": "URL",
    "fileStatus.active": "アクティブ",
    "fileStatus.outdated": "期限切れ",
    "fileStatus.error": "エラー",
    "sitemapConfiguration": "サイトマップ設定",
    "basicSettings": "基本設定",
    "enableSitemap": "サイトマップを有効にする",
    "autoGenerate": "自動生成",
    "contentTypes": "コンテンツタイプ",
    "includeImages": "画像を含める",
    "includeNews": "ニュースを含める",
    "includeVideos": "動画を含める",
    "defaultChangeFreq": "デフォルト更新頻度",
    "defaultPriority": "デフォルト優先度",
    "excludePaths": "除外パス",
    "always": "常に",
    "hourly": "毎時",
    "daily": "毎日",
    "weekly": "毎週",
    "monthly": "毎月",
    "yearly": "毎年",
    "never": "なし",
    "addUrl": "URL追加",
    "remove": "削除",
    "noCustomUrls": "カスタムURLなし",
    "loadDataFailed": "データの読み込みに失敗しました",
    "generateSuccess": "生成成功",
    "generateFailed": "生成失敗",
    "configUpdated": "設定が更新されました",
    "updateConfigFailed": "設定の更新に失敗しました",
    "downloadFailed": "ダウンロード失敗",
    "submitSuccess": "送信成功",
    "submitFailed": "送信失敗"
  },
  "keywordResearch": {
    "keywordResearch": "キーワード調査",
    "searchPlaceholder": "シードキーワードを入力...",
    "searching": "検索中",
    "search": "検索",
    "queryRequired": "検索クエリを入力してください",
    "searchComplete": "検索完了",
    "searchFailed": "検索失敗",
    "competitorAnalysisFailed": "競合分析に失敗しました",
    "selectKeywordsFirst": "まずキーワードを選択してください",
    "group": "グループ",
    "groupCreated": "グループが作成されました",
    "exportSuccess": "エクスポート成功",
    "exportFailed": "エクスポート失敗",
    "searchVolume": "検索ボリューム",
    "difficulty": "難易度",
    "searchIntent": "検索意図",
    "allIntents": "すべての意図",
    "informational": "情報型",
    "navigational": "ナビゲーション型",
    "transactional": "取引型",
    "commercial": "商業型",
    "createGroup": "グループ作成",
    "export": "エクスポート",
    "keywords": "キーワード",
    "groups": "グループ",
    "competitors": "競合",
    "relatedKeywords": "関連キーワード",
    "questionSuggestions": "質問提案",
    "intent.informational": "情報型",
    "intent.navigational": "ナビゲーション型",
    "intent.transactional": "取引型",
    "intent.commercial": "商業型",
    "noKeywords": "キーワードなし",
    "noKeywordsDescription": "キーワードを入力して検索を開始",
    "totalVolume": "総検索ボリューム",
    "avgDifficulty": "平均難易度",
    "noGroups": "グループなし",
    "noGroupsDescription": "キーワードを選択してグループを作成",
    "addCompetitor": "競合を追加",
    "analyze": "分析",
    "traffic": "トラフィック",
    "topKeywords": "トップキーワード",
    "noCompetitors": "競合なし",
    "noCompetitorsDescription": "ドメインを入力して競合を分析"
  },
  "structuredData": {
    "structuredDataGenerator": "構造化データジェネレーター",
    "structuredDataDescription": "ウェブページの構造化データマークアップを作成・管理",
    "selectTemplate": "テンプレート選択",
    "selectTemplatePlaceholder": "構造化データタイプを選択",
    "fillDetails": "詳細入力",
    "targetUrl": "対象URL",
    "generateCode": "コード生成",
    "generatedCode": "生成されたコード",
    "copy": "コピー",
    "validate": "検証",
    "preview": "プレビュー",
    "applyToPage": "ページに適用",
    "noCodeGenerated": "コード未生成",
    "selectTemplateAndFill": "テンプレートを選択し詳細を入力して構造化データコードを生成",
    "validationResults": "検証結果",
    "validStructuredData": "有効な構造化データ",
    "validStructuredDataDescription": "構造化データマークアップは有効で、ウェブページに適用できます",
    "errors": "エラー",
    "warnings": "警告",
    "true": "はい",
    "false": "いいえ",
    "structuredDataPreview": "構造化データプレビュー",
    "previewDescription": "以下はページに追加される構造化データコードです",
    "loadTemplatesFailed": "テンプレートの読み込みに失敗しました",
    "requiredFieldsMissing": "必須フィールドが不足: {fields}",
    "generationSuccess": "生成成功",
    "generationFailed": "生成失敗",
    "validationSuccess": "検証成功",
    "validationFailed": "検証失敗",
    "validationError": "検証エラーが発生しました",
    "applySuccess": "適用成功",
    "applyFailed": "適用失敗",
    "codeCopied": "コードをコピーしました",
    "copyFailed": "コピー失敗"
  }
  "accessibility": {
    "skipToMain": "メインコンテンツにスキップ",
    "skipToNavigation": "ナビゲーションにスキップ",
    "skipToFooter": "フッターにスキップ",
    "skipLinks": "スキップリンク",
    "shortcuts": {
      "title": "キーボードショートカット",
      "description": "以下のショートカットを使用して素早くナビゲートし操作できます",
      "showHelp": "キーボードショートカットヘルプを表示",
      "search": "検索",
      "home": "ホームに移動",
      "articles": "記事一覧",
      "dashboard": "管理ダッシュボード",
      "help": "ヘルプを表示",
      "close": "閉じる",
      "categories": {
        "navigation": "ナビゲーション",
        "general": "一般",
        "help": "ヘルプ"
      }
    }
  },
  "rss": {
    "title": "RSS購読",
    "subtitle": "RSSリーダーで最新コンテンツを購読",
    "subscribe": "購読",
    "copied": "{type}リンクをコピーしました",
    "copyFailed": "コピーに失敗しました",
    "filtered": "フィルター",
    "rssDescription": "標準のRSS 2.0形式",
    "atomDescription": "モダンなAtom 1.0形式",
    "jsonDescription": "軽量なJSON Feed形式",
    "howToUse": "RSSの使い方",
    "readers": {
      "title": "RSSリーダー",
      "description": "最高の体験のために専門のRSSリーダーを使用することをお勧めします"
    },
    "browsers": {
      "title": "ブラウザサポート",
      "description": "モダンブラウザはRSSリンクを直接開いてコンテンツをプレビューできます"
    },
    "apps": {
      "title": "モバイルアプリ",
      "description": "スマートフォンでRSSアプリを使用していつでもどこでも更新を読むことができます"
    }
  },
  "friendLinks": {
    "title": "友達リンク",
    "description": "優秀なウェブサイトとブログの推薦",
    "loading": "読み込み中...",
    "featured": "おすすめリンク",
    "allLinks": "すべてのリンク",
    "visit": "訪問",
    "categories": {
      "tech": "技術",
      "blog": "ブログ",
      "friend": "友達",
      "other": "その他"
    },
    "applyTitle": "友達リンク申請",
    "applyDescription": "質の高いウェブサイトの友達リンク申請を歓迎します。一緒により良いウェブエコシステムを構築しましょう",
    "requirements": {
      "title": "申請要件",
      "content": "健全なウェブサイトコンテンツ、違法または不適切な情報なし",
      "update": "ウェブサイトが定期的に更新されている、長期間非アクティブでない",
      "friendly": "ユーザーフレンドリーなウェブサイトデザインと良好なユーザーエクスペリエンス",
      "https": "HTTPS アクセスをサポート"
    },
    "applyNow": "今すぐ申請"
  },
  "meta": {
    "defaultTitle": "モダンブログシステム",
    "defaultDescription": "AI要約、多言語サポート、ダークモードなどの機能を備えたNext.js製モダンブログシステム"
  }
}
'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Plus, Edit, Trash2, MoreH<PERSON>zontal, Tag, FolderOpen, Search } from 'lucide-react'

interface Category {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  articleCount: number
  createdAt: string
  updatedAt: string
}

interface TagItem {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  articleCount: number
  createdAt: string
  updatedAt: string
}

export function CategoryTagManager() {
  const t = useTranslations('admin.categoryTag')
  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<TagItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<Category | TagItem | null>(null)
  const [activeTab, setActiveTab] = useState<'categories' | 'tags'>('categories')

  // 模拟数据加载
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      try {
        // 这里应该调用实际的 API
        const mockCategories: Category[] = [
          {
            id: '1',
            name: '技术',
            slug: 'tech',
            description: '技术相关文章',
            color: '#3b82f6',
            articleCount: 15,
            createdAt: '2024-01-01',
            updatedAt: '2024-01-01',
          },
          {
            id: '2',
            name: '生活',
            slug: 'life',
            description: '生活感悟和分享',
            color: '#10b981',
            articleCount: 8,
            createdAt: '2024-01-01',
            updatedAt: '2024-01-01',
          },
        ]

        const mockTags: TagItem[] = [
          {
            id: '1',
            name: 'React',
            slug: 'react',
            description: 'React 相关内容',
            color: '#61dafb',
            articleCount: 12,
            createdAt: '2024-01-01',
            updatedAt: '2024-01-01',
          },
          {
            id: '2',
            name: 'TypeScript',
            slug: 'typescript',
            description: 'TypeScript 相关内容',
            color: '#3178c6',
            articleCount: 8,
            createdAt: '2024-01-01',
            updatedAt: '2024-01-01',
          },
        ]

        setCategories(mockCategories)
        setTags(mockTags)
      } catch (error) {
        console.error('Failed to load categories and tags:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredTags = tags.filter(tag =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tag.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreate = (type: 'category' | 'tag') => {
    setEditingItem(null)
    setActiveTab(type === 'category' ? 'categories' : 'tags')
    setIsCreateDialogOpen(true)
  }

  const handleEdit = (item: Category | TagItem) => {
    setEditingItem(item)
    setIsCreateDialogOpen(true)
  }

  const handleDelete = async (id: string, type: 'category' | 'tag') => {
    if (!confirm(t('confirmDelete'))) return

    try {
      // 这里应该调用删除 API
      if (type === 'category') {
        setCategories(prev => prev.filter(cat => cat.id !== id))
      } else {
        setTags(prev => prev.filter(tag => tag.id !== id))
      }
    } catch (error) {
      console.error('Failed to delete:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t('loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground">{t('subtitle')}</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => handleCreate('category')} className="gap-2">
            <Plus className="h-4 w-4" />
            {t('createCategory')}
          </Button>
          <Button onClick={() => handleCreate('tag')} variant="outline" className="gap-2">
            <Plus className="h-4 w-4" />
            {t('createTag')}
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'categories' | 'tags')}>
        <TabsList>
          <TabsTrigger value="categories" className="gap-2">
            <FolderOpen className="h-4 w-4" />
            {t('categories')} ({categories.length})
          </TabsTrigger>
          <TabsTrigger value="tags" className="gap-2">
            <Tag className="h-4 w-4" />
            {t('tags')} ({tags.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="categories" className="space-y-4">
          <CategoryTable
            categories={filteredCategories}
            onEdit={handleEdit}
            onDelete={(id) => handleDelete(id, 'category')}
          />
        </TabsContent>

        <TabsContent value="tags" className="space-y-4">
          <TagTable
            tags={filteredTags}
            onEdit={handleEdit}
            onDelete={(id) => handleDelete(id, 'tag')}
          />
        </TabsContent>
      </Tabs>

      <CreateEditDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        editingItem={editingItem}
        type={activeTab}
        onSave={(data) => {
          // 这里应该调用保存 API
          console.log('Save:', data)
          setIsCreateDialogOpen(false)
        }}
      />
    </div>
  )
}

function CategoryTable({
  categories,
  onEdit,
  onDelete,
}: {
  categories: Category[]
  onEdit: (category: Category) => void
  onDelete: (id: string) => void
}) {
  const t = useTranslations('admin.categoryTag')

  return (
    <Card>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('description')}</TableHead>
            <TableHead>{t('articleCount')}</TableHead>
            <TableHead>{t('updatedAt')}</TableHead>
            <TableHead className="w-[70px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {categories.map((category) => (
            <TableRow key={category.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  {category.color && (
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                  )}
                  <span className="font-medium">{category.name}</span>
                  <Badge variant="secondary">{category.slug}</Badge>
                </div>
              </TableCell>
              <TableCell className="text-muted-foreground">
                {category.description || '-'}
              </TableCell>
              <TableCell>{category.articleCount}</TableCell>
              <TableCell className="text-muted-foreground">
                {new Date(category.updatedAt).toLocaleDateString()}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onEdit(category)}>
                      <Edit className="h-4 w-4 mr-2" />
                      {t('edit')}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onDelete(category.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      {t('delete')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  )
}

function TagTable({
  tags,
  onEdit,
  onDelete,
}: {
  tags: TagItem[]
  onEdit: (tag: TagItem) => void
  onDelete: (id: string) => void
}) {
  const t = useTranslations('admin.categoryTag')

  return (
    <Card>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('description')}</TableHead>
            <TableHead>{t('articleCount')}</TableHead>
            <TableHead>{t('updatedAt')}</TableHead>
            <TableHead className="w-[70px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tags.map((tag) => (
            <TableRow key={tag.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  {tag.color && (
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: tag.color }}
                    />
                  )}
                  <span className="font-medium">{tag.name}</span>
                  <Badge variant="secondary">{tag.slug}</Badge>
                </div>
              </TableCell>
              <TableCell className="text-muted-foreground">
                {tag.description || '-'}
              </TableCell>
              <TableCell>{tag.articleCount}</TableCell>
              <TableCell className="text-muted-foreground">
                {new Date(tag.updatedAt).toLocaleDateString()}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onEdit(tag)}>
                      <Edit className="h-4 w-4 mr-2" />
                      {t('edit')}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onDelete(tag.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      {t('delete')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  )
}

function CreateEditDialog({
  isOpen,
  onClose,
  editingItem,
  type,
  onSave,
}: {
  isOpen: boolean
  onClose: () => void
  editingItem: Category | TagItem | null
  type: 'categories' | 'tags'
  onSave: (data: any) => void
}) {
  const t = useTranslations('admin.categoryTag')
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    color: '#3b82f6',
  })

  useEffect(() => {
    if (editingItem) {
      setFormData({
        name: editingItem.name,
        slug: editingItem.slug,
        description: editingItem.description || '',
        color: editingItem.color || '#3b82f6',
      })
    } else {
      setFormData({
        name: '',
        slug: '',
        description: '',
        color: '#3b82f6',
      })
    }
  }, [editingItem, isOpen])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }

  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: generateSlug(name),
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {editingItem
              ? t(type === 'categories' ? 'editCategory' : 'editTag')
              : t(type === 'categories' ? 'createCategory' : 'createTag')
            }
          </DialogTitle>
          <DialogDescription>
            {t(type === 'categories' ? 'categoryDescription' : 'tagDescription')}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t('name')}</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder={t('namePlaceholder')}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="slug">{t('slug')}</Label>
            <Input
              id="slug"
              value={formData.slug}
              onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
              placeholder={t('slugPlaceholder')}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">{t('description')}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder={t('descriptionPlaceholder')}
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="color">{t('color')}</Label>
            <div className="flex items-center gap-2">
              <Input
                id="color"
                type="color"
                value={formData.color}
                onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                className="w-16 h-10"
              />
              <Input
                value={formData.color}
                onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                placeholder="#3b82f6"
                className="flex-1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {t('cancel')}
            </Button>
            <Button type="submit">
              {editingItem ? t('update') : t('create')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

import { Env, Context } from './types';
import { handleOptions, createErrorResponse } from './utils';
import { authMiddleware, loggingMiddleware, corsMiddleware, rateLimitMiddleware } from './middleware/auth';

// 导入路由处理器
import {
  handleGitHubCallback,
  getGitHubAuthUrl,
  verifyToken,
  refreshToken,
  logout,
  getCurrentUser,
  updateUser,
} from './routes/auth';

import {
  uploadFile,
  getFiles,
  getFile,
  deleteFile,
  getStorageUsage,
} from './routes/files';

import {
  generateSummary,
  generateTags,
  analyzeContent,
  translateText,
} from './routes/ai';

/**
 * 主要的 Worker 处理函数
 */
export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    try {
      const url = new URL(request.url);
      const path = url.pathname;
      const method = request.method;

      // 设置 CORS
      const corsHeaders = corsMiddleware([env.FRONTEND_URL, 'http://localhost:3000']);
      
      // 处理 OPTIONS 请求
      if (method === 'OPTIONS') {
        return new Response(null, { status: 204, headers: corsHeaders(request) });
      }

      // 初始化上下文
      let context: Context = {
        env,
        requestId: crypto.randomUUID(),
      };

      // 应用中间件
      context = await loggingMiddleware(request, env, ctx, context);
      context = await authMiddleware(request, env, ctx, context);

      // 路由处理
      let response: Response;

      // 认证相关路由
      if (path === '/api/auth/github/callback' && method === 'POST') {
        response = await handleGitHubCallback(request, env, ctx);
      } else if (path === '/api/auth/github/url' && method === 'GET') {
        response = await getGitHubAuthUrl(request, env, ctx);
      } else if (path === '/api/auth/verify' && method === 'POST') {
        response = await verifyToken(request, env, ctx);
      } else if (path === '/api/auth/refresh' && method === 'POST') {
        response = await refreshToken(request, env, ctx);
      } else if (path === '/api/auth/logout' && method === 'POST') {
        response = await logout(request, env, ctx, context);
      } else if (path === '/api/auth/me' && method === 'GET') {
        response = await getCurrentUser(request, env, ctx, context);
      } else if (path === '/api/auth/me' && method === 'PUT') {
        response = await updateUser(request, env, ctx, context);
      }
      
      // 文件相关路由
      else if (path === '/api/files/upload' && method === 'POST') {
        // 应用速率限制
        await rateLimitMiddleware(request, env, ctx, context, {
          windowMs: 60 * 1000, // 1 分钟
          maxRequests: 10, // 最多 10 次上传
        });
        response = await uploadFile(request, env, ctx, context);
      } else if (path === '/api/files' && method === 'GET') {
        response = await getFiles(request, env, ctx, context);
      } else if (path.startsWith('/api/files/') && method === 'GET') {
        const key = path.replace('/api/files/', '');
        response = await getFile(request, env, ctx, key);
      } else if (path.startsWith('/api/files/') && method === 'DELETE') {
        const fileId = path.replace('/api/files/', '');
        response = await deleteFile(request, env, ctx, context, fileId);
      } else if (path === '/api/files/usage' && method === 'GET') {
        response = await getStorageUsage(request, env, ctx, context);
      }
      
      // AI 相关路由
      else if (path === '/api/ai/summary' && method === 'POST') {
        // 应用速率限制
        await rateLimitMiddleware(request, env, ctx, context, {
          windowMs: 60 * 1000, // 1 分钟
          maxRequests: 20, // 最多 20 次 AI 请求
        });
        response = await generateSummary(request, env, ctx, context);
      } else if (path === '/api/ai/tags' && method === 'POST') {
        await rateLimitMiddleware(request, env, ctx, context, {
          windowMs: 60 * 1000,
          maxRequests: 20,
        });
        response = await generateTags(request, env, ctx, context);
      } else if (path === '/api/ai/analyze' && method === 'POST') {
        await rateLimitMiddleware(request, env, ctx, context, {
          windowMs: 60 * 1000,
          maxRequests: 10,
        });
        response = await analyzeContent(request, env, ctx, context);
      } else if (path === '/api/ai/translate' && method === 'POST') {
        await rateLimitMiddleware(request, env, ctx, context, {
          windowMs: 60 * 1000,
          maxRequests: 30,
        });
        response = await translateText(request, env, ctx, context);
      }
      
      // 健康检查
      else if (path === '/api/health' && method === 'GET') {
        response = new Response(JSON.stringify({
          success: true,
          message: 'API is healthy',
          timestamp: new Date().toISOString(),
          environment: env.ENVIRONMENT,
        }), {
          headers: { 'Content-Type': 'application/json' },
        });
      }
      
      // 404 处理
      else {
        response = createErrorResponse('Not Found', 404);
      }

      // 添加 CORS 头
      const responseHeaders = new Headers(response.headers);
      corsHeaders(request).forEach((value, key) => {
        responseHeaders.set(key, value);
      });

      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      });

    } catch (error) {
      console.error('Worker error:', error);
      
      // 创建错误响应
      const errorResponse = createErrorResponse(
        error instanceof Error ? error.message : 'Internal Server Error',
        500
      );
      
      // 添加 CORS 头
      const responseHeaders = new Headers(errorResponse.headers);
      corsMiddleware([env.FRONTEND_URL, 'http://localhost:3000'])(request).forEach((value, key) => {
        responseHeaders.set(key, value);
      });

      return new Response(errorResponse.body, {
        status: errorResponse.status,
        statusText: errorResponse.statusText,
        headers: responseHeaders,
      });
    }
  },

  /**
   * 定时任务处理
   */
  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    console.log('Scheduled event triggered:', event.cron);
    
    try {
      // 清理过期的会话
      await cleanupExpiredSessions(env);
      
      // 清理临时文件
      await cleanupTempFiles(env);
      
      console.log('Scheduled cleanup completed');
    } catch (error) {
      console.error('Scheduled task error:', error);
    }
  },
};

/**
 * 清理过期的会话
 */
async function cleanupExpiredSessions(env: Env): Promise<void> {
  try {
    // 这里可以实现清理逻辑
    // KV 会自动处理过期，但可以添加额外的清理逻辑
    console.log('Session cleanup completed');
  } catch (error) {
    console.error('Session cleanup error:', error);
  }
}

/**
 * 清理临时文件
 */
async function cleanupTempFiles(env: Env): Promise<void> {
  try {
    // 清理超过 7 天的临时文件
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    
    const tempFiles = await env.DB.prepare(`
      SELECT r2_key FROM files 
      WHERE folder = 'temp' AND uploaded_at < ?
    `).bind(sevenDaysAgo.toISOString()).all();
    
    for (const file of tempFiles.results) {
      try {
        await env.STORAGE.delete(file.r2_key as string);
        await env.DB.prepare('DELETE FROM files WHERE r2_key = ?').bind(file.r2_key).run();
      } catch (error) {
        console.error('Failed to delete temp file:', file.r2_key, error);
      }
    }
    
    console.log(`Cleaned up ${tempFiles.results.length} temp files`);
  } catch (error) {
    console.error('Temp file cleanup error:', error);
  }
}

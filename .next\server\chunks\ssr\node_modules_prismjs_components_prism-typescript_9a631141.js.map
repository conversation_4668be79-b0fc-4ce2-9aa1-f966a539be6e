{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/node_modules/prismjs/components/prism-typescript.js"], "sourcesContent": ["(function (Prism) {\n\n\tPrism.languages.typescript = Prism.languages.extend('javascript', {\n\t\t'class-name': {\n\t\t\tpattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: null // see below\n\t\t},\n\t\t'builtin': /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/,\n\t});\n\n\t// The keywords TypeScript adds to JavaScript\n\tPrism.languages.typescript.keyword.push(\n\t\t/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/,\n\t\t// keywords that have to be followed by an identifier\n\t\t/\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/,\n\t\t// This is for `import type *, {}`\n\t\t/\\btype\\b(?=\\s*(?:[\\{*]|$))/\n\t);\n\n\t// doesn't work with TS because TS is too complex\n\tdelete Prism.languages.typescript['parameter'];\n\tdelete Prism.languages.typescript['literal-property'];\n\n\t// a version of typescript specifically for highlighting types\n\tvar typeInside = Prism.languages.extend('typescript', {});\n\tdelete typeInside['class-name'];\n\n\tPrism.languages.typescript['class-name'].inside = typeInside;\n\n\tPrism.languages.insertBefore('typescript', 'function', {\n\t\t'decorator': {\n\t\t\tpattern: /@[$\\w\\xA0-\\uFFFF]+/,\n\t\t\tinside: {\n\t\t\t\t'at': {\n\t\t\t\t\tpattern: /^@/,\n\t\t\t\t\talias: 'operator'\n\t\t\t\t},\n\t\t\t\t'function': /^[\\s\\S]+/\n\t\t\t}\n\t\t},\n\t\t'generic-function': {\n\t\t\t// e.g. foo<T extends \"bar\" | \"baz\">( ...\n\t\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n\t\t\t\t'generic': {\n\t\t\t\t\tpattern: /<[\\s\\S]+/, // everything after the first <\n\t\t\t\t\talias: 'class-name',\n\t\t\t\t\tinside: typeInside\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\tPrism.languages.ts = Prism.languages.typescript;\n\n}(Prism));\n"], "names": [], "mappings": "AAAC,CAAA,SAAU,MAAK;IAEf,OAAM,SAAS,CAAC,UAAU,GAAG,OAAM,SAAS,CAAC,MAAM,CAAC,cAAc;QACjE,cAAc;YACb,SAAS;YACT,YAAY;YACZ,QAAQ;YACR,QAAQ,KAAK,YAAY;QAC1B;QACA,WAAW;IACZ;IAEA,6CAA6C;IAC7C,OAAM,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CACtC,sDACA,qDAAqD;IACrD,4FACA,kCAAkC;IAClC;IAGD,iDAAiD;IACjD,OAAO,OAAM,SAAS,CAAC,UAAU,CAAC,YAAY;IAC9C,OAAO,OAAM,SAAS,CAAC,UAAU,CAAC,mBAAmB;IAErD,8DAA8D;IAC9D,IAAI,aAAa,OAAM,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC;IACvD,OAAO,UAAU,CAAC,aAAa;IAE/B,OAAM,SAAS,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,GAAG;IAElD,OAAM,SAAS,CAAC,YAAY,CAAC,cAAc,YAAY;QACtD,aAAa;YACZ,SAAS;YACT,QAAQ;gBACP,MAAM;oBACL,SAAS;oBACT,OAAO;gBACR;gBACA,YAAY;YACb;QACD;QACA,oBAAoB;YACnB,yCAAyC;YACzC,SAAS;YACT,QAAQ;YACR,QAAQ;gBACP,YAAY;gBACZ,WAAW;oBACV,SAAS;oBACT,OAAO;oBACP,QAAQ;gBACT;YACD;QACD;IACD;IAEA,OAAM,SAAS,CAAC,EAAE,GAAG,OAAM,SAAS,CAAC,UAAU;AAEhD,CAAA,EAAE", "ignoreList": [0], "debugId": null}}]}
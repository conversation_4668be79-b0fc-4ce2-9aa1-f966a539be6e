import{redirect as e,permanentRedirect as t}from"next/navigation";import{forwardRef as o}from"react";import{receiveRoutingConfig as r}from"../../routing/config.js";import n from"../../shared/use.js";import{isLocalizableHref as a,isPromise as i}from"../../shared/utils.js";import c from"./BaseLink.js";import{serializeSearchParams as f,compileLocalizedPathname as m,applyPathnamePrefix as l,normalizeNameOrNameWithParams as s}from"./utils.js";import{jsx as p}from"react/jsx-runtime";function u(u,h){const j=r(h||{}),d=j.pathnames;function g({href:e,locale:t,...o},r){let f,m;"object"==typeof e?(f=e.pathname,m=e.params):f=e;const l=a(e),s=u(),h=i(s)?n(s):s,g=l?y({locale:t||h,href:null==d?f:{pathname:f,params:m},forcePrefix:null!=t||void 0}):f;return p(c,{ref:r,href:"object"==typeof e?{...e,pathname:g}:g,locale:t,localeCookie:j.localeCookie,...o})}const x=o(g);function y(e){const{forcePrefix:t,href:o,locale:r}=e;let n;return null==d?"object"==typeof o?(n=o.pathname,o.query&&(n+=f(o.query))):n=o:n=m({locale:r,...s(o),pathnames:j.pathnames}),l(n,r,j,t)}function k(e){return function(t,...o){return e(y(t),...o)}}const b=k(e),P=k(t);return{config:j,Link:x,redirect:b,permanentRedirect:P,getPathname:y}}export{u as default};

# =============================================================================
# 现代化博客系统 - 环境变量配置示例
# =============================================================================

# -----------------------------------------------------------------------------
# 基础配置
# -----------------------------------------------------------------------------
NEXT_PUBLIC_SITE_NAME="现代博客系统"
NEXT_PUBLIC_SITE_URL="https://your-domain.com"
NEXT_PUBLIC_SITE_DESCRIPTION="基于 Next.js 的现代化博客系统"
NODE_ENV="development"
NEXT_PUBLIC_APP_ENV="development"

# -----------------------------------------------------------------------------
# Cloudflare Workers 配置
# -----------------------------------------------------------------------------
NEXT_PUBLIC_API_URL="https://your-workers.your-subdomain.workers.dev"
CLOUDFLARE_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"

# -----------------------------------------------------------------------------
# 数据库配置 (Cloudflare D1)
# -----------------------------------------------------------------------------
DATABASE_NAME="blog-database"
DATABASE_ID="your-d1-database-id"
DB_RETRY_ATTEMPTS=3
DB_TIMEOUT_MS=5000

# -----------------------------------------------------------------------------
# 文件存储配置 (Cloudflare R2)
# -----------------------------------------------------------------------------
R2_BUCKET_NAME="blog-storage"
R2_ACCESS_KEY_ID="your-r2-access-key"
R2_SECRET_ACCESS_KEY="your-r2-secret-key"
R2_ENDPOINT="https://your-account-id.r2.cloudflarestorage.com"
NEXT_PUBLIC_R2_PUBLIC_URL="https://your-custom-domain.com"
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp,image/gif,application/pdf"

# -----------------------------------------------------------------------------
# 认证配置
# -----------------------------------------------------------------------------
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_EXPIRES_IN="30d"
GITHUB_CLIENT_ID="********************"
GITHUB_CLIENT_SECRET="e52ad4d7a6a07a326666f2a3cd9e29ec6997c363"
GITHUB_CALLBACK_URL="http://localhost:3000/auth/github/callback"
ADMIN_GITHUB_USERNAME="your-github-username"
ADMIN_EMAIL="<EMAIL>"

# -----------------------------------------------------------------------------
# 评论系统配置 (Giscus)
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GISCUS_REPO="your-username/your-repo"
NEXT_PUBLIC_GISCUS_REPO_ID="your-giscus-repo-id"
NEXT_PUBLIC_GISCUS_CATEGORY="General"
NEXT_PUBLIC_GISCUS_CATEGORY_ID="your-giscus-category-id"

# -----------------------------------------------------------------------------
# 分析和监控配置
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"
NEXT_PUBLIC_BAIDU_ANALYTICS_ID="your-baidu-analytics-id"
NEXT_PUBLIC_SENTRY_DSN="your-sentry-dsn"

# -----------------------------------------------------------------------------
# AI 服务配置
# -----------------------------------------------------------------------------
OPENAI_API_KEY="your-openai-api-key"
CLOUDFLARE_AI_TOKEN="your-cloudflare-ai-token"
OPENAI_MODEL="gpt-3.5-turbo"

# -----------------------------------------------------------------------------
# 安全配置
# -----------------------------------------------------------------------------
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_HOUR=1000
CORS_ORIGINS="http://localhost:3000,https://your-domain.com"

# -----------------------------------------------------------------------------
# 功能开关
# -----------------------------------------------------------------------------
ENABLE_COMMENTS=true
ENABLE_ANALYTICS=true
ENABLE_RSS=true
ENABLE_SITEMAP=true
ENABLE_SEARCH=true
ENABLE_FRIEND_LINKS=true

# -----------------------------------------------------------------------------
# 本地化配置
# -----------------------------------------------------------------------------
NEXT_PUBLIC_DEFAULT_LOCALE="zh"
NEXT_PUBLIC_SUPPORTED_LOCALES="zh,en,ja"
TIMEZONE="Asia/Shanghai"

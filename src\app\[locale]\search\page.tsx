import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import { useTranslations } from 'next-intl'
import { getTranslations } from 'next-intl/server'
import { SearchResults } from '@/components/search/search-results'
import { SearchFilters } from '@/components/search/search-filters'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Search, Filter } from 'lucide-react'

interface SearchPageProps {
  params: {
    locale: string
  }
  searchParams: {
    q?: string
    type?: string
    category?: string
    tag?: string
    author?: string
    date?: string
    page?: string
  }
}

export async function generateMetadata({
  params: { locale },
  searchParams,
}: SearchPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'search' })
  const query = searchParams.q || ''

  return {
    title: query ? t('resultsTitle', { query }) : t('title'),
    description: query ? t('resultsDescription', { query }) : t('description'),
    robots: 'noindex, follow',
  }
}

function SearchResultsSkeleton() {
  return (
    <div className="space-y-6">
      {Array.from({ length: 5 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <div className="space-y-3">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
              <div className="flex gap-2">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-20" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export default function SearchPage({ params, searchParams }: SearchPageProps) {
  const query = searchParams.q || ''
  const hasQuery = query.length > 0

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Search className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold">
              {hasQuery ? (
                <SearchPageTitle query={query} />
              ) : (
                <SearchPageTitleDefault />
              )}
            </h1>
          </div>
          {hasQuery && (
            <p className="text-muted-foreground">
              <SearchPageDescription query={query} />
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 搜索过滤器 */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Filter className="h-5 w-5" />
                  <SearchFiltersTitle />
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SearchFilters searchParams={searchParams} />
              </CardContent>
            </Card>
          </div>

          {/* 搜索结果 */}
          <div className="lg:col-span-3">
            {hasQuery ? (
              <Suspense fallback={<SearchResultsSkeleton />}>
                <SearchResults searchParams={searchParams} />
              </Suspense>
            ) : (
              <NoSearchQuery />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

function SearchPageTitle({ query }: { query: string }) {
  const t = useTranslations('search')
  return <>{t('resultsFor', { query })}</>
}

function SearchPageTitleDefault() {
  const t = useTranslations('search')
  return <>{t('title')}</>
}

function SearchPageDescription({ query }: { query: string }) {
  const t = useTranslations('search')
  return <>{t('searchDescription', { query })}</>
}

function SearchFiltersTitle() {
  const t = useTranslations('search')
  return <>{t('filters')}</>
}

function NoSearchQuery() {
  const t = useTranslations('search')
  
  return (
    <Card>
      <CardContent className="p-12 text-center">
        <Search className="h-16 w-16 text-muted-foreground mx-auto mb-6" />
        <h2 className="text-2xl font-semibold mb-4">{t('startSearching')}</h2>
        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
          {t('startSearchingDescription')}
        </p>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            {t('searchTips')}:
          </div>
          <ul className="text-sm text-muted-foreground space-y-2 max-w-sm mx-auto">
            <li>• {t('tip1')}</li>
            <li>• {t('tip2')}</li>
            <li>• {t('tip3')}</li>
            <li>• {t('tip4')}</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

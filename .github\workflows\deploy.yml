name: Deploy Modern Blog System

on:
  push:
    branches:
      - main        # 生产环境部署
      - staging     # 测试环境部署
      - develop     # 开发环境部署
  pull_request:
    branches:
      - main
      - staging

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # =============================================================================
  # 代码质量检查
  # =============================================================================
  lint-and-test:
    name: Lint and Test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run ESLint
        run: pnpm lint

      - name: Run TypeScript check
        run: pnpm type-check

      - name: Run tests
        run: pnpm test
        env:
          CI: true

      - name: Build frontend
        run: pnpm build

      - name: Build workers
        run: |
          cd workers
          pnpm build

  # =============================================================================
  # 安全扫描
  # =============================================================================
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: lint-and-test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # =============================================================================
  # 部署到开发环境
  # =============================================================================
  deploy-development:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: |
          pnpm install --frozen-lockfile
          cd workers && pnpm install --frozen-lockfile

      - name: Deploy Workers to Development
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --env development
          workingDirectory: workers

      - name: Deploy Frontend to Development
        run: |
          echo "Deploying frontend to development environment"
          # 这里可以添加前端部署逻辑，比如部署到 Vercel 或 Cloudflare Pages

  # =============================================================================
  # 部署到测试环境
  # =============================================================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-scan]
    if: github.ref == 'refs/heads/staging'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: |
          pnpm install --frozen-lockfile
          cd workers && pnpm install --frozen-lockfile

      - name: Run database migrations (Staging)
        run: |
          cd workers
          pnpm wrangler d1 migrations apply modern-blog-db-staging --env staging
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

      - name: Deploy Workers to Staging
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --env staging
          workingDirectory: workers

      - name: Deploy Frontend to Staging
        run: |
          echo "Deploying frontend to staging environment"
          # 这里可以添加前端部署逻辑

      - name: Run smoke tests
        run: |
          echo "Running smoke tests against staging environment"
          # 这里可以添加冒烟测试

  # =============================================================================
  # 部署到生产环境
  # =============================================================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: |
          pnpm install --frozen-lockfile
          cd workers && pnpm install --frozen-lockfile

      - name: Create database backup
        run: |
          cd workers
          pnpm wrangler kv:key put "backup-$(date +%Y%m%d-%H%M%S)" "$(date)" --namespace-id ${{ secrets.BACKUP_KV_NAMESPACE_ID }}
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
        continue-on-error: true

      - name: Run database migrations (Production)
        run: |
          cd workers
          pnpm wrangler d1 migrations apply modern-blog-db-prod --env production
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

      - name: Deploy Workers to Production
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --env production
          workingDirectory: workers

      - name: Deploy Frontend to Production
        run: |
          echo "Deploying frontend to production environment"
          # 这里可以添加前端部署逻辑

      - name: Health check
        run: |
          echo "Performing health check..."
          sleep 30
          curl -f https://api.yourdomain.com/health || exit 1

      - name: Notify deployment success
        if: success()
        run: |
          echo "Production deployment successful!"
          # 这里可以添加通知逻辑，比如发送到 Slack 或邮件

      - name: Rollback on failure
        if: failure()
        run: |
          echo "Deployment failed, initiating rollback..."
          # 这里可以添加回滚逻辑

  # =============================================================================
  # 清理和通知
  # =============================================================================
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-development, deploy-staging, deploy-production]
    if: always()
    
    steps:
      - name: Clean up artifacts
        run: |
          echo "Cleaning up deployment artifacts..."

      - name: Send notification
        if: always()
        run: |
          echo "Sending deployment notification..."
          # 这里可以添加通知逻辑

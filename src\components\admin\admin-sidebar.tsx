'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
  LayoutDashboard,
  FileText,
  Users,
  FolderOpen,
  Tag,
  Image,
  Settings,
  BarChart3,
  MessageSquare,
  Link as LinkIcon,
  Globe,
  ChevronLeft,
  ChevronRight,
  Menu,
} from 'lucide-react'

interface AdminSidebarProps {
  className?: string
}

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string
  children?: NavItem[]
}

export function AdminSidebar({ className }: AdminSidebarProps) {
  const pathname = usePathname()
  const t = useTranslations('admin.sidebar')
  const [collapsed, setCollapsed] = useState(false)

  const navItems: NavItem[] = [
    {
      title: t('dashboard'),
      href: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      title: t('content'),
      href: '/dashboard/content',
      icon: FileText,
      children: [
        {
          title: t('articles'),
          href: '/dashboard/articles',
          icon: FileText,
        },
        {
          title: t('pages'),
          href: '/dashboard/pages',
          icon: Globe,
        },
        {
          title: t('categories'),
          href: '/dashboard/categories',
          icon: FolderOpen,
        },
        {
          title: t('tags'),
          href: '/dashboard/tags',
          icon: Tag,
        },
      ],
    },
    {
      title: t('media'),
      href: '/dashboard/files',
      icon: Image,
    },
    {
      title: t('users'),
      href: '/dashboard/users',
      icon: Users,
    },
    {
      title: t('comments'),
      href: '/dashboard/comments',
      icon: MessageSquare,
      badge: '5',
    },
    {
      title: t('links'),
      href: '/dashboard/links',
      icon: LinkIcon,
    },
    {
      title: t('analytics'),
      href: '/dashboard/analytics',
      icon: BarChart3,
    },
    {
      title: t('settings'),
      href: '/dashboard/settings',
      icon: Settings,
    },
  ]

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  return (
    <div className={cn('flex flex-col h-full bg-background border-r', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">M</span>
            </div>
            <span className="font-semibold">{t('title')}</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCollapsed(!collapsed)}
          className="ml-auto"
        >
          {collapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-2">
          {navItems.map((item) => (
            <NavItemComponent
              key={item.href}
              item={item}
              isActive={isActive}
              collapsed={collapsed}
            />
          ))}
        </nav>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t">
        {!collapsed && (
          <div className="text-xs text-muted-foreground">
            <p>{t('version')} 1.0.0</p>
            <p>{t('lastUpdate')}: {new Date().toLocaleDateString()}</p>
          </div>
        )}
      </div>
    </div>
  )
}

function NavItemComponent({
  item,
  isActive,
  collapsed,
}: {
  item: NavItem
  isActive: (href: string) => boolean
  collapsed: boolean
}) {
  const [expanded, setExpanded] = useState(false)
  const hasChildren = item.children && item.children.length > 0

  if (hasChildren) {
    return (
      <div>
        <Button
          variant="ghost"
          className={cn(
            'w-full justify-start gap-2 h-9',
            isActive(item.href) && 'bg-accent text-accent-foreground'
          )}
          onClick={() => setExpanded(!expanded)}
        >
          <item.icon className="h-4 w-4 shrink-0" />
          {!collapsed && (
            <>
              <span className="flex-1 text-left">{item.title}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
              <ChevronRight
                className={cn(
                  'h-4 w-4 transition-transform',
                  expanded && 'rotate-90'
                )}
              />
            </>
          )}
        </Button>
        {expanded && !collapsed && (
          <div className="ml-6 mt-1 space-y-1">
            {item.children?.map((child) => (
              <Link key={child.href} href={child.href}>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'w-full justify-start gap-2 h-8',
                    isActive(child.href) && 'bg-accent text-accent-foreground'
                  )}
                >
                  <child.icon className="h-3 w-3" />
                  <span>{child.title}</span>
                  {child.badge && (
                    <Badge variant="secondary" className="ml-auto">
                      {child.badge}
                    </Badge>
                  )}
                </Button>
              </Link>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <Link href={item.href}>
      <Button
        variant="ghost"
        className={cn(
          'w-full justify-start gap-2 h-9',
          isActive(item.href) && 'bg-accent text-accent-foreground'
        )}
      >
        <item.icon className="h-4 w-4 shrink-0" />
        {!collapsed && (
          <>
            <span className="flex-1 text-left">{item.title}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-auto">
                {item.badge}
              </Badge>
            )}
          </>
        )}
      </Button>
    </Link>
  )
}

/**
 * 移动端侧边栏
 */
export function MobileAdminSidebar() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="md:hidden"
      >
        <Menu className="h-4 w-4" />
      </Button>

      {isOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div
            className="fixed inset-0 bg-background/80 backdrop-blur-sm"
            onClick={() => setIsOpen(false)}
          />
          <div className="fixed left-0 top-0 h-full w-72 bg-background border-r">
            <AdminSidebar />
          </div>
        </div>
      )}
    </>
  )
}

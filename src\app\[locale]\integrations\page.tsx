import { Metadata } from 'next'
import { useTranslations } from 'next-intl'
import { getTranslations } from 'next-intl/server'
import { 
  Puzzle, 
  Paintbrush, 
  Mail, 
  Share2, 
  Webhook,
  Play,
  Music,
  Image as ImageIcon,
  Settings,
  Zap
} from 'lucide-react'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { NewsletterSubscription } from '@/components/integrations/newsletter-subscription'
import { SocialMediaIntegration } from '@/components/integrations/social-media-integration'
import { PluginManager } from '@/components/plugins/plugin-manager'
import { ThemeCustomizer } from '@/components/plugins/theme-customizer'
import { VideoPlayer } from '@/components/media/video-player'
import { AudioPlayer } from '@/components/media/audio-player'
import { ImageGallery } from '@/components/media/image-gallery'
import { WebhookManager } from '@/components/webhooks/webhook-manager'

interface IntegrationsPageProps {
  params: {
    locale: string
  }
}

export async function generateMetadata({ params }: IntegrationsPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'integrations' })
  
  return {
    title: t('title'),
    description: t('description')
  }
}

export default function IntegrationsPage({ params }: IntegrationsPageProps) {
  const t = useTranslations('integrations')
  
  // 示例数据
  const sampleVideoSources = [
    {
      src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      type: 'video/mp4',
      quality: '720p'
    }
  ]
  
  const sampleAudioTracks = [
    {
      src: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      title: 'Sample Audio Track',
      artist: 'Demo Artist',
      album: 'Demo Album'
    }
  ]
  
  const sampleImages = [
    {
      src: 'https://picsum.photos/800/600?random=1',
      alt: 'Sample Image 1',
      caption: 'Beautiful landscape'
    },
    {
      src: 'https://picsum.photos/800/600?random=2',
      alt: 'Sample Image 2',
      caption: 'City skyline'
    },
    {
      src: 'https://picsum.photos/800/600?random=3',
      alt: 'Sample Image 3',
      caption: 'Nature photography'
    }
  ]

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* 页面头部 */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">{t('title')}</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          {t('description')}
        </p>
      </div>

      {/* 功能概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="text-center">
          <CardHeader>
            <Mail className="h-12 w-12 mx-auto mb-2 text-primary" />
            <CardTitle className="text-lg">{t('newsletter.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('newsletter.description')}
            </p>
            <Badge variant="secondary">{t('newsletter.status')}</Badge>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Share2 className="h-12 w-12 mx-auto mb-2 text-primary" />
            <CardTitle className="text-lg">{t('social.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('social.description')}
            </p>
            <Badge variant="secondary">{t('social.status')}</Badge>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Puzzle className="h-12 w-12 mx-auto mb-2 text-primary" />
            <CardTitle className="text-lg">{t('plugins.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('plugins.description')}
            </p>
            <Badge variant="secondary">{t('plugins.status')}</Badge>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Webhook className="h-12 w-12 mx-auto mb-2 text-primary" />
            <CardTitle className="text-lg">{t('webhooks.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {t('webhooks.description')}
            </p>
            <Badge variant="secondary">{t('webhooks.status')}</Badge>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容 */}
      <Tabs defaultValue="newsletter" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-6">
          <TabsTrigger value="newsletter" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.newsletter')}</span>
          </TabsTrigger>
          <TabsTrigger value="social" className="flex items-center gap-2">
            <Share2 className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.social')}</span>
          </TabsTrigger>
          <TabsTrigger value="plugins" className="flex items-center gap-2">
            <Puzzle className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.plugins')}</span>
          </TabsTrigger>
          <TabsTrigger value="themes" className="flex items-center gap-2">
            <Paintbrush className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.themes')}</span>
          </TabsTrigger>
          <TabsTrigger value="media" className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.media')}</span>
          </TabsTrigger>
          <TabsTrigger value="webhooks" className="flex items-center gap-2">
            <Webhook className="h-4 w-4" />
            <span className="hidden sm:inline">{t('tabs.webhooks')}</span>
          </TabsTrigger>
        </TabsList>

        {/* 邮件订阅 */}
        <TabsContent value="newsletter" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('newsletter.title')}</h2>
            <p className="text-muted-foreground">{t('newsletter.fullDescription')}</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">{t('newsletter.variants')}</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">{t('newsletter.cardVariant')}</h4>
                  <NewsletterSubscription variant="card" />
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">{t('newsletter.inlineVariant')}</h4>
                  <NewsletterSubscription variant="inline" />
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">{t('newsletter.minimalVariant')}</h4>
                  <NewsletterSubscription variant="minimal" />
                </div>
              </div>
            </div>
            
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">{t('newsletter.features')}</h3>
              
              <div className="space-y-4">
                <NewsletterSubscription 
                  variant="card" 
                  showStats={true} 
                  showSettings={true}
                />
              </div>
            </div>
          </div>
        </TabsContent>

        {/* 社交媒体集成 */}
        <TabsContent value="social" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('social.title')}</h2>
            <p className="text-muted-foreground">{t('social.fullDescription')}</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">{t('social.shareButtons')}</h3>
              <SocialMediaIntegration 
                variant="share" 
                url="https://example.com/article"
                title="Sample Article Title"
                summary="This is a sample article summary for sharing"
              />
              
              <h3 className="text-lg font-semibold">{t('social.connectAccounts')}</h3>
              <SocialMediaIntegration variant="connect" />
            </div>
            
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">{t('social.dashboard')}</h3>
              <SocialMediaIntegration 
                variant="dashboard" 
                showStats={true}
              />
            </div>
          </div>
        </TabsContent>

        {/* 插件管理 */}
        <TabsContent value="plugins" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('plugins.title')}</h2>
            <p className="text-muted-foreground">{t('plugins.fullDescription')}</p>
          </div>
          
          <PluginManager />
        </TabsContent>

        {/* 主题定制 */}
        <TabsContent value="themes" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('themes.title')}</h2>
            <p className="text-muted-foreground">{t('themes.fullDescription')}</p>
          </div>
          
          <ThemeCustomizer />
        </TabsContent>

        {/* 多媒体内容 */}
        <TabsContent value="media" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('media.title')}</h2>
            <p className="text-muted-foreground">{t('media.fullDescription')}</p>
          </div>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Play className="h-5 w-5" />
                {t('media.videoPlayer')}
              </h3>
              <VideoPlayer
                sources={sampleVideoSources}
                poster="https://picsum.photos/800/450?random=video"
                title="Sample Video"
                className="max-w-4xl mx-auto"
              />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Music className="h-5 w-5" />
                {t('media.audioPlayer')}
              </h3>
              <div className="max-w-2xl mx-auto">
                <AudioPlayer
                  tracks={sampleAudioTracks}
                  showPlaylist={true}
                  showArtwork={true}
                />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                {t('media.imageGallery')}
              </h3>
              <ImageGallery
                images={sampleImages}
                columns={3}
                lightbox={true}
                showInfo={true}
                className="max-w-4xl mx-auto"
              />
            </div>
          </div>
        </TabsContent>

        {/* Webhook管理 */}
        <TabsContent value="webhooks" className="space-y-6">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold">{t('webhooks.title')}</h2>
            <p className="text-muted-foreground">{t('webhooks.fullDescription')}</p>
          </div>
          
          <WebhookManager />
        </TabsContent>
      </Tabs>
    </div>
  )
}

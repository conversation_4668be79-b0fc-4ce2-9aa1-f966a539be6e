"use client"

import { useEffect } from "react"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import rehypeHighlight from "rehype-highlight"
import rehypeRaw from "rehype-raw"
import { cn } from "@/lib/utils"

// 导入代码高亮样式
import "prismjs/themes/prism-tomorrow.css"

// 临时类型声明，避免依赖外部类型包

interface MarkdownRendererProps {
  content: string
  className?: string
}

export function MarkdownRenderer({ content, className }: MarkdownRendererProps) {
  useEffect(() => {
    // 动态导入 Prism.js 语言支持
    // @ts-ignore - 临时忽略类型错误
    import("prismjs/components/prism-javascript")
    // @ts-ignore
    import("prismjs/components/prism-typescript")
    // @ts-ignore
    import("prismjs/components/prism-jsx")
    // @ts-ignore
    import("prismjs/components/prism-tsx")
    // @ts-ignore
    import("prismjs/components/prism-css")
    // @ts-ignore
    import("prismjs/components/prism-scss")
    // @ts-ignore
    import("prismjs/components/prism-json")
    // @ts-ignore
    import("prismjs/components/prism-bash")
    // @ts-ignore
    import("prismjs/components/prism-python")
    // @ts-ignore
    import("prismjs/components/prism-sql")
  }, [])

  return (
    <div className={cn("prose prose-gray dark:prose-invert max-w-none", className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight, rehypeRaw]}
        components={{
          // 自定义代码块渲染
          code({ node, inline, className, children, ...props }: any) {
            const match = /language-(\w+)/.exec(className || "")
            return !inline && match ? (
              <pre className={cn("rounded-lg border bg-muted p-4 overflow-x-auto", className)}>
                <code className={className} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code 
                className={cn(
                  "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold",
                  className
                )} 
                {...props}
              >
                {children}
              </code>
            )
          },
          // 自定义链接渲染
          a({ href, children, ...props }) {
            return (
              <a
                href={href}
                target={href?.startsWith("http") ? "_blank" : undefined}
                rel={href?.startsWith("http") ? "noopener noreferrer" : undefined}
                className="text-primary hover:underline"
                {...props}
              >
                {children}
              </a>
            )
          },
          // 自定义图片渲染
          img({ src, alt, ...props }) {
            return (
              <img
                src={src}
                alt={alt}
                className="rounded-lg border shadow-sm max-w-full h-auto"
                loading="lazy"
                {...props}
              />
            )
          },
          // 自定义表格渲染
          table({ children, ...props }) {
            return (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-border" {...props}>
                  {children}
                </table>
              </div>
            )
          },
          th({ children, ...props }) {
            return (
              <th className="border border-border bg-muted p-2 text-left font-semibold" {...props}>
                {children}
              </th>
            )
          },
          td({ children, ...props }) {
            return (
              <td className="border border-border p-2" {...props}>
                {children}
              </td>
            )
          },
          // 自定义引用块渲染
          blockquote({ children, ...props }) {
            return (
              <blockquote 
                className="border-l-4 border-primary pl-4 italic text-muted-foreground"
                {...props}
              >
                {children}
              </blockquote>
            )
          },
          // 自定义标题渲染
          h1({ children, ...props }) {
            return (
              <h1 className="text-3xl font-bold mt-8 mb-4 first:mt-0" {...props}>
                {children}
              </h1>
            )
          },
          h2({ children, ...props }) {
            return (
              <h2 className="text-2xl font-semibold mt-6 mb-3" {...props}>
                {children}
              </h2>
            )
          },
          h3({ children, ...props }) {
            return (
              <h3 className="text-xl font-semibold mt-5 mb-2" {...props}>
                {children}
              </h3>
            )
          },
          // 自定义列表渲染
          ul({ children, ...props }) {
            return (
              <ul className="list-disc list-inside space-y-1 ml-4" {...props}>
                {children}
              </ul>
            )
          },
          ol({ children, ...props }) {
            return (
              <ol className="list-decimal list-inside space-y-1 ml-4" {...props}>
                {children}
              </ol>
            )
          },
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}

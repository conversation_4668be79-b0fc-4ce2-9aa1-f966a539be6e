"use client"

import Link from "next/link"
import { useState } from "react"
import { Menu, Search, X } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { She<PERSON>, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { ThemeToggle } from "@/components/theme-toggle"
import { UserNav } from "@/components/auth/user-nav"
import { GlobalSearch } from "@/components/search/global-search"
import { siteConfig } from "@/config/site"
import { useTranslations } from "next-intl"
import { LanguageToggle } from "@/components/i18n/language-toggle"
import { cn } from "@/lib/utils"

export function Header() {
  const t = useTranslations('navigation')
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const navigation = [
    { name: t('home'), href: "/" },
    { name: t('articles'), href: "/articles" },
    { name: t('archive'), href: "/archive" },
    { name: t('now'), href: "/now" },
    { name: t('guestbook'), href: "/guestbook" },
    { name: t('integrations'), href: "/integrations" },
    { name: t('business'), href: "/business" },
  ]

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <span className="text-lg sm:text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent truncate max-w-[200px] sm:max-w-none">
            {siteConfig.name}
          </span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {navigation.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              {item.name}
            </Link>
          ))}
        </nav>

        {/* Right Side Actions */}
        <div className="flex items-center space-x-1 sm:space-x-2">
          {/* Mobile Search Toggle */}
          <Button
            variant="ghost"
            size="icon"
            className="sm:hidden"
            onClick={() => setIsSearchOpen(!isSearchOpen)}
            aria-label={t('search')}
          >
            <Search className="h-4 w-4" />
          </Button>

          {/* Desktop Search */}
          <div className="hidden sm:block">
            <GlobalSearch />
          </div>

          {/* Language Toggle */}
          <div className="hidden sm:block">
            <LanguageToggle />
          </div>

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* User Navigation */}
          <div className="hidden sm:block">
            <UserNav />
          </div>

          {/* Mobile Menu */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                aria-label={t('menu')}
              >
                <Menu className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="w-[300px] sm:w-[400px] p-0"
              onInteractOutside={() => setIsMobileMenuOpen(false)}
            >
              <div className="flex flex-col h-full">
                {/* Mobile Menu Header */}
                <div className="flex items-center justify-between p-4 border-b">
                  <h2 className="text-lg font-semibold">{t('menu')}</h2>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsMobileMenuOpen(false)}
                    aria-label={t('close')}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Navigation Links */}
                <div className="flex-1 overflow-y-auto">
                  <nav className="p-4 space-y-2">
                    {navigation.map((item) => (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={cn(
                          "flex items-center px-3 py-3 text-base font-medium rounded-lg transition-colors",
                          "hover:bg-accent hover:text-accent-foreground",
                          "focus:bg-accent focus:text-accent-foreground focus:outline-none",
                          "active:bg-accent/80"
                        )}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {item.name}
                      </Link>
                    ))}
                  </nav>

                  {/* Mobile Search */}
                  <div className="p-4 border-t">
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-muted-foreground">
                        {t('search')}
                      </h3>
                      <GlobalSearch />
                    </div>
                  </div>

                  {/* Mobile User Actions */}
                  <div className="p-4 border-t">
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-muted-foreground">
                        {t('settings')}
                      </h3>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">{t('language')}</span>
                        <LanguageToggle />
                      </div>
                      <div className="pt-2">
                        <UserNav />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Mobile Search Overlay */}
        {isSearchOpen && (
          <div className="absolute top-full left-0 right-0 bg-background border-b shadow-lg sm:hidden">
            <div className="container p-4">
              <div className="flex items-center space-x-2">
                <div className="flex-1">
                  <GlobalSearch />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsSearchOpen(false)}
                  aria-label={t('close')}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

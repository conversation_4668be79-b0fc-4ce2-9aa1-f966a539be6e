{"name": "modern-blog-workers", "version": "1.0.0", "description": "Cloudflare Workers backend for modern blog system", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --env development", "deploy:prod": "wrangler deploy --env production", "build": "tsc", "type-check": "tsc --noEmit", "db:generate": "wrangler d1 execute modern-blog-db --file=./schema.sql", "db:migrate": "wrangler d1 migrations apply modern-blog-db", "kv:create": "wrangler kv:namespace create CACHE && wrangler kv:namespace create SESSIONS", "r2:create": "wrangler r2 bucket create modern-blog-storage", "secrets:setup": "echo 'Run: wrangler secret put GITHUB_CLIENT_SECRET && wrangler secret put JWT_SECRET && wrangler secret put ADMIN_EMAILS'"}, "keywords": ["cloudflare", "workers", "blog", "api", "typescript"], "author": "Your Name", "license": "MIT", "devDependencies": {"@cloudflare/workers-types": "^4.20241127.0", "@types/jsonwebtoken": "^9.0.6", "typescript": "^5.3.3", "wrangler": "^3.78.12"}, "dependencies": {"hono": "^3.12.8", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4"}}
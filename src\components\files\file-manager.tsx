"use client"

import { useState, useRef } from "react"
import { Upload, Search, Grid, List, MoreHorizontal, Download, Trash2, <PERSON>, Co<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ResponsiveGrid } from "@/components/ui/responsive-grid"
import { formatFileSize, formatRelativeTime } from "@/lib/utils"
import type { User, FileItem } from "@/types"

interface FileManagerProps {
  user: User
}

// 模拟文件数据
const mockFiles: FileItem[] = [
  {
    id: "1",
    name: "hero-image.jpg",
    path: "/uploads/hero-image.jpg",
    url: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop",
    size: 245760,
    type: "image/jpeg",
    uploadedBy: "1",
    uploadedAt: new Date("2024-01-15"),
    isPublic: true,
  },
  {
    id: "2",
    name: "document.pdf",
    path: "/uploads/document.pdf",
    url: "/uploads/document.pdf",
    size: 1048576,
    type: "application/pdf",
    uploadedBy: "1",
    uploadedAt: new Date("2024-01-14"),
    isPublic: false,
  },
  {
    id: "3",
    name: "code-screenshot.png",
    path: "/uploads/code-screenshot.png",
    url: "https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop",
    size: 512000,
    type: "image/png",
    uploadedBy: "2",
    uploadedAt: new Date("2024-01-13"),
    isPublic: true,
  },
  {
    id: "4",
    name: "profile-avatar.jpg",
    path: "/uploads/profile-avatar.jpg",
    url: "https://avatars.githubusercontent.com/u/1?v=4",
    size: 102400,
    type: "image/jpeg",
    uploadedBy: "1",
    uploadedAt: new Date("2024-01-12"),
    isPublic: true,
  },
]

export function FileManager({ user }: FileManagerProps) {
  const [files, setFiles] = useState<FileItem[]>(mockFiles)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("")
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesType = !typeFilter || file.type.startsWith(typeFilter)
    return matchesSearch && matchesType
  })

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files
    if (!selectedFiles) return

    setIsUploading(true)
    try {
      // 这里应该调用 Cloudflare Worker API 上传文件
      for (const file of Array.from(selectedFiles)) {
        console.log("上传文件:", file.name)
        // 模拟上传过程
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 添加到文件列表
        const newFile: FileItem = {
          id: Date.now().toString(),
          name: file.name,
          path: `/uploads/${file.name}`,
          url: URL.createObjectURL(file),
          size: file.size,
          type: file.type,
          uploadedBy: user.id,
          uploadedAt: new Date(),
          isPublic: true,
        }
        setFiles(prev => [newFile, ...prev])
      }
    } catch (error) {
      console.error("上传失败:", error)
    } finally {
      setIsUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const handleDeleteFile = async (fileId: string) => {
    try {
      // 这里应该调用 API 删除文件
      console.log("删除文件:", fileId)
      setFiles(prev => prev.filter(f => f.id !== fileId))
    } catch (error) {
      console.error("删除失败:", error)
    }
  }

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url)
    // 这里可以添加 toast 提示
    console.log("已复制到剪贴板:", url)
  }

  const getFileIcon = (type: string) => {
    if (type.startsWith("image/")) return "🖼️"
    if (type.startsWith("video/")) return "🎥"
    if (type.startsWith("audio/")) return "🎵"
    if (type === "application/pdf") return "📄"
    if (type.includes("document") || type.includes("word")) return "📝"
    if (type.includes("spreadsheet") || type.includes("excel")) return "📊"
    return "📁"
  }

  return (
    <div className="space-y-6">
      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">总文件数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{files.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">图片文件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {files.filter(f => f.type.startsWith("image/")).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">文档文件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {files.filter(f => f.type.includes("pdf") || f.type.includes("document")).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">总大小</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatFileSize(files.reduce((sum, f) => sum + f.size, 0))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 工具栏 */}
      <Card>
        <CardHeader>
          <CardTitle>文件操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="搜索文件..."
                  className="pl-10 w-full sm:w-80"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="文件类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">所有类型</SelectItem>
                  <SelectItem value="image">图片</SelectItem>
                  <SelectItem value="video">视频</SelectItem>
                  <SelectItem value="audio">音频</SelectItem>
                  <SelectItem value="application">文档</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex items-center border rounded-lg">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
              
              <input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={handleFileUpload}
                accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.md"
              />
              <Button
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                <Upload className="w-4 h-4 mr-2" />
                {isUploading ? "上传中..." : "上传文件"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 文件列表 */}
      <Card>
        <CardHeader>
          <CardTitle>文件列表</CardTitle>
          <CardDescription>
            共 {filteredFiles.length} 个文件
          </CardDescription>
        </CardHeader>
        <CardContent>
          {viewMode === "grid" ? (
            <ResponsiveGrid cols={{ default: 1, sm: 2, md: 3, lg: 4 }} gap={4}>
              {filteredFiles.map((file) => (
                <Card key={file.id} className="group hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* 文件预览 */}
                      <div className="aspect-square bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                        {file.type.startsWith("image/") ? (
                          <img
                            src={file.url}
                            alt={file.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="text-4xl">{getFileIcon(file.type)}</div>
                        )}
                      </div>

                      {/* 文件信息 */}
                      <div className="space-y-1">
                        <h3 className="font-medium text-sm truncate" title={file.name}>
                          {file.name}
                        </h3>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{formatFileSize(file.size)}</span>
                          <Badge variant={file.isPublic ? "default" : "secondary"} className="text-xs">
                            {file.isPublic ? "公开" : "私有"}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {formatRelativeTime(file.uploadedAt)}
                        </p>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopyUrl(file.url)}
                        >
                          <Copy className="w-3 h-3 mr-1" />
                          复制
                        </Button>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => window.open(file.url, "_blank")}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleCopyUrl(file.url)}>
                              <Copy className="mr-2 h-4 w-4" />
                              复制链接
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              下载
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {user.role === 'admin' && (
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => handleDeleteFile(file.id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </ResponsiveGrid>
          ) : (
            <div className="space-y-2">
              {filteredFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{getFileIcon(file.type)}</div>
                    <div>
                      <h3 className="font-medium">{file.name}</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{formatFileSize(file.size)}</span>
                        <span>•</span>
                        <span>{formatRelativeTime(file.uploadedAt)}</span>
                        <Badge variant={file.isPublic ? "default" : "secondary"} className="text-xs">
                          {file.isPublic ? "公开" : "私有"}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCopyUrl(file.url)}
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      复制
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => window.open(file.url, "_blank")}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyUrl(file.url)}>
                          <Copy className="mr-2 h-4 w-4" />
                          复制链接
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="mr-2 h-4 w-4" />
                          下载
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {user.role === 'admin' && (
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => handleDeleteFile(file.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            删除
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}

          {filteredFiles.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📁</div>
              <h3 className="text-xl font-semibold mb-2">暂无文件</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || typeFilter
                  ? "没有找到符合条件的文件"
                  : "还没有上传任何文件"}
              </p>
              <Button onClick={() => fileInputRef.current?.click()}>
                <Upload className="w-4 h-4 mr-2" />
                上传第一个文件
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

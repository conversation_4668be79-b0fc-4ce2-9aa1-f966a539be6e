{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      // 确保按钮有适当的类型\n      type={!asChild && !props.type ? \"button\" : props.type}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAa;QACb,MAAM,CAAC,WAAW,CAAC,MAAM,IAAI,GAAG,WAAW,MAAM,IAAI;QACpD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({\n  className,\n  type,\n  id,\n  \"aria-label\": ariaLabel,\n  \"aria-describedby\": ariaDescribedby,\n  required,\n  ...props\n}: React.ComponentProps<\"input\">) {\n  // 确保输入框有适当的无障碍标签\n  const hasAccessibleLabel = id || ariaLabel || ariaDescribedby || props[\"aria-labelledby\"]\n\n  if (!hasAccessibleLabel && process.env.NODE_ENV === \"development\") {\n    console.warn(\n      \"Input component is missing an accessible label. \" +\n      \"Add an id (paired with a label), aria-label, aria-labelledby, or aria-describedby attribute.\"\n    )\n  }\n\n  return (\n    <input\n      type={type}\n      id={id}\n      data-slot=\"input\"\n      aria-label={ariaLabel}\n      aria-describedby={ariaDescribedby}\n      required={required}\n      aria-required={required}\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EACb,SAAS,EACT,IAAI,EACJ,EAAE,EACF,cAAc,SAAS,EACvB,oBAAoB,eAAe,EACnC,QAAQ,EACR,GAAG,OAC2B;IAC9B,iBAAiB;IACjB,MAAM,qBAAqB,MAAM,aAAa,mBAAmB,KAAK,CAAC,kBAAkB;IAEzF,IAAI,CAAC,sBAAsB,oDAAyB,eAAe;QACjE,QAAQ,IAAI,CACV,qDACA;IAEJ;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,IAAI;QACJ,aAAU;QACV,cAAY;QACZ,oBAAkB;QAClB,UAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/markdown-renderer.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect } from \"react\"\nimport ReactMarkdown from \"react-markdown\"\nimport remarkGfm from \"remark-gfm\"\nimport rehypeHighlight from \"rehype-highlight\"\nimport rehypeRaw from \"rehype-raw\"\nimport { cn } from \"@/lib/utils\"\n\n// 导入代码高亮样式\nimport \"prismjs/themes/prism-tomorrow.css\"\n\ninterface MarkdownRendererProps {\n  content: string\n  className?: string\n}\n\nexport function MarkdownRenderer({ content, className }: MarkdownRendererProps) {\n  useEffect(() => {\n    // 动态导入 Prism.js 语言支持\n    import(\"prismjs/components/prism-javascript\")\n    import(\"prismjs/components/prism-typescript\")\n    import(\"prismjs/components/prism-jsx\")\n    import(\"prismjs/components/prism-tsx\")\n    import(\"prismjs/components/prism-css\")\n    import(\"prismjs/components/prism-scss\")\n    import(\"prismjs/components/prism-json\")\n    import(\"prismjs/components/prism-bash\")\n    import(\"prismjs/components/prism-python\")\n    import(\"prismjs/components/prism-sql\")\n  }, [])\n\n  return (\n    <div className={cn(\"prose prose-gray dark:prose-invert max-w-none\", className)}>\n      <ReactMarkdown\n        remarkPlugins={[remarkGfm]}\n        rehypePlugins={[rehypeHighlight, rehypeRaw]}\n        components={{\n          // 自定义代码块渲染\n          code({ node, inline, className, children, ...props }) {\n            const match = /language-(\\w+)/.exec(className || \"\")\n            return !inline && match ? (\n              <pre className={cn(\"rounded-lg border bg-muted p-4 overflow-x-auto\", className)}>\n                <code className={className} {...props}>\n                  {children}\n                </code>\n              </pre>\n            ) : (\n              <code \n                className={cn(\n                  \"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold\",\n                  className\n                )} \n                {...props}\n              >\n                {children}\n              </code>\n            )\n          },\n          // 自定义链接渲染\n          a({ href, children, ...props }) {\n            return (\n              <a\n                href={href}\n                target={href?.startsWith(\"http\") ? \"_blank\" : undefined}\n                rel={href?.startsWith(\"http\") ? \"noopener noreferrer\" : undefined}\n                className=\"text-primary hover:underline\"\n                {...props}\n              >\n                {children}\n              </a>\n            )\n          },\n          // 自定义图片渲染\n          img({ src, alt, ...props }) {\n            return (\n              <img\n                src={src}\n                alt={alt}\n                className=\"rounded-lg border shadow-sm max-w-full h-auto\"\n                loading=\"lazy\"\n                {...props}\n              />\n            )\n          },\n          // 自定义表格渲染\n          table({ children, ...props }) {\n            return (\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full border-collapse border border-border\" {...props}>\n                  {children}\n                </table>\n              </div>\n            )\n          },\n          th({ children, ...props }) {\n            return (\n              <th className=\"border border-border bg-muted p-2 text-left font-semibold\" {...props}>\n                {children}\n              </th>\n            )\n          },\n          td({ children, ...props }) {\n            return (\n              <td className=\"border border-border p-2\" {...props}>\n                {children}\n              </td>\n            )\n          },\n          // 自定义引用块渲染\n          blockquote({ children, ...props }) {\n            return (\n              <blockquote \n                className=\"border-l-4 border-primary pl-4 italic text-muted-foreground\"\n                {...props}\n              >\n                {children}\n              </blockquote>\n            )\n          },\n          // 自定义标题渲染\n          h1({ children, ...props }) {\n            return (\n              <h1 className=\"text-3xl font-bold mt-8 mb-4 first:mt-0\" {...props}>\n                {children}\n              </h1>\n            )\n          },\n          h2({ children, ...props }) {\n            return (\n              <h2 className=\"text-2xl font-semibold mt-6 mb-3\" {...props}>\n                {children}\n              </h2>\n            )\n          },\n          h3({ children, ...props }) {\n            return (\n              <h3 className=\"text-xl font-semibold mt-5 mb-2\" {...props}>\n                {children}\n              </h3>\n            )\n          },\n          // 自定义列表渲染\n          ul({ children, ...props }) {\n            return (\n              <ul className=\"list-disc list-inside space-y-1 ml-4\" {...props}>\n                {children}\n              </ul>\n            )\n          },\n          ol({ children, ...props }) {\n            return (\n              <ol className=\"list-decimal list-inside space-y-1 ml-4\" {...props}>\n                {children}\n              </ol>\n            )\n          },\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AAiBO,SAAS,iBAAiB,EAAE,OAAO,EAAE,SAAS,EAAyB;IAC5E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;;;;;;;;;;;IAWvB,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;kBAClE,cAAA,8OAAC,wLAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,6IAAA,CAAA,UAAS;aAAC;YAC1B,eAAe;gBAAC,mJAAA,CAAA,UAAe;gBAAE,6IAAA,CAAA,UAAS;aAAC;YAC3C,YAAY;gBACV,WAAW;gBACX,MAAK,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;oBAClD,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;oBACjD,OAAO,CAAC,UAAU,sBAChB,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;kCACnE,cAAA,8OAAC;4BAAK,WAAW;4BAAY,GAAG,KAAK;sCAClC;;;;;;;;;;+CAIL,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;wBAED,GAAG,KAAK;kCAER;;;;;;gBAGP;gBACA,UAAU;gBACV,GAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;oBAC5B,qBACE,8OAAC;wBACC,MAAM;wBACN,QAAQ,MAAM,WAAW,UAAU,WAAW;wBAC9C,KAAK,MAAM,WAAW,UAAU,wBAAwB;wBACxD,WAAU;wBACT,GAAG,KAAK;kCAER;;;;;;gBAGP;gBACA,UAAU;gBACV,KAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,OAAO;oBACxB,qBACE,8OAAC;wBACC,KAAK;wBACL,KAAK;wBACL,WAAU;wBACV,SAAQ;wBACP,GAAG,KAAK;;;;;;gBAGf;gBACA,UAAU;gBACV,OAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;oBAC1B,qBACE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;4BAA+C,GAAG,KAAK;sCACrE;;;;;;;;;;;gBAIT;gBACA,IAAG,EAAE,QAAQ,EAAE,GAAG,OAAO;oBACvB,qBACE,8OAAC;wBAAG,WAAU;wBAA6D,GAAG,KAAK;kCAChF;;;;;;gBAGP;gBACA,IAAG,EAAE,QAAQ,EAAE,GAAG,OAAO;oBACvB,qBACE,8OAAC;wBAAG,WAAU;wBAA4B,GAAG,KAAK;kCAC/C;;;;;;gBAGP;gBACA,WAAW;gBACX,YAAW,EAAE,QAAQ,EAAE,GAAG,OAAO;oBAC/B,qBACE,8OAAC;wBACC,WAAU;wBACT,GAAG,KAAK;kCAER;;;;;;gBAGP;gBACA,UAAU;gBACV,IAAG,EAAE,QAAQ,EAAE,GAAG,OAAO;oBACvB,qBACE,8OAAC;wBAAG,WAAU;wBAA2C,GAAG,KAAK;kCAC9D;;;;;;gBAGP;gBACA,IAAG,EAAE,QAAQ,EAAE,GAAG,OAAO;oBACvB,qBACE,8OAAC;wBAAG,WAAU;wBAAoC,GAAG,KAAK;kCACvD;;;;;;gBAGP;gBACA,IAAG,EAAE,QAAQ,EAAE,GAAG,OAAO;oBACvB,qBACE,8OAAC;wBAAG,WAAU;wBAAmC,GAAG,KAAK;kCACtD;;;;;;gBAGP;gBACA,UAAU;gBACV,IAAG,EAAE,QAAQ,EAAE,GAAG,OAAO;oBACvB,qBACE,8OAAC;wBAAG,WAAU;wBAAwC,GAAG,KAAK;kCAC3D;;;;;;gBAGP;gBACA,IAAG,EAAE,QAAQ,EAAE,GAAG,OAAO;oBACvB,qBACE,8OAAC;wBAAG,WAAU;wBAA2C,GAAG,KAAK;kCAC9D;;;;;;gBAGP;YACF;sBAEC;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 877, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/lib/seo-analyzer.ts"], "sourcesContent": ["import { Article } from \"@/types\"\n\n/**\n * SEO 分析结果接口\n */\nexport interface SEOAnalysis {\n  score: number // 0-100 分数\n  issues: SEOIssue[]\n  suggestions: string[]\n  strengths: string[]\n}\n\nexport interface SEOIssue {\n  type: \"error\" | \"warning\" | \"info\"\n  message: string\n  field: string\n}\n\n/**\n * 分析文章的 SEO 质量\n */\nexport function analyzeArticleSEO(article: Article): SEOAnalysis {\n  const issues: SEOIssue[] = []\n  const suggestions: string[] = []\n  const strengths: string[] = []\n  let score = 100\n\n  // 检查标题\n  if (!article.title) {\n    issues.push({\n      type: \"error\",\n      message: \"文章标题不能为空\",\n      field: \"title\"\n    })\n    score -= 20\n  } else {\n    if (article.title.length < 10) {\n      issues.push({\n        type: \"warning\",\n        message: \"标题太短，建议至少10个字符\",\n        field: \"title\"\n      })\n      score -= 10\n    } else if (article.title.length > 60) {\n      issues.push({\n        type: \"warning\",\n        message: \"标题太长，建议不超过60个字符\",\n        field: \"title\"\n      })\n      score -= 5\n    } else {\n      strengths.push(\"标题长度适中\")\n    }\n  }\n\n  // 检查描述/摘要\n  const description = article.summary || article.excerpt\n  if (!description) {\n    issues.push({\n      type: \"error\",\n      message: \"缺少文章摘要或描述\",\n      field: \"description\"\n    })\n    score -= 15\n    suggestions.push(\"添加文章摘要以提升搜索引擎可见性\")\n  } else {\n    if (description.length < 50) {\n      issues.push({\n        type: \"warning\",\n        message: \"描述太短，建议至少50个字符\",\n        field: \"description\"\n      })\n      score -= 8\n    } else if (description.length > 160) {\n      issues.push({\n        type: \"warning\",\n        message: \"描述太长，建议不超过160个字符\",\n        field: \"description\"\n      })\n      score -= 5\n    } else {\n      strengths.push(\"描述长度适中\")\n    }\n  }\n\n  // 检查 URL slug\n  if (!article.slug) {\n    issues.push({\n      type: \"error\",\n      message: \"缺少 URL 别名\",\n      field: \"slug\"\n    })\n    score -= 10\n  } else {\n    if (article.slug.includes(\" \")) {\n      issues.push({\n        type: \"error\",\n        message: \"URL 别名不应包含空格\",\n        field: \"slug\"\n      })\n      score -= 5\n    }\n    if (article.slug.length > 50) {\n      issues.push({\n        type: \"warning\",\n        message: \"URL 别名太长，建议不超过50个字符\",\n        field: \"slug\"\n      })\n      score -= 3\n    }\n    if (!/^[a-z0-9-]+$/.test(article.slug)) {\n      issues.push({\n        type: \"warning\",\n        message: \"URL 别名建议只使用小写字母、数字和连字符\",\n        field: \"slug\"\n      })\n      score -= 3\n    } else {\n      strengths.push(\"URL 别名格式正确\")\n    }\n  }\n\n  // 检查标签\n  if (article.tags.length === 0) {\n    issues.push({\n      type: \"warning\",\n      message: \"没有设置标签\",\n      field: \"tags\"\n    })\n    score -= 5\n    suggestions.push(\"添加相关标签以提升文章分类和搜索性\")\n  } else if (article.tags.length > 10) {\n    issues.push({\n      type: \"warning\",\n      message: \"标签过多，建议不超过10个\",\n      field: \"tags\"\n    })\n    score -= 3\n  } else {\n    strengths.push(`设置了 ${article.tags.length} 个标签`)\n  }\n\n  // 检查分类\n  if (!article.category) {\n    issues.push({\n      type: \"warning\",\n      message: \"没有设置分类\",\n      field: \"category\"\n    })\n    score -= 5\n  } else {\n    strengths.push(\"已设置文章分类\")\n  }\n\n  // 检查内容长度\n  if (article.content.length < 300) {\n    issues.push({\n      type: \"warning\",\n      message: \"内容太短，建议至少300个字符\",\n      field: \"content\"\n    })\n    score -= 10\n    suggestions.push(\"增加内容长度以提升搜索引擎排名\")\n  } else if (article.content.length > 10000) {\n    issues.push({\n      type: \"info\",\n      message: \"内容很长，考虑分割为多篇文章\",\n      field: \"content\"\n    })\n  } else {\n    strengths.push(\"内容长度适中\")\n  }\n\n  // 检查封面图片\n  if (!article.coverImage) {\n    issues.push({\n      type: \"info\",\n      message: \"没有设置封面图片\",\n      field: \"coverImage\"\n    })\n    suggestions.push(\"添加封面图片以提升社交媒体分享效果\")\n  } else {\n    strengths.push(\"已设置封面图片\")\n  }\n\n  // 检查标题中是否包含关键词\n  const titleWords = article.title.toLowerCase().split(/\\s+/)\n  const hasKeywords = article.tags.some(tag => \n    titleWords.some(word => word.includes(tag.toLowerCase()))\n  )\n  if (!hasKeywords) {\n    suggestions.push(\"考虑在标题中包含主要关键词\")\n    score -= 3\n  } else {\n    strengths.push(\"标题包含关键词\")\n  }\n\n  // 检查内容结构（标题层级）\n  const headingMatches = article.content.match(/^#{1,6}\\s+.+$/gm)\n  if (!headingMatches || headingMatches.length < 2) {\n    issues.push({\n      type: \"info\",\n      message: \"内容缺少标题结构\",\n      field: \"content\"\n    })\n    suggestions.push(\"使用标题（H1-H6）来组织内容结构\")\n    score -= 5\n  } else {\n    strengths.push(\"内容具有良好的标题结构\")\n  }\n\n  // 检查内部链接\n  const internalLinks = article.content.match(/\\[.*?\\]\\(\\/.*?\\)/g)\n  if (!internalLinks || internalLinks.length === 0) {\n    suggestions.push(\"添加内部链接以提升网站内容关联性\")\n    score -= 2\n  } else {\n    strengths.push(\"包含内部链接\")\n  }\n\n  // 确保分数在 0-100 范围内\n  score = Math.max(0, Math.min(100, score))\n\n  return {\n    score,\n    issues,\n    suggestions,\n    strengths\n  }\n}\n\n/**\n * 获取 SEO 分数等级\n */\nexport function getSEOGrade(score: number): {\n  grade: string\n  color: string\n  description: string\n} {\n  if (score >= 90) {\n    return {\n      grade: \"A+\",\n      color: \"text-green-600\",\n      description: \"优秀\"\n    }\n  } else if (score >= 80) {\n    return {\n      grade: \"A\",\n      color: \"text-green-500\",\n      description: \"良好\"\n    }\n  } else if (score >= 70) {\n    return {\n      grade: \"B\",\n      color: \"text-yellow-500\",\n      description: \"一般\"\n    }\n  } else if (score >= 60) {\n    return {\n      grade: \"C\",\n      color: \"text-orange-500\",\n      description: \"需要改进\"\n    }\n  } else {\n    return {\n      grade: \"D\",\n      color: \"text-red-500\",\n      description: \"急需改进\"\n    }\n  }\n}\n\n/**\n * 生成 SEO 改进建议\n */\nexport function generateSEORecommendations(analysis: SEOAnalysis): string[] {\n  const recommendations: string[] = []\n\n  // 基于分数给出总体建议\n  if (analysis.score < 60) {\n    recommendations.push(\"您的文章 SEO 分数较低，建议优先解决错误和警告问题\")\n  } else if (analysis.score < 80) {\n    recommendations.push(\"您的文章 SEO 表现一般，可以通过优化获得更好的搜索排名\")\n  } else {\n    recommendations.push(\"您的文章 SEO 表现良好，继续保持这种质量\")\n  }\n\n  // 添加具体建议\n  recommendations.push(...analysis.suggestions)\n\n  // 基于问题类型给出建议\n  const errorCount = analysis.issues.filter(issue => issue.type === \"error\").length\n  const warningCount = analysis.issues.filter(issue => issue.type === \"warning\").length\n\n  if (errorCount > 0) {\n    recommendations.push(`发现 ${errorCount} 个严重问题，请优先解决`)\n  }\n  if (warningCount > 0) {\n    recommendations.push(`发现 ${warningCount} 个警告，建议逐步改进`)\n  }\n\n  return recommendations\n}\n"], "names": [], "mappings": ";;;;;AAqBO,SAAS,kBAAkB,OAAgB;IAChD,MAAM,SAAqB,EAAE;IAC7B,MAAM,cAAwB,EAAE;IAChC,MAAM,YAAsB,EAAE;IAC9B,IAAI,QAAQ;IAEZ,OAAO;IACP,IAAI,CAAC,QAAQ,KAAK,EAAE;QAClB,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,SAAS;IACX,OAAO;QACL,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,IAAI;YAC7B,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YACA,SAAS;QACX,OAAO,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,IAAI;YACpC,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YACA,SAAS;QACX,OAAO;YACL,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,UAAU;IACV,MAAM,cAAc,QAAQ,OAAO,IAAI,QAAQ,OAAO;IACtD,IAAI,CAAC,aAAa;QAChB,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,SAAS;QACT,YAAY,IAAI,CAAC;IACnB,OAAO;QACL,IAAI,YAAY,MAAM,GAAG,IAAI;YAC3B,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YACA,SAAS;QACX,OAAO,IAAI,YAAY,MAAM,GAAG,KAAK;YACnC,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YACA,SAAS;QACX,OAAO;YACL,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,cAAc;IACd,IAAI,CAAC,QAAQ,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,SAAS;IACX,OAAO;QACL,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;YAC9B,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YACA,SAAS;QACX;QACA,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI;YAC5B,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YACA,SAAS;QACX;QACA,IAAI,CAAC,eAAe,IAAI,CAAC,QAAQ,IAAI,GAAG;YACtC,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YACA,SAAS;QACX,OAAO;YACL,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,OAAO;IACP,IAAI,QAAQ,IAAI,CAAC,MAAM,KAAK,GAAG;QAC7B,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,SAAS;QACT,YAAY,IAAI,CAAC;IACnB,OAAO,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI;QACnC,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,SAAS;IACX,OAAO;QACL,UAAU,IAAI,CAAC,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACjD;IAEA,OAAO;IACP,IAAI,CAAC,QAAQ,QAAQ,EAAE;QACrB,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,SAAS;IACX,OAAO;QACL,UAAU,IAAI,CAAC;IACjB;IAEA,SAAS;IACT,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,KAAK;QAChC,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,SAAS;QACT,YAAY,IAAI,CAAC;IACnB,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,OAAO;QACzC,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;IACF,OAAO;QACL,UAAU,IAAI,CAAC;IACjB;IAEA,SAAS;IACT,IAAI,CAAC,QAAQ,UAAU,EAAE;QACvB,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,YAAY,IAAI,CAAC;IACnB,OAAO;QACL,UAAU,IAAI,CAAC;IACjB;IAEA,eAAe;IACf,MAAM,aAAa,QAAQ,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;IACrD,MAAM,cAAc,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAA,MACpC,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,IAAI,WAAW;IAEvD,IAAI,CAAC,aAAa;QAChB,YAAY,IAAI,CAAC;QACjB,SAAS;IACX,OAAO;QACL,UAAU,IAAI,CAAC;IACjB;IAEA,eAAe;IACf,MAAM,iBAAiB,QAAQ,OAAO,CAAC,KAAK,CAAC;IAC7C,IAAI,CAAC,kBAAkB,eAAe,MAAM,GAAG,GAAG;QAChD,OAAO,IAAI,CAAC;YACV,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,YAAY,IAAI,CAAC;QACjB,SAAS;IACX,OAAO;QACL,UAAU,IAAI,CAAC;IACjB;IAEA,SAAS;IACT,MAAM,gBAAgB,QAAQ,OAAO,CAAC,KAAK,CAAC;IAC5C,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;QAChD,YAAY,IAAI,CAAC;QACjB,SAAS;IACX,OAAO;QACL,UAAU,IAAI,CAAC;IACjB;IAEA,kBAAkB;IAClB,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;IAElC,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,YAAY,KAAa;IAKvC,IAAI,SAAS,IAAI;QACf,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO,IAAI,SAAS,IAAI;QACtB,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO,IAAI,SAAS,IAAI;QACtB,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO,IAAI,SAAS,IAAI;QACtB,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO;QACL,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF;AACF;AAKO,SAAS,2BAA2B,QAAqB;IAC9D,MAAM,kBAA4B,EAAE;IAEpC,aAAa;IACb,IAAI,SAAS,KAAK,GAAG,IAAI;QACvB,gBAAgB,IAAI,CAAC;IACvB,OAAO,IAAI,SAAS,KAAK,GAAG,IAAI;QAC9B,gBAAgB,IAAI,CAAC;IACvB,OAAO;QACL,gBAAgB,IAAI,CAAC;IACvB;IAEA,SAAS;IACT,gBAAgB,IAAI,IAAI,SAAS,WAAW;IAE5C,aAAa;IACb,MAAM,aAAa,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,SAAS,MAAM;IACjF,MAAM,eAAe,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,WAAW,MAAM;IAErF,IAAI,aAAa,GAAG;QAClB,gBAAgB,IAAI,CAAC,CAAC,GAAG,EAAE,WAAW,YAAY,CAAC;IACrD;IACA,IAAI,eAAe,GAAG;QACpB,gBAAgB,IAAI,CAAC,CAAC,GAAG,EAAE,aAAa,WAAW,CAAC;IACtD;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/article/seo-analyzer.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { AlertCircle, CheckCircle, Info, AlertTriangle } from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Button } from \"@/components/ui/button\"\nimport { analyzeArticleSEO, getSEOGrade, generateSEORecommendations } from \"@/lib/seo-analyzer\"\nimport type { Article } from \"@/types\"\n\ninterface SEOAnalyzerProps {\n  article: Partial<Article>\n}\n\nexport function SEOAnalyzer({ article }: SEOAnalyzerProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [analysis, setAnalysis] = useState<ReturnType<typeof analyzeArticleSEO> | null>(null)\n  \n  // 当文章内容变化时重新分析\n  useEffect(() => {\n    if (!article.title || !article.content) return\n    \n    // 创建一个完整的文章对象用于分析\n    const fullArticle: Article = {\n      id: article.id || \"draft\",\n      title: article.title || \"\",\n      slug: article.slug || \"\",\n      content: article.content || \"\",\n      excerpt: article.excerpt || \"\",\n      summary: article.summary || \"\",\n      coverImage: article.coverImage || \"\",\n      tags: article.tags || [],\n      category: article.category || \"\",\n      status: article.status || \"draft\",\n      authorId: article.authorId || \"\",\n      author: article.author || {\n        id: \"current-user\",\n        name: \"当前用户\",\n        email: \"\",\n        role: \"user\",\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      },\n      publishedAt: article.publishedAt,\n      createdAt: article.createdAt || new Date(),\n      updatedAt: article.updatedAt || new Date(),\n      viewCount: article.viewCount || 0,\n      likeCount: article.likeCount || 0,\n    }\n    \n    const result = analyzeArticleSEO(fullArticle)\n    setAnalysis(result)\n  }, [article])\n  \n  if (!analysis) return null\n  \n  const { score, issues, suggestions, strengths } = analysis\n  const { grade, color, description } = getSEOGrade(score)\n  const recommendations = generateSEORecommendations(analysis)\n  \n  return (\n    <Card>\n      <CardHeader className=\"pb-2\">\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"text-sm font-medium\">SEO 分析</CardTitle>\n          <Button \n            variant=\"ghost\" \n            size=\"sm\" \n            onClick={() => setIsOpen(!isOpen)}\n          >\n            {isOpen ? \"收起\" : \"展开\"}\n          </Button>\n        </div>\n        <CardDescription>\n          文章 SEO 质量评分\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-4\">\n          {/* 分数展示 */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              <div className={`text-2xl font-bold ${color}`}>\n                {grade}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">\n                ({score}/100) - {description}\n              </div>\n            </div>\n          </div>\n          \n          {/* 进度条 */}\n          <Progress value={score} className=\"h-2\" />\n          \n          {isOpen && (\n            <div className=\"space-y-4 pt-2\">\n              {/* 问题列表 */}\n              {issues.length > 0 && (\n                <div className=\"space-y-2\">\n                  <h4 className=\"text-sm font-medium\">需要改进的地方</h4>\n                  <div className=\"space-y-1\">\n                    {issues.map((issue, index) => (\n                      <div key={index} className=\"flex items-start gap-2 text-sm\">\n                        {issue.type === \"error\" ? (\n                          <AlertCircle className=\"h-4 w-4 text-destructive mt-0.5\" />\n                        ) : issue.type === \"warning\" ? (\n                          <AlertTriangle className=\"h-4 w-4 text-yellow-500 mt-0.5\" />\n                        ) : (\n                          <Info className=\"h-4 w-4 text-blue-500 mt-0.5\" />\n                        )}\n                        <div>\n                          <span className=\"font-medium\">{issue.field}: </span>\n                          {issue.message}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n              \n              <Separator />\n              \n              {/* 优势列表 */}\n              {strengths.length > 0 && (\n                <div className=\"space-y-2\">\n                  <h4 className=\"text-sm font-medium\">SEO 优势</h4>\n                  <div className=\"space-y-1\">\n                    {strengths.map((strength, index) => (\n                      <div key={index} className=\"flex items-start gap-2 text-sm\">\n                        <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n                        <div>{strength}</div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n              \n              <Separator />\n              \n              {/* 建议列表 */}\n              {recommendations.length > 0 && (\n                <div className=\"space-y-2\">\n                  <h4 className=\"text-sm font-medium\">改进建议</h4>\n                  <div className=\"space-y-1\">\n                    {recommendations.map((recommendation, index) => (\n                      <div key={index} className=\"flex items-start gap-2 text-sm\">\n                        <Info className=\"h-4 w-4 text-blue-500 mt-0.5\" />\n                        <div>{recommendation}</div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n              \n              {/* SEO 标签 */}\n              <div className=\"space-y-2\">\n                <h4 className=\"text-sm font-medium\">SEO 关键因素</h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  <Badge variant={article.title ? \"default\" : \"outline\"}>\n                    标题\n                  </Badge>\n                  <Badge variant={article.excerpt || article.summary ? \"default\" : \"outline\"}>\n                    描述\n                  </Badge>\n                  <Badge variant={article.slug ? \"default\" : \"outline\"}>\n                    URL\n                  </Badge>\n                  <Badge variant={article.tags && article.tags.length > 0 ? \"default\" : \"outline\"}>\n                    标签\n                  </Badge>\n                  <Badge variant={article.category ? \"default\" : \"outline\"}>\n                    分类\n                  </Badge>\n                  <Badge variant={article.coverImage ? \"default\" : \"outline\"}>\n                    图片\n                  </Badge>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAgBO,SAAS,YAAY,EAAE,OAAO,EAAoB;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+C;IAEtF,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,OAAO,EAAE;QAExC,kBAAkB;QAClB,MAAM,cAAuB;YAC3B,IAAI,QAAQ,EAAE,IAAI;YAClB,OAAO,QAAQ,KAAK,IAAI;YACxB,MAAM,QAAQ,IAAI,IAAI;YACtB,SAAS,QAAQ,OAAO,IAAI;YAC5B,SAAS,QAAQ,OAAO,IAAI;YAC5B,SAAS,QAAQ,OAAO,IAAI;YAC5B,YAAY,QAAQ,UAAU,IAAI;YAClC,MAAM,QAAQ,IAAI,IAAI,EAAE;YACxB,UAAU,QAAQ,QAAQ,IAAI;YAC9B,QAAQ,QAAQ,MAAM,IAAI;YAC1B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,QAAQ,QAAQ,MAAM,IAAI;gBACxB,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;YACA,aAAa,QAAQ,WAAW;YAChC,WAAW,QAAQ,SAAS,IAAI,IAAI;YACpC,WAAW,QAAQ,SAAS,IAAI,IAAI;YACpC,WAAW,QAAQ,SAAS,IAAI;YAChC,WAAW,QAAQ,SAAS,IAAI;QAClC;QAEA,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,oBAAiB,AAAD,EAAE;QACjC,YAAY;IACd,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG;IAClD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD,EAAE;IAClD,MAAM,kBAAkB,CAAA,GAAA,6HAAA,CAAA,6BAA0B,AAAD,EAAE;IAEnD,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsB;;;;;;0CAC3C,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU,CAAC;0CAEzB,SAAS,OAAO;;;;;;;;;;;;kCAGrB,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,mBAAmB,EAAE,OAAO;kDAC1C;;;;;;kDAEH,8OAAC;wCAAI,WAAU;;4CAAgC;4CAC3C;4CAAM;4CAAS;;;;;;;;;;;;;;;;;;sCAMvB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,OAAO;4BAAO,WAAU;;;;;;wBAEjC,wBACC,8OAAC;4BAAI,WAAU;;gCAEZ,OAAO,MAAM,GAAG,mBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;oDAAgB,WAAU;;wDACxB,MAAM,IAAI,KAAK,wBACd,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;mEACrB,MAAM,IAAI,KAAK,0BACjB,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;iFAEzB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAElB,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;;wEAAe,MAAM,KAAK;wEAAC;;;;;;;gEAC1C,MAAM,OAAO;;;;;;;;mDAVR;;;;;;;;;;;;;;;;8CAkBlB,8OAAC,qIAAA,CAAA,YAAS;;;;;gCAGT,UAAU,MAAM,GAAG,mBAClB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;sEAAK;;;;;;;mDAFE;;;;;;;;;;;;;;;;8CASlB,8OAAC,qIAAA,CAAA,YAAS;;;;;gCAGT,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,sBACpC,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;mDAFE;;;;;;;;;;;;;;;;8CAUlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,QAAQ,KAAK,GAAG,YAAY;8DAAW;;;;;;8DAGvD,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,GAAG,YAAY;8DAAW;;;;;;8DAG5E,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,QAAQ,IAAI,GAAG,YAAY;8DAAW;;;;;;8DAGtD,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY;8DAAW;;;;;;8DAGjF,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,QAAQ,QAAQ,GAAG,YAAY;8DAAW;;;;;;8DAG1D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,QAAQ,UAAU,GAAG,YAAY;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9E", "debugId": null}}, {"offset": {"line": 1622, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/article/ai-assistant.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON><PERSON>, <PERSON>, FileText, BarChart3, <PERSON><PERSON>2, <PERSON><PERSON>, Check } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Separator } from \"@/components/ui/separator\"\n\ninterface AIAssistantProps {\n  title: string\n  content: string\n  onSummaryGenerated: (summary: string) => void\n  onTagsRecommended: (tags: string[]) => void\n}\n\nexport function AIAssistant({\n  title,\n  content,\n  onSummaryGenerated,\n  onTagsRecommended,\n}: AIAssistantProps) {\n  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false)\n  const [isRecommendingTags, setIsRecommendingTags] = useState(false)\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [copiedSummary, setCopiedSummary] = useState(false)\n  \n  const [generatedSummary, setGeneratedSummary] = useState<string>(\"\")\n  const [recommendedTags, setRecommendedTags] = useState<string[]>([])\n  const [contentAnalysis, setContentAnalysis] = useState<any>(null)\n\n  // 生成摘要\n  const handleGenerateSummary = async () => {\n    if (!content.trim()) return\n    \n    setIsGeneratingSummary(true)\n    try {\n      const response = await fetch(\"/api/ai/summary\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          content,\n          options: {\n            maxLength: 150,\n            language: \"zh\",\n            style: \"formal\",\n          },\n        }),\n      })\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        setGeneratedSummary(data.data)\n        onSummaryGenerated(data.data)\n      } else {\n        console.error(\"摘要生成失败:\", data.error)\n      }\n    } catch (error) {\n      console.error(\"摘要生成错误:\", error)\n    } finally {\n      setIsGeneratingSummary(false)\n    }\n  }\n\n  // 推荐标签\n  const handleRecommendTags = async () => {\n    if (!title.trim() || !content.trim()) return\n    \n    setIsRecommendingTags(true)\n    try {\n      const response = await fetch(\"/api/ai/tags\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          title,\n          content,\n          options: {\n            language: \"zh\",\n          },\n        }),\n      })\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        setRecommendedTags(data.data)\n        onTagsRecommended(data.data)\n      } else {\n        console.error(\"标签推荐失败:\", data.error)\n      }\n    } catch (error) {\n      console.error(\"标签推荐错误:\", error)\n    } finally {\n      setIsRecommendingTags(false)\n    }\n  }\n\n  // 分析内容质量\n  const handleAnalyzeContent = async () => {\n    if (!title.trim() || !content.trim()) return\n    \n    setIsAnalyzing(true)\n    try {\n      const response = await fetch(\"/api/ai/analyze\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          title,\n          content,\n        }),\n      })\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        setContentAnalysis(data.data)\n      } else {\n        console.error(\"内容分析失败:\", data.error)\n      }\n    } catch (error) {\n      console.error(\"内容分析错误:\", error)\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  // 复制摘要\n  const handleCopySummary = async () => {\n    if (!generatedSummary) return\n    \n    try {\n      await navigator.clipboard.writeText(generatedSummary)\n      setCopiedSummary(true)\n      setTimeout(() => setCopiedSummary(false), 2000)\n    } catch (error) {\n      console.error(\"复制失败:\", error)\n    }\n  }\n\n  const canUseAI = title.trim() && content.trim()\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Sparkles className=\"h-5 w-5 text-blue-500\" />\n          AI 助手\n        </CardTitle>\n        <CardDescription>\n          使用 AI 生成摘要、推荐标签和分析内容质量\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <Tabs defaultValue=\"summary\" className=\"space-y-4\">\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"summary\">摘要生成</TabsTrigger>\n            <TabsTrigger value=\"tags\">标签推荐</TabsTrigger>\n            <TabsTrigger value=\"analysis\">内容分析</TabsTrigger>\n          </TabsList>\n          \n          <TabsContent value=\"summary\" className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <Button\n                onClick={handleGenerateSummary}\n                disabled={!canUseAI || isGeneratingSummary}\n                className=\"w-full\"\n              >\n                {isGeneratingSummary ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    生成中...\n                  </>\n                ) : (\n                  <>\n                    <FileText className=\"w-4 h-4 mr-2\" />\n                    生成 AI 摘要\n                  </>\n                )}\n              </Button>\n              \n              {generatedSummary && (\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium\">生成的摘要:</span>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={handleCopySummary}\n                    >\n                      {copiedSummary ? (\n                        <Check className=\"w-3 h-3\" />\n                      ) : (\n                        <Copy className=\"w-3 h-3\" />\n                      )}\n                    </Button>\n                  </div>\n                  <div className=\"p-3 bg-muted rounded-lg text-sm\">\n                    {generatedSummary}\n                  </div>\n                </div>\n              )}\n            </div>\n          </TabsContent>\n          \n          <TabsContent value=\"tags\" className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <Button\n                onClick={handleRecommendTags}\n                disabled={!canUseAI || isRecommendingTags}\n                className=\"w-full\"\n              >\n                {isRecommendingTags ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    推荐中...\n                  </>\n                ) : (\n                  <>\n                    <Tag className=\"w-4 h-4 mr-2\" />\n                    推荐标签\n                  </>\n                )}\n              </Button>\n              \n              {recommendedTags.length > 0 && (\n                <div className=\"space-y-2\">\n                  <span className=\"text-sm font-medium\">推荐的标签:</span>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {recommendedTags.map((tag) => (\n                      <Badge\n                        key={tag}\n                        variant=\"secondary\"\n                        className=\"cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors\"\n                        onClick={() => onTagsRecommended([tag])}\n                      >\n                        {tag}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </TabsContent>\n          \n          <TabsContent value=\"analysis\" className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <Button\n                onClick={handleAnalyzeContent}\n                disabled={!canUseAI || isAnalyzing}\n                className=\"w-full\"\n              >\n                {isAnalyzing ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    分析中...\n                  </>\n                ) : (\n                  <>\n                    <BarChart3 className=\"w-4 h-4 mr-2\" />\n                    分析内容质量\n                  </>\n                )}\n              </Button>\n              \n              {contentAnalysis && (\n                <div className=\"space-y-4\">\n                  {/* 总体分数 */}\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm font-medium\">内容质量分数</span>\n                      <span className=\"text-lg font-bold text-primary\">\n                        {contentAnalysis.score}/100\n                      </span>\n                    </div>\n                    <Progress value={contentAnalysis.score} className=\"h-2\" />\n                  </div>\n                  \n                  <Separator />\n                  \n                  {/* 详细分析 */}\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <span className=\"font-medium\">可读性:</span>\n                      <span className=\"ml-2\">{contentAnalysis.readabilityScore}/100</span>\n                    </div>\n                    <div>\n                      <span className=\"font-medium\">情感分数:</span>\n                      <span className=\"ml-2\">{contentAnalysis.sentimentScore}/100</span>\n                    </div>\n                  </div>\n                  \n                  {/* 优势 */}\n                  {contentAnalysis.strengths.length > 0 && (\n                    <div className=\"space-y-2\">\n                      <span className=\"text-sm font-medium text-green-600\">优势:</span>\n                      <ul className=\"text-sm space-y-1\">\n                        {contentAnalysis.strengths.map((strength: string, index: number) => (\n                          <li key={index} className=\"flex items-start gap-2\">\n                            <span className=\"text-green-500\">✓</span>\n                            {strength}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                  \n                  {/* 建议 */}\n                  {contentAnalysis.suggestions.length > 0 && (\n                    <div className=\"space-y-2\">\n                      <span className=\"text-sm font-medium text-orange-600\">改进建议:</span>\n                      <ul className=\"text-sm space-y-1\">\n                        {contentAnalysis.suggestions.map((suggestion: string, index: number) => (\n                          <li key={index} className=\"flex items-start gap-2\">\n                            <span className=\"text-orange-500\">•</span>\n                            {suggestion}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          </TabsContent>\n        </Tabs>\n        \n        {!canUseAI && (\n          <div className=\"text-center py-4 text-sm text-muted-foreground\">\n            请先输入标题和内容以使用 AI 功能\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAkBO,SAAS,YAAY,EAC1B,KAAK,EACL,OAAO,EACP,kBAAkB,EAClB,iBAAiB,EACA;IACjB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAE5D,OAAO;IACP,MAAM,wBAAwB;QAC5B,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,uBAAuB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,SAAS;wBACP,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,oBAAoB,KAAK,IAAI;gBAC7B,mBAAmB,KAAK,IAAI;YAC9B,OAAO;gBACL,QAAQ,KAAK,CAAC,WAAW,KAAK,KAAK;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,OAAO;IACP,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;QAEtC,sBAAsB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA,SAAS;wBACP,UAAU;oBACZ;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,IAAI;gBAC5B,kBAAkB,KAAK,IAAI;YAC7B,OAAO;gBACL,QAAQ,KAAK,CAAC,WAAW,KAAK,KAAK;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,SAAS;IACT,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;QAEtC,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,IAAI;YAC9B,OAAO;gBACL,QAAQ,KAAK,CAAC,WAAW,KAAK,KAAK;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;IACP,MAAM,oBAAoB;QACxB,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,iBAAiB;YACjB,WAAW,IAAM,iBAAiB,QAAQ;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,WAAW,MAAM,IAAI,MAAM,QAAQ,IAAI;IAE7C,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAA0B;;;;;;;kCAGhD,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC,gIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAU,WAAU;;0CACrC,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAU;;;;;;kDAC7B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAO;;;;;;kDAC1B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;;;;;;;0CAGhC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,YAAY;4CACvB,WAAU;sDAET,oCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;wCAM1C,kCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;sEAER,8BACC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;qFAEjB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAItB,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;0CAOX,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;0CAClC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,YAAY;4CACvB,WAAU;sDAET,mCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;wCAMrC,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,8OAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,oBACpB,8OAAC,iIAAA,CAAA,QAAK;4DAEJ,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,kBAAkB;oEAAC;iEAAI;sEAErC;2DALI;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAcnB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,YAAY;4CACvB,WAAU;sDAET,4BACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,kNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;wCAM3C,iCACC,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,8OAAC;oEAAK,WAAU;;wEACb,gBAAgB,KAAK;wEAAC;;;;;;;;;;;;;sEAG3B,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,OAAO,gBAAgB,KAAK;4DAAE,WAAU;;;;;;;;;;;;8DAGpD,8OAAC,qIAAA,CAAA,YAAS;;;;;8DAGV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,8OAAC;oEAAK,WAAU;;wEAAQ,gBAAgB,gBAAgB;wEAAC;;;;;;;;;;;;;sEAE3D,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,8OAAC;oEAAK,WAAU;;wEAAQ,gBAAgB,cAAc;wEAAC;;;;;;;;;;;;;;;;;;;gDAK1D,gBAAgB,SAAS,CAAC,MAAM,GAAG,mBAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,8OAAC;4DAAG,WAAU;sEACX,gBAAgB,SAAS,CAAC,GAAG,CAAC,CAAC,UAAkB,sBAChD,8OAAC;oEAAe,WAAU;;sFACxB,8OAAC;4EAAK,WAAU;sFAAiB;;;;;;wEAChC;;mEAFM;;;;;;;;;;;;;;;;gDAUhB,gBAAgB,WAAW,CAAC,MAAM,GAAG,mBACpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAsC;;;;;;sEACtD,8OAAC;4DAAG,WAAU;sEACX,gBAAgB,WAAW,CAAC,GAAG,CAAC,CAAC,YAAoB,sBACpD,8OAAC;oEAAe,WAAU;;sFACxB,8OAAC;4EAAK,WAAU;sFAAkB;;;;;;wEACjC;;mEAFM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAc1B,CAAC,0BACA,8OAAC;wBAAI,WAAU;kCAAiD;;;;;;;;;;;;;;;;;;AAO1E", "debugId": null}}, {"offset": {"line": 2375, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/lib/mock-data.ts"], "sourcesContent": ["import { Article, User, Category, Tag, FriendLink } from \"@/types\"\n\n// 模拟用户数据\nexport const mockUsers: User[] = [\n  {\n    id: \"1\",\n    name: \"张三\",\n    email: \"<PERSON><PERSON><PERSON>@example.com\",\n    avatar: \"https://avatars.githubusercontent.com/u/1?v=4\",\n    role: \"admin\",\n    createdAt: new Date(\"2024-01-01\"),\n    updatedAt: new Date(\"2024-01-01\"),\n  },\n  {\n    id: \"2\", \n    name: \"李四\",\n    email: \"<EMAIL>\",\n    avatar: \"https://avatars.githubusercontent.com/u/2?v=4\",\n    role: \"collaborator\",\n    createdAt: new Date(\"2024-01-02\"),\n    updatedAt: new Date(\"2024-01-02\"),\n  },\n]\n\n// 模拟分类数据\nexport const mockCategories: Category[] = [\n  {\n    id: \"1\",\n    name: \"技术\",\n    slug: \"tech\",\n    description: \"技术相关文章\",\n    color: \"#3b82f6\",\n    articleCount: 8,\n  },\n  {\n    id: \"2\",\n    name: \"生活\",\n    slug: \"life\", \n    description: \"生活感悟和经验分享\",\n    color: \"#10b981\",\n    articleCount: 3,\n  },\n  {\n    id: \"3\",\n    name: \"教程\",\n    slug: \"tutorial\",\n    description: \"各种教程和指南\",\n    color: \"#f59e0b\",\n    articleCount: 4,\n  },\n]\n\n// 模拟标签数据\nexport const mockTags: Tag[] = [\n  { id: \"1\", name: \"Next.js\", slug: \"nextjs\", color: \"#000000\", articleCount: 5 },\n  { id: \"2\", name: \"React\", slug: \"react\", color: \"#61dafb\", articleCount: 6 },\n  { id: \"3\", name: \"TypeScript\", slug: \"typescript\", color: \"#3178c6\", articleCount: 4 },\n  { id: \"4\", name: \"Tailwind CSS\", slug: \"tailwindcss\", color: \"#06b6d4\", articleCount: 3 },\n  { id: \"5\", name: \"JavaScript\", slug: \"javascript\", color: \"#f7df1e\", articleCount: 7 },\n  { id: \"6\", name: \"CSS\", slug: \"css\", color: \"#1572b6\", articleCount: 2 },\n  { id: \"7\", name: \"HTML\", slug: \"html\", color: \"#e34f26\", articleCount: 2 },\n  { id: \"8\", name: \"Node.js\", slug: \"nodejs\", color: \"#339933\", articleCount: 3 },\n]\n\n// 模拟文章数据\nexport const mockArticles: Article[] = [\n  {\n    id: \"1\",\n    title: \"Next.js 14 完整指南：从入门到精通\",\n    slug: \"nextjs-14-complete-guide\",\n    content: `# Next.js 14 完整指南\n\nNext.js 14 是一个强大的 React 框架，提供了许多开箱即用的功能...\n\n## 主要特性\n\n- App Router\n- Server Components  \n- Streaming\n- 内置优化\n\n## 安装和设置\n\n\\`\\`\\`bash\nnpx create-next-app@latest my-app\ncd my-app\nnpm run dev\n\\`\\`\\`\n\n这是一个非常详细的教程，涵盖了 Next.js 14 的所有重要概念。`,\n    excerpt: \"深入了解 Next.js 14 的新特性和最佳实践，从基础概念到高级应用。\",\n    summary: \"本文全面介绍了 Next.js 14 框架的核心特性，包括 App Router、Server Components 等新功能，适合想要学习现代 React 开发的开发者。\",\n    coverImage: \"https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop\",\n    tags: [\"Next.js\", \"React\", \"TypeScript\"],\n    category: \"技术\",\n    status: \"published\",\n    authorId: \"1\",\n    author: mockUsers[0],\n    publishedAt: new Date(\"2024-01-15\"),\n    createdAt: new Date(\"2024-01-10\"),\n    updatedAt: new Date(\"2024-01-15\"),\n    viewCount: 1234,\n    likeCount: 89,\n  },\n  {\n    id: \"2\",\n    title: \"现代化 CSS 布局技巧\",\n    slug: \"modern-css-layout-techniques\",\n    content: `# 现代化 CSS 布局技巧\n\nCSS 布局已经发生了巨大的变化...\n\n## Grid 布局\n\n\\`\\`\\`css\n.container {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n}\n\\`\\`\\`\n\n## Flexbox 布局\n\n\\`\\`\\`css\n.flex-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\\`\\`\\``,\n    excerpt: \"探索现代 CSS 布局技术，包括 Grid、Flexbox 和容器查询。\",\n    summary: \"介绍了现代 CSS 布局的最佳实践，重点讲解 Grid 和 Flexbox 的使用场景和技巧。\",\n    coverImage: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop\",\n    tags: [\"CSS\", \"HTML\"],\n    category: \"技术\",\n    status: \"published\",\n    authorId: \"1\",\n    author: mockUsers[0],\n    publishedAt: new Date(\"2024-01-12\"),\n    createdAt: new Date(\"2024-01-08\"),\n    updatedAt: new Date(\"2024-01-12\"),\n    viewCount: 856,\n    likeCount: 67,\n  },\n  {\n    id: \"3\",\n    title: \"TypeScript 高级类型系统详解\",\n    slug: \"typescript-advanced-types\",\n    content: `# TypeScript 高级类型系统详解\n\nTypeScript 的类型系统非常强大...\n\n## 泛型\n\n\\`\\`\\`typescript\nfunction identity<T>(arg: T): T {\n  return arg;\n}\n\\`\\`\\`\n\n## 条件类型\n\n\\`\\`\\`typescript\ntype NonNullable<T> = T extends null | undefined ? never : T;\n\\`\\`\\``,\n    excerpt: \"深入理解 TypeScript 的高级类型特性，提升代码质量和开发效率。\",\n    summary: \"详细讲解了 TypeScript 的高级类型特性，包括泛型、条件类型、映射类型等概念。\",\n    coverImage: \"https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop\",\n    tags: [\"TypeScript\", \"JavaScript\"],\n    category: \"技术\",\n    status: \"published\",\n    authorId: \"2\",\n    author: mockUsers[1],\n    publishedAt: new Date(\"2024-01-10\"),\n    createdAt: new Date(\"2024-01-05\"),\n    updatedAt: new Date(\"2024-01-10\"),\n    viewCount: 723,\n    likeCount: 54,\n  },\n  {\n    id: \"4\",\n    title: \"我的编程学习之路\",\n    slug: \"my-programming-journey\",\n    content: `# 我的编程学习之路\n\n回顾我从零开始学习编程的经历...\n\n## 起步阶段\n\n最开始接触的是 HTML 和 CSS...\n\n## 进阶阶段\n\n后来开始学习 JavaScript...\n\n## 现在\n\n现在主要专注于 React 和 Node.js 开发...`,\n    excerpt: \"分享我从编程小白到全栈开发者的成长历程和心得体会。\",\n    summary: \"作者分享了自己学习编程的完整历程，从基础的 HTML/CSS 到现在的全栈开发，给初学者很多启发。\",\n    coverImage: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=400&fit=crop\",\n    tags: [\"JavaScript\", \"React\", \"Node.js\"],\n    category: \"生活\",\n    status: \"published\",\n    authorId: \"1\",\n    author: mockUsers[0],\n    publishedAt: new Date(\"2024-01-08\"),\n    createdAt: new Date(\"2024-01-03\"),\n    updatedAt: new Date(\"2024-01-08\"),\n    viewCount: 445,\n    likeCount: 32,\n  },\n  {\n    id: \"5\",\n    title: \"Tailwind CSS 实用技巧集合\",\n    slug: \"tailwind-css-tips-and-tricks\",\n    content: `# Tailwind CSS 实用技巧集合\n\n收集了一些 Tailwind CSS 的实用技巧...\n\n## 自定义配置\n\n\\`\\`\\`javascript\nmodule.exports = {\n  theme: {\n    extend: {\n      colors: {\n        primary: '#3b82f6',\n      }\n    }\n  }\n}\n\\`\\`\\`\n\n## 响应式设计\n\n\\`\\`\\`html\n<div class=\"w-full md:w-1/2 lg:w-1/3\">\n  响应式容器\n</div>\n\\`\\`\\``,\n    excerpt: \"整理了 Tailwind CSS 开发中的实用技巧和最佳实践。\",\n    summary: \"汇总了 Tailwind CSS 在实际项目中的使用技巧，包括自定义配置、响应式设计等内容。\",\n    coverImage: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=400&fit=crop\",\n    tags: [\"Tailwind CSS\", \"CSS\"],\n    category: \"教程\",\n    status: \"published\",\n    authorId: \"2\",\n    author: mockUsers[1],\n    publishedAt: new Date(\"2024-01-05\"),\n    createdAt: new Date(\"2024-01-01\"),\n    updatedAt: new Date(\"2024-01-05\"),\n    viewCount: 612,\n    likeCount: 43,\n  },\n]\n\n// 获取文章列表的函数\nexport function getArticles(options?: {\n  page?: number\n  limit?: number\n  category?: string\n  tag?: string\n  search?: string\n  status?: string\n}) {\n  const {\n    page = 1,\n    limit = 10,\n    category,\n    tag,\n    search,\n    status = \"published\"\n  } = options || {}\n\n  let filteredArticles = mockArticles.filter(article => \n    article.status === status\n  )\n\n  // 分类筛选\n  if (category) {\n    filteredArticles = filteredArticles.filter(article => \n      article.category === category\n    )\n  }\n\n  // 标签筛选\n  if (tag) {\n    filteredArticles = filteredArticles.filter(article => \n      article.tags.includes(tag)\n    )\n  }\n\n  // 搜索筛选\n  if (search) {\n    const searchLower = search.toLowerCase()\n    filteredArticles = filteredArticles.filter(article => \n      article.title.toLowerCase().includes(searchLower) ||\n      article.content.toLowerCase().includes(searchLower) ||\n      article.excerpt?.toLowerCase().includes(searchLower)\n    )\n  }\n\n  // 排序（按发布时间倒序）\n  filteredArticles.sort((a, b) => {\n    const dateA = a.publishedAt || a.createdAt\n    const dateB = b.publishedAt || b.createdAt\n    return dateB.getTime() - dateA.getTime()\n  })\n\n  // 分页\n  const total = filteredArticles.length\n  const totalPages = Math.ceil(total / limit)\n  const offset = (page - 1) * limit\n  const articles = filteredArticles.slice(offset, offset + limit)\n\n  return {\n    articles,\n    pagination: {\n      page,\n      limit,\n      total,\n      totalPages,\n    }\n  }\n}\n\n// 根据 slug 获取文章\nexport function getArticleBySlug(slug: string) {\n  return mockArticles.find(article => article.slug === slug)\n}\n\n// 模拟友情链接数据\nexport const mockFriendLinks: FriendLink[] = [\n  {\n    id: \"1\",\n    name: \"Next.js\",\n    url: \"https://nextjs.org\",\n    description: \"React 框架，用于构建现代化 Web 应用\",\n    avatar: \"https://nextjs.org/favicon.ico\",\n    category: \"框架\",\n    status: \"active\",\n    createdAt: new Date(\"2023-01-01\"),\n    updatedAt: new Date(\"2023-01-01\"),\n    order: 1,\n  },\n  {\n    id: \"2\",\n    name: \"React\",\n    url: \"https://reactjs.org\",\n    description: \"用于构建用户界面的 JavaScript 库\",\n    avatar: \"https://reactjs.org/favicon.ico\",\n    category: \"框架\",\n    status: \"active\",\n    createdAt: new Date(\"2023-01-02\"),\n    updatedAt: new Date(\"2023-01-02\"),\n    order: 2,\n  },\n  {\n    id: \"3\",\n    name: \"TypeScript\",\n    url: \"https://www.typescriptlang.org\",\n    description: \"JavaScript 的超集，添加了类型系统\",\n    avatar: \"https://www.typescriptlang.org/favicon.ico\",\n    category: \"语言\",\n    status: \"active\",\n    createdAt: new Date(\"2023-01-03\"),\n    updatedAt: new Date(\"2023-01-03\"),\n    order: 3,\n  },\n  {\n    id: \"4\",\n    name: \"Tailwind CSS\",\n    url: \"https://tailwindcss.com\",\n    description: \"功能类优先的 CSS 框架\",\n    avatar: \"https://tailwindcss.com/favicon.ico\",\n    category: \"样式\",\n    status: \"active\",\n    createdAt: new Date(\"2023-01-04\"),\n    updatedAt: new Date(\"2023-01-04\"),\n    order: 4,\n  },\n  {\n    id: \"5\",\n    name: \"Vercel\",\n    url: \"https://vercel.com\",\n    description: \"前端云平台，用于部署 Web 应用\",\n    avatar: \"https://vercel.com/favicon.ico\",\n    category: \"平台\",\n    status: \"active\",\n    createdAt: new Date(\"2023-01-05\"),\n    updatedAt: new Date(\"2023-01-05\"),\n    order: 5,\n  },\n  {\n    id: \"6\",\n    name: \"GitHub\",\n    url: \"https://github.com\",\n    description: \"代码托管平台\",\n    avatar: \"https://github.com/favicon.ico\",\n    category: \"平台\",\n    status: \"active\",\n    createdAt: new Date(\"2023-01-06\"),\n    updatedAt: new Date(\"2023-01-06\"),\n    order: 6,\n  },\n  {\n    id: \"7\",\n    name: \"MDN Web Docs\",\n    url: \"https://developer.mozilla.org\",\n    description: \"Web 技术文档\",\n    avatar: \"https://developer.mozilla.org/favicon.ico\",\n    category: \"文档\",\n    status: \"active\",\n    createdAt: new Date(\"2023-01-07\"),\n    updatedAt: new Date(\"2023-01-07\"),\n    order: 7,\n  },\n  {\n    id: \"8\",\n    name: \"CSS-Tricks\",\n    url: \"https://css-tricks.com\",\n    description: \"CSS 技巧和教程\",\n    avatar: \"https://css-tricks.com/favicon.ico\",\n    category: \"博客\",\n    status: \"active\",\n    createdAt: new Date(\"2023-01-08\"),\n    updatedAt: new Date(\"2023-01-08\"),\n    order: 8,\n  },\n]\n\n// 获取友情链接\nexport function getFriendLinks({\n  category,\n  status = \"active\",\n}: {\n  category?: string\n  status?: \"active\" | \"inactive\" | \"pending\" | \"all\"\n} = {}) {\n  let links = [...mockFriendLinks]\n\n  // 按分类筛选\n  if (category) {\n    links = links.filter(link => link.category === category)\n  }\n\n  // 按状态筛选\n  if (status !== \"all\") {\n    links = links.filter(link => link.status === status)\n  }\n\n  // 按排序字段排序\n  links.sort((a, b) => a.order - b.order)\n\n  return links\n}\n\n// 获取友情链接分类\nexport function getFriendLinkCategories() {\n  const categories = mockFriendLinks.map(link => link.category)\n  return [...new Set(categories)]\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,iBAA6B;IACxC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,cAAc;IAChB;CACD;AAGM,MAAM,WAAkB;IAC7B;QAAE,IAAI;QAAK,MAAM;QAAW,MAAM;QAAU,OAAO;QAAW,cAAc;IAAE;IAC9E;QAAE,IAAI;QAAK,MAAM;QAAS,MAAM;QAAS,OAAO;QAAW,cAAc;IAAE;IAC3E;QAAE,IAAI;QAAK,MAAM;QAAc,MAAM;QAAc,OAAO;QAAW,cAAc;IAAE;IACrF;QAAE,IAAI;QAAK,MAAM;QAAgB,MAAM;QAAe,OAAO;QAAW,cAAc;IAAE;IACxF;QAAE,IAAI;QAAK,MAAM;QAAc,MAAM;QAAc,OAAO;QAAW,cAAc;IAAE;IACrF;QAAE,IAAI;QAAK,MAAM;QAAO,MAAM;QAAO,OAAO;QAAW,cAAc;IAAE;IACvE;QAAE,IAAI;QAAK,MAAM;QAAQ,MAAM;QAAQ,OAAO;QAAW,cAAc;IAAE;IACzE;QAAE,IAAI;QAAK,MAAM;QAAW,MAAM;QAAU,OAAO;QAAW,cAAc;IAAE;CAC/E;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;mCAmBqB,CAAC;QAChC,SAAS;QACT,SAAS;QACT,YAAY;QACZ,MAAM;YAAC;YAAW;YAAS;SAAa;QACxC,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,SAAS,CAAC,EAAE;QACpB,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsBR,CAAC;QACH,SAAS;QACT,SAAS;QACT,YAAY;QACZ,MAAM;YAAC;YAAO;SAAO;QACrB,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,SAAS,CAAC,EAAE;QACpB,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;;;;MAgBR,CAAC;QACH,SAAS;QACT,SAAS;QACT,YAAY;QACZ,MAAM;YAAC;YAAc;SAAa;QAClC,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,SAAS,CAAC,EAAE;QACpB,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;;6BAce,CAAC;QAC1B,SAAS;QACT,SAAS;QACT,YAAY;QACZ,MAAM;YAAC;YAAc;YAAS;SAAU;QACxC,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,SAAS,CAAC,EAAE;QACpB,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;MAwBR,CAAC;QACH,SAAS;QACT,SAAS;QACT,YAAY;QACZ,MAAM;YAAC;YAAgB;SAAM;QAC7B,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,SAAS,CAAC,EAAE;QACpB,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,WAAW;IACb;CACD;AAGM,SAAS,YAAY,OAO3B;IACC,MAAM,EACJ,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,QAAQ,EACR,GAAG,EACH,MAAM,EACN,SAAS,WAAW,EACrB,GAAG,WAAW,CAAC;IAEhB,IAAI,mBAAmB,aAAa,MAAM,CAAC,CAAA,UACzC,QAAQ,MAAM,KAAK;IAGrB,OAAO;IACP,IAAI,UAAU;QACZ,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,QAAQ,KAAK;IAEzB;IAEA,OAAO;IACP,IAAI,KAAK;QACP,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,IAAI,CAAC,QAAQ,CAAC;IAE1B;IAEA,OAAO;IACP,IAAI,QAAQ;QACV,MAAM,cAAc,OAAO,WAAW;QACtC,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,QAAQ,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACvC,QAAQ,OAAO,EAAE,cAAc,SAAS;IAE5C;IAEA,cAAc;IACd,iBAAiB,IAAI,CAAC,CAAC,GAAG;QACxB,MAAM,QAAQ,EAAE,WAAW,IAAI,EAAE,SAAS;QAC1C,MAAM,QAAQ,EAAE,WAAW,IAAI,EAAE,SAAS;QAC1C,OAAO,MAAM,OAAO,KAAK,MAAM,OAAO;IACxC;IAEA,KAAK;IACL,MAAM,QAAQ,iBAAiB,MAAM;IACrC,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;IACrC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAC5B,MAAM,WAAW,iBAAiB,KAAK,CAAC,QAAQ,SAAS;IAEzD,OAAO;QACL;QACA,YAAY;YACV;YACA;YACA;YACA;QACF;IACF;AACF;AAGO,SAAS,iBAAiB,IAAY;IAC3C,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,KAAK;AACvD;AAGO,MAAM,kBAAgC;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;CACD;AAGM,SAAS,eAAe,EAC7B,QAAQ,EACR,SAAS,QAAQ,EAIlB,GAAG,CAAC,CAAC;IACJ,IAAI,QAAQ;WAAI;KAAgB;IAEhC,QAAQ;IACR,IAAI,UAAU;QACZ,QAAQ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IACjD;IAEA,QAAQ;IACR,IAAI,WAAW,OAAO;QACpB,QAAQ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IAC/C;IAEA,UAAU;IACV,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;IAEtC,OAAO;AACT;AAGO,SAAS;IACd,MAAM,aAAa,gBAAgB,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAC5D,OAAO;WAAI,IAAI,IAAI;KAAY;AACjC", "debugId": null}}, {"offset": {"line": 2865, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/lib/api/articles.ts"], "sourcesContent": ["import { Article } from '@/types'\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''\n\nexport interface CreateArticleData {\n  title: string\n  content: string\n  excerpt?: string\n  category: string\n  tags: string[]\n  status: 'draft' | 'published' | 'archived'\n  cover_image?: string\n}\n\nexport interface UpdateArticleData extends Partial<CreateArticleData> {\n  id: string\n}\n\nexport interface ArticleListParams {\n  page?: number\n  limit?: number\n  status?: string\n  category?: string\n  author_id?: string\n}\n\nexport interface ApiResponse<T> {\n  success: boolean\n  data?: T\n  error?: string\n  message?: string\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[]\n  pagination: {\n    page: number\n    limit: number\n    total: number\n    totalPages: number\n  }\n}\n\n/**\n * 文章 API 客户端\n */\nexport class ArticlesAPI {\n  private baseUrl: string\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl\n  }\n\n  /**\n   * 创建文章\n   */\n  async createArticle(data: CreateArticleData): Promise<ApiResponse<Article>> {\n    const response = await fetch(`${this.baseUrl}/api/articles`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    return response.json()\n  }\n\n  /**\n   * 获取文章列表\n   */\n  async getArticles(params: ArticleListParams = {}): Promise<ApiResponse<PaginatedResponse<Article>>> {\n    const searchParams = new URLSearchParams()\n    \n    if (params.page) searchParams.set('page', String(params.page))\n    if (params.limit) searchParams.set('limit', String(params.limit))\n    if (params.status) searchParams.set('status', params.status)\n    if (params.category) searchParams.set('category', params.category)\n    if (params.author_id) searchParams.set('author_id', params.author_id)\n\n    const response = await fetch(`${this.baseUrl}/api/articles?${searchParams}`)\n    return response.json()\n  }\n\n  /**\n   * 根据 slug 获取文章\n   */\n  async getArticleBySlug(slug: string): Promise<ApiResponse<Article>> {\n    const response = await fetch(`${this.baseUrl}/api/articles/${slug}`)\n    return response.json()\n  }\n\n  /**\n   * 更新文章\n   */\n  async updateArticle(id: string, data: Partial<CreateArticleData>): Promise<ApiResponse<Article>> {\n    const response = await fetch(`${this.baseUrl}/api/articles/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    return response.json()\n  }\n\n  /**\n   * 删除文章\n   */\n  async deleteArticle(id: string): Promise<ApiResponse<{ message: string }>> {\n    const response = await fetch(`${this.baseUrl}/api/articles/${id}`, {\n      method: 'DELETE',\n    })\n\n    return response.json()\n  }\n\n  /**\n   * 搜索文章\n   */\n  async searchArticles(query: string, params: any = {}): Promise<ApiResponse<any>> {\n    const searchParams = new URLSearchParams({ q: query, ...params })\n    const response = await fetch(`${this.baseUrl}/api/search?${searchParams}`)\n    return response.json()\n  }\n}\n\n// 默认实例\nexport const articlesAPI = new ArticlesAPI()\n\n// 便捷函数\nexport const {\n  createArticle,\n  getArticles,\n  getArticleBySlug,\n  updateArticle,\n  deleteArticle,\n  searchArticles,\n} = articlesAPI\n\n/**\n * 数据格式转换函数\n * 将后端返回的数据格式转换为前端期望的格式\n */\nexport function transformArticleFromAPI(apiArticle: any): Article {\n  return {\n    id: apiArticle.id,\n    title: apiArticle.title,\n    slug: apiArticle.slug,\n    content: apiArticle.content,\n    excerpt: apiArticle.excerpt,\n    summary: apiArticle.summary,\n    coverImage: apiArticle.cover_image,\n    tags: Array.isArray(apiArticle.tags) ? apiArticle.tags : [],\n    category: apiArticle.category,\n    status: apiArticle.status,\n    authorId: apiArticle.author_id,\n    author: {\n      id: apiArticle.author_id,\n      name: 'Unknown', // 需要从用户 API 获取\n      email: '',\n      role: 'user',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n    publishedAt: apiArticle.published_at ? new Date(apiArticle.published_at) : undefined,\n    createdAt: new Date(apiArticle.created_at),\n    updatedAt: new Date(apiArticle.updated_at),\n    viewCount: apiArticle.view_count || 0,\n    likeCount: apiArticle.like_count || 0,\n  }\n}\n\n/**\n * 将前端文章数据转换为 API 格式\n */\nexport function transformArticleToAPI(article: Partial<Article>): Partial<CreateArticleData> {\n  return {\n    title: article.title,\n    content: article.content,\n    excerpt: article.excerpt,\n    category: article.category,\n    tags: article.tags,\n    status: article.status,\n    cover_image: article.coverImage,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AA4CjD,MAAM;IACH,QAAe;IAEvB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;GAEC,GACD,MAAM,cAAc,IAAuB,EAAiC;QAC1E,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YAC3D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,YAAY,SAA4B,CAAC,CAAC,EAAoD;QAClG,MAAM,eAAe,IAAI;QAEzB,IAAI,OAAO,IAAI,EAAE,aAAa,GAAG,CAAC,QAAQ,OAAO,OAAO,IAAI;QAC5D,IAAI,OAAO,KAAK,EAAE,aAAa,GAAG,CAAC,SAAS,OAAO,OAAO,KAAK;QAC/D,IAAI,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC3D,IAAI,OAAO,QAAQ,EAAE,aAAa,GAAG,CAAC,YAAY,OAAO,QAAQ;QACjE,IAAI,OAAO,SAAS,EAAE,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QAEpE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc;QAC3E,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,iBAAiB,IAAY,EAAiC;QAClE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,MAAM;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAE,IAAgC,EAAiC;QAC/F,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAA6C;QACzE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE;YACjE,QAAQ;QACV;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,MAAM,eAAe,KAAa,EAAE,SAAc,CAAC,CAAC,EAA6B;QAC/E,MAAM,eAAe,IAAI,gBAAgB;YAAE,GAAG;YAAO,GAAG,MAAM;QAAC;QAC/D,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,cAAc;QACzE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc,IAAI;AAGxB,MAAM,EACX,aAAa,EACb,WAAW,EACX,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,cAAc,EACf,GAAG;AAMG,SAAS,wBAAwB,UAAe;IACrD,OAAO;QACL,IAAI,WAAW,EAAE;QACjB,OAAO,WAAW,KAAK;QACvB,MAAM,WAAW,IAAI;QACrB,SAAS,WAAW,OAAO;QAC3B,SAAS,WAAW,OAAO;QAC3B,SAAS,WAAW,OAAO;QAC3B,YAAY,WAAW,WAAW;QAClC,MAAM,MAAM,OAAO,CAAC,WAAW,IAAI,IAAI,WAAW,IAAI,GAAG,EAAE;QAC3D,UAAU,WAAW,QAAQ;QAC7B,QAAQ,WAAW,MAAM;QACzB,UAAU,WAAW,SAAS;QAC9B,QAAQ;YACN,IAAI,WAAW,SAAS;YACxB,MAAM;YACN,OAAO;YACP,MAAM;YACN,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,aAAa,WAAW,YAAY,GAAG,IAAI,KAAK,WAAW,YAAY,IAAI;QAC3E,WAAW,IAAI,KAAK,WAAW,UAAU;QACzC,WAAW,IAAI,KAAK,WAAW,UAAU;QACzC,WAAW,WAAW,UAAU,IAAI;QACpC,WAAW,WAAW,UAAU,IAAI;IACtC;AACF;AAKO,SAAS,sBAAsB,OAAyB;IAC7D,OAAO;QACL,OAAO,QAAQ,KAAK;QACpB,SAAS,QAAQ,OAAO;QACxB,SAAS,QAAQ,OAAO;QACxB,UAAU,QAAQ,QAAQ;QAC1B,MAAM,QAAQ,IAAI;QAClB,QAAQ,QAAQ,MAAM;QACtB,aAAa,QAAQ,UAAU;IACjC;AACF", "debugId": null}}, {"offset": {"line": 2991, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/article/article-editor.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { useRout<PERSON> } from \"next/navigation\"\nimport { Save, Eye, Send, ArrowLeft, <PERSON><PERSON><PERSON> } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { MarkdownRenderer } from \"@/components/ui/markdown-renderer\"\nimport { SEOAnalyzer } from \"./seo-analyzer\"\nimport { AIAssistant } from \"./ai-assistant\"\nimport { mockCategories, mockTags } from \"@/lib/mock-data\"\nimport { generateSlug } from \"@/lib/utils\"\nimport type { User } from \"@/types\"\nimport { articlesAPI } from \"@/lib/api/articles\"\n\ninterface ArticleEditorProps {\n  user: User\n  articleId?: string\n}\n\ninterface ArticleForm {\n  title: string\n  slug: string\n  content: string\n  excerpt: string\n  category: string\n  tags: string[]\n  coverImage: string\n  status: 'draft' | 'published'\n}\n\nexport function ArticleEditor({ user, articleId }: ArticleEditorProps) {\n  const router = useRouter()\n  const [isPreview, setIsPreview] = useState(false)\n  const [isSaving, setIsSaving] = useState(false)\n  const [isGeneratingAI, setIsGeneratingAI] = useState(false)\n  \n  const [form, setForm] = useState<ArticleForm>({\n    title: \"\",\n    slug: \"\",\n    content: \"\",\n    excerpt: \"\",\n    category: \"\",\n    tags: [],\n    coverImage: \"\",\n    status: \"draft\",\n  })\n\n  // 自动生成 slug\n  useEffect(() => {\n    if (form.title && !articleId) {\n      setForm(prev => ({\n        ...prev,\n        slug: generateSlug(form.title)\n      }))\n    }\n  }, [form.title, articleId])\n\n  // 自动保存草稿\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (form.title || form.content) {\n        handleSaveDraft()\n      }\n    }, 30000) // 30秒自动保存\n\n    return () => clearTimeout(timer)\n  }, [form])\n\n  const handleInputChange = (field: keyof ArticleForm, value: string | string[]) => {\n    setForm(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const handleTagToggle = (tag: string) => {\n    setForm(prev => ({\n      ...prev,\n      tags: prev.tags.includes(tag)\n        ? prev.tags.filter(t => t !== tag)\n        : [...prev.tags, tag]\n    }))\n  }\n\n  const handleSaveDraft = async () => {\n    setIsSaving(true)\n    try {\n      const articleData = {\n        title: form.title,\n        content: form.content,\n        excerpt: form.excerpt,\n        category: form.category,\n        tags: form.tags,\n        status: 'draft' as const,\n        cover_image: form.coverImage\n      }\n\n      const result = await articlesAPI.createArticle(articleData)\n\n      if (result.success) {\n        console.log(\"草稿保存成功:\", result.data)\n        // 可以显示成功提示\n        // 可以更新 URL 为编辑模式\n        if (result.data?.id) {\n          router.push(`/dashboard/articles/edit/${result.data.id}`)\n        }\n      } else {\n        throw new Error(result.error || '保存失败')\n      }\n    } catch (error) {\n      console.error(\"保存失败:\", error)\n      // 可以显示错误提示\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handlePublish = async () => {\n    setIsSaving(true)\n    try {\n      const articleData = {\n        title: form.title,\n        content: form.content,\n        excerpt: form.excerpt,\n        category: form.category,\n        tags: form.tags,\n        status: 'published' as const,\n        cover_image: form.coverImage\n      }\n\n      const result = await articlesAPI.createArticle(articleData)\n\n      if (result.success) {\n        console.log(\"文章发布成功:\", result.data)\n        router.push(\"/dashboard/articles\")\n      } else {\n        throw new Error(result.error || '发布失败')\n      }\n    } catch (error) {\n      console.error(\"发布失败:\", error)\n      // 可以显示错误提示\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handleGenerateAISummary = async () => {\n    if (!form.content) return\n\n    setIsGeneratingAI(true)\n    try {\n      const response = await fetch(\"/api/ai/summary\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          content: form.content,\n          options: {\n            maxLength: 150,\n            language: \"zh\",\n            style: \"formal\",\n          },\n        }),\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        setForm(prev => ({\n          ...prev,\n          excerpt: data.data\n        }))\n      } else {\n        console.error(\"AI 生成失败:\", data.error)\n      }\n    } catch (error) {\n      console.error(\"AI 生成失败:\", error)\n    } finally {\n      setIsGeneratingAI(false)\n    }\n  }\n\n  // 处理 AI 摘要生成\n  const handleAISummaryGenerated = (summary: string) => {\n    setForm(prev => ({\n      ...prev,\n      excerpt: summary\n    }))\n  }\n\n  // 处理 AI 标签推荐\n  const handleAITagsRecommended = (tags: string[]) => {\n    setForm(prev => ({\n      ...prev,\n      tags: [...new Set([...prev.tags, ...tags])]\n    }))\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n      {/* 编辑器主体 */}\n      <div className=\"lg:col-span-2 space-y-6\">\n        {/* 工具栏 */}\n        <div className=\"flex items-center justify-between\">\n          <Button variant=\"ghost\" onClick={() => router.back()}>\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            返回\n          </Button>\n          \n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"outline\"\n              onClick={() => setIsPreview(!isPreview)}\n            >\n              <Eye className=\"w-4 h-4 mr-2\" />\n              {isPreview ? \"编辑\" : \"预览\"}\n            </Button>\n            <Button\n              variant=\"outline\"\n              onClick={handleSaveDraft}\n              disabled={isSaving}\n            >\n              <Save className=\"w-4 h-4 mr-2\" />\n              {isSaving ? \"保存中...\" : \"保存草稿\"}\n            </Button>\n            <Button onClick={handlePublish} disabled={isSaving}>\n              <Send className=\"w-4 h-4 mr-2\" />\n              发布文章\n            </Button>\n          </div>\n        </div>\n\n        {/* 文章标题 */}\n        <Card>\n          <CardHeader>\n            <CardTitle>文章信息</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"title\">标题</Label>\n              <Input\n                id=\"title\"\n                placeholder=\"输入文章标题...\"\n                value={form.title}\n                onChange={(e) => handleInputChange(\"title\", e.target.value)}\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"slug\">URL 别名</Label>\n              <Input\n                id=\"slug\"\n                placeholder=\"article-slug\"\n                value={form.slug}\n                onChange={(e) => handleInputChange(\"slug\", e.target.value)}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <Label htmlFor=\"excerpt\">文章摘要</Label>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleGenerateAISummary}\n                  disabled={isGeneratingAI || !form.content}\n                >\n                  <Sparkles className=\"w-3 h-3 mr-1\" />\n                  {isGeneratingAI ? \"生成中...\" : \"AI 生成\"}\n                </Button>\n              </div>\n              <Textarea\n                id=\"excerpt\"\n                placeholder=\"输入文章摘要...\"\n                rows={3}\n                value={form.excerpt}\n                onChange={(e) => handleInputChange(\"excerpt\", e.target.value)}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* 内容编辑器 */}\n        <Card>\n          <CardHeader>\n            <CardTitle>文章内容</CardTitle>\n            <CardDescription>\n              使用 Markdown 语法编写文章内容\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {isPreview ? (\n              <div className=\"min-h-[400px] border rounded-lg p-4\">\n                <MarkdownRenderer content={form.content} />\n              </div>\n            ) : (\n              <Textarea\n                placeholder=\"开始编写您的文章内容...\"\n                className=\"min-h-[400px] font-mono\"\n                value={form.content}\n                onChange={(e) => handleInputChange(\"content\", e.target.value)}\n              />\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* 侧边栏设置 */}\n      <div className=\"space-y-6\">\n        {/* 发布设置 */}\n        <Card>\n          <CardHeader>\n            <CardTitle>发布设置</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"category\">分类</Label>\n              <Select value={form.category} onValueChange={(value) => handleInputChange(\"category\", value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"选择分类\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {mockCategories.map((category) => (\n                    <SelectItem key={category.id} value={category.name}>\n                      {category.name}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>标签</Label>\n              <div className=\"flex flex-wrap gap-2\">\n                {mockTags.map((tag) => (\n                  <Badge\n                    key={tag.id}\n                    variant={form.tags.includes(tag.name) ? \"default\" : \"outline\"}\n                    className=\"cursor-pointer\"\n                    onClick={() => handleTagToggle(tag.name)}\n                  >\n                    {tag.name}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n\n            <Separator />\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"coverImage\">封面图片 URL</Label>\n              <Input\n                id=\"coverImage\"\n                placeholder=\"https://example.com/image.jpg\"\n                value={form.coverImage}\n                onChange={(e) => handleInputChange(\"coverImage\", e.target.value)}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* 文章统计 */}\n        <Card>\n          <CardHeader>\n            <CardTitle>文章统计</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm\">\n              <span>字符数:</span>\n              <span>{form.content.length}</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span>预计阅读时间:</span>\n              <span>{Math.ceil(form.content.length / 500)} 分钟</span>\n            </div>\n            <div className=\"flex justify-between text-sm\">\n              <span>状态:</span>\n              <Badge variant={form.status === 'published' ? 'default' : 'secondary'}>\n                {form.status === 'published' ? '已发布' : '草稿'}\n              </Badge>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* SEO 分析 */}\n        <SEOAnalyzer article={form} />\n\n        {/* AI 助手 */}\n        <AIAssistant\n          title={form.title}\n          content={form.content}\n          onSummaryGenerated={handleAISummaryGenerated}\n          onTagsRecommended={handleAITagsRecommended}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAnBA;;;;;;;;;;;;;;;;;;;AAqCO,SAAS,cAAc,EAAE,IAAI,EAAE,SAAS,EAAsB;IACnE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC5C,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,MAAM,EAAE;QACR,YAAY;QACZ,QAAQ;IACV;IAEA,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW;YAC5B,QAAQ,CAAA,OAAQ,CAAC;oBACf,GAAG,IAAI;oBACP,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,KAAK;gBAC/B,CAAC;QACH;IACF,GAAG;QAAC,KAAK,KAAK;QAAE;KAAU;IAE1B,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,IAAI,KAAK,KAAK,IAAI,KAAK,OAAO,EAAE;gBAC9B;YACF;QACF,GAAG,OAAO,UAAU;;QAEpB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB,CAAC,OAA0B;QACnD,QAAQ,CAAA,OAAQ,CAAC;gBACf,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,QAAQ,CAAA,OAAQ,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,OACrB,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,OAC5B;uBAAI,KAAK,IAAI;oBAAE;iBAAI;YACzB,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB,YAAY;QACZ,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;gBACf,QAAQ;gBACR,aAAa,KAAK,UAAU;YAC9B;YAEA,MAAM,SAAS,MAAM,6HAAA,CAAA,cAAW,CAAC,aAAa,CAAC;YAE/C,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,GAAG,CAAC,WAAW,OAAO,IAAI;gBAClC,WAAW;gBACX,iBAAiB;gBACjB,IAAI,OAAO,IAAI,EAAE,IAAI;oBACnB,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE;gBAC1D;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACvB,WAAW;QACb,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY;QACZ,IAAI;YACF,MAAM,cAAc;gBAClB,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;gBACf,QAAQ;gBACR,aAAa,KAAK,UAAU;YAC9B;YAEA,MAAM,SAAS,MAAM,6HAAA,CAAA,cAAW,CAAC,aAAa,CAAC;YAE/C,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,GAAG,CAAC,WAAW,OAAO,IAAI;gBAClC,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACvB,WAAW;QACb,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI,CAAC,KAAK,OAAO,EAAE;QAEnB,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,KAAK,OAAO;oBACrB,SAAS;wBACP,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,CAAA,OAAQ,CAAC;wBACf,GAAG,IAAI;wBACP,SAAS,KAAK,IAAI;oBACpB,CAAC;YACH,OAAO;gBACL,QAAQ,KAAK,CAAC,YAAY,KAAK,KAAK;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;QAC5B,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,aAAa;IACb,MAAM,2BAA2B,CAAC;QAChC,QAAQ,CAAA,OAAQ,CAAC;gBACf,GAAG,IAAI;gBACP,SAAS;YACX,CAAC;IACH;IAEA,aAAa;IACb,MAAM,0BAA0B,CAAC;QAC/B,QAAQ,CAAA,OAAQ,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;uBAAI,IAAI,IAAI;2BAAI,KAAK,IAAI;2BAAK;qBAAK;iBAAE;YAC7C,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,SAAS,IAAM,OAAO,IAAI;;kDAChD,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa,CAAC;;0DAE7B,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACd,YAAY,OAAO;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,WAAW,WAAW;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAe,UAAU;;0DACxC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAOvC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,KAAK,KAAK;gDACjB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAI9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,KAAK,IAAI;gDAChB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAI7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,UAAU,kBAAkB,CAAC,KAAK,OAAO;;0EAEzC,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,iBAAiB,WAAW;;;;;;;;;;;;;0DAGjC,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,aAAY;gDACZ,MAAM;gDACN,OAAO,KAAK,OAAO;gDACnB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOpE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACT,0BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gJAAA,CAAA,mBAAgB;wCAAC,SAAS,KAAK,OAAO;;;;;;;;;;yDAGzC,8OAAC,oIAAA,CAAA,WAAQ;oCACP,aAAY;oCACZ,WAAU;oCACV,OAAO,KAAK,OAAO;oCACnB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAQtE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO,KAAK,QAAQ;gDAAE,eAAe,CAAC,QAAU,kBAAkB,YAAY;;kEACpF,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;kEACX,0HAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,yBACnB,8OAAC,kIAAA,CAAA,aAAU;gEAAmB,OAAO,SAAS,IAAI;0EAC/C,SAAS,IAAI;+DADC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;kDAQpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAI,WAAU;0DACZ,0HAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,CAAC,oBACb,8OAAC,iIAAA,CAAA,QAAK;wDAEJ,SAAS,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,YAAY;wDACpD,WAAU;wDACV,SAAS,IAAM,gBAAgB,IAAI,IAAI;kEAEtC,IAAI,IAAI;uDALJ,IAAI,EAAE;;;;;;;;;;;;;;;;kDAWnB,8OAAC,qIAAA,CAAA,YAAS;;;;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,KAAK,UAAU;gDACtB,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOvE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAM,KAAK,OAAO,CAAC,MAAM;;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM,KAAK,IAAI,CAAC,KAAK,OAAO,CAAC,MAAM,GAAG;oDAAK;;;;;;;;;;;;;kDAE9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,KAAK,MAAM,KAAK,cAAc,YAAY;0DACvD,KAAK,MAAM,KAAK,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,8OAAC,gJAAA,CAAA,cAAW;wBAAC,SAAS;;;;;;kCAGtB,8OAAC,gJAAA,CAAA,cAAW;wBACV,OAAO,KAAK,KAAK;wBACjB,SAAS,KAAK,OAAO;wBACrB,oBAAoB;wBACpB,mBAAmB;;;;;;;;;;;;;;;;;;AAK7B", "debugId": null}}]}
import { NextResponse } from "next/server"
import { getArticles } from "@/lib/mock-data"
import { siteConfig } from "@/config/site"

/**
 * 生成 RSS Feed
 */
export async function GET() {
  // 获取所有已发布的文章
  const { articles } = getArticles({ status: "published" })
  
  // 创建 RSS XML
  const rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0"
     xmlns:content="http://purl.org/rss/1.0/modules/content/"
     xmlns:wfw="http://wellformedweb.org/CommentAPI/"
     xmlns:dc="http://purl.org/dc/elements/1.1/"
     xmlns:atom="http://www.w3.org/2005/Atom"
     xmlns:sy="http://purl.org/rss/1.0/modules/syndication/"
     xmlns:slash="http://purl.org/rss/1.0/modules/slash/">
  <channel>
    <title>${siteConfig.name}</title>
    <atom:link href="${siteConfig.url}/api/rss" rel="self" type="application/rss+xml" />
    <link>${siteConfig.url}</link>
    <description>${siteConfig.description}</description>
    <language>zh-CN</language>
    <pubDate>${new Date().toUTCString()}</pubDate>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <sy:updatePeriod>hourly</sy:updatePeriod>
    <sy:updateFrequency>1</sy:updateFrequency>
    <generator>Next.js</generator>
    <managingEditor>${siteConfig.author.email} (${siteConfig.author.name})</managingEditor>
    <webMaster>${siteConfig.author.email} (${siteConfig.author.name})</webMaster>
    <image>
      <url>${siteConfig.url}/logo.png</url>
      <title>${siteConfig.name}</title>
      <link>${siteConfig.url}</link>
      <width>144</width>
      <height>144</height>
    </image>
    
    ${articles.map(article => {
      const pubDate = article.publishedAt || article.createdAt
      const content = article.content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&apos;')
      
      return `
    <item>
      <title>${article.title}</title>
      <link>${siteConfig.url}/articles/${article.slug}</link>
      <pubDate>${pubDate.toUTCString()}</pubDate>
      <dc:creator><![CDATA[${article.author.name}]]></dc:creator>
      <guid isPermaLink="false">${siteConfig.url}/articles/${article.slug}</guid>
      <description><![CDATA[${article.excerpt || article.summary || ''}]]></description>
      <content:encoded><![CDATA[${content}]]></content:encoded>
      <category><![CDATA[${article.category}]]></category>
      ${article.tags.map(tag => `<category><![CDATA[${tag}]]></category>`).join('')}
      ${article.coverImage ? `<enclosure url="${article.coverImage}" type="image/jpeg" />` : ''}
    </item>`
    }).join('')}
  </channel>
</rss>`

  // 返回 XML 响应
  return new NextResponse(rss, {
    headers: {
      "Content-Type": "application/rss+xml",
      "Cache-Control": "public, max-age=3600, s-maxage=3600",
    },
  })
}

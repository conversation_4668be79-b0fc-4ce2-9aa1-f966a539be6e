import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// 支持的语言列表
export const locales = ['zh', 'en', 'ja'] as const;
export type Locale = typeof locales[number];

// 默认语言
export const defaultLocale: Locale = 'zh';

// 语言配置
export const localeConfig = {
  zh: {
    name: '中文',
    flag: '🇨🇳',
    dir: 'ltr',
  },
  en: {
    name: 'English',
    flag: '🇺🇸',
    dir: 'ltr',
  },
  ja: {
    name: '日本語',
    flag: '🇯🇵',
    dir: 'ltr',
  },
} as const;

// 向后兼容
export const localeNames: Record<Locale, string> = {
  zh: localeConfig.zh.name,
  en: localeConfig.en.name,
  ja: localeConfig.ja.name,
};

export const localeFlags: Record<Locale, string> = {
  zh: localeConfig.zh.flag,
  en: localeConfig.en.flag,
  ja: localeConfig.ja.flag,
};

export default getRequestConfig(async ({ locale }) => {
  // 验证传入的语言是否支持
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  try {
    return {
      messages: (await import(`./messages/${locale}.json`)).default,
      timeZone: 'Asia/Shanghai',
      now: new Date(),
      formats: {
        dateTime: {
          short: {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          },
          long: {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
          },
        },
        number: {
          precise: {
            maximumFractionDigits: 5,
          },
        },
        list: {
          enumeration: {
            style: 'long',
            type: 'conjunction',
          },
        },
      },
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);
    notFound();
  }
});

/**
 * 获取语言显示名称
 */
export function getLocaleName(locale: Locale): string {
  return localeConfig[locale]?.name || locale;
}

/**
 * 获取语言标志
 */
export function getLocaleFlag(locale: Locale): string {
  return localeConfig[locale]?.flag || '🌐';
}

/**
 * 检查是否为有效语言
 */
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

/**
 * 获取浏览器首选语言
 */
export function getBrowserLocale(): Locale {
  if (typeof window === 'undefined') return defaultLocale;

  const browserLang = navigator.language.split('-')[0];
  return isValidLocale(browserLang) ? browserLang : defaultLocale;
}

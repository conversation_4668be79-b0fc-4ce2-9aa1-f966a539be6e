{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/node_modules/prismjs/components/prism-scss.js"], "sourcesContent": ["Prism.languages.scss = Prism.languages.extend('css', {\n\t'comment': {\n\t\tpattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n\t\tlookbehind: true\n\t},\n\t'atrule': {\n\t\tpattern: /@[\\w-](?:\\([^()]+\\)|[^()\\s]|\\s+(?!\\s))*?(?=\\s+[{;])/,\n\t\tinside: {\n\t\t\t'rule': /@[\\w-]+/\n\t\t\t// See rest below\n\t\t}\n\t},\n\t// url, compassified\n\t'url': /(?:[-a-z]+-)?url(?=\\()/i,\n\t// CSS selector regex is not appropriate for Sass\n\t// since there can be lot more things (var, @ directive, nesting..)\n\t// a selector must start at the end of a property or after a brace (end of other rules or nesting)\n\t// it can contain some characters that aren't used for defining rules or end of selector, & (parent selector), or interpolated variable\n\t// the end of a selector is found when there is no rules in it ( {} or {\\s}) or if there is a property (because an interpolated var\n\t// can \"pass\" as a selector- e.g: proper#{$erty})\n\t// this one was hard to do, so please be careful if you edit this one :)\n\t'selector': {\n\t\t// Initial look-ahead is used to prevent matching of blank selectors\n\t\tpattern: /(?=\\S)[^@;{}()]?(?:[^@;{}()\\s]|\\s+(?!\\s)|#\\{\\$[-\\w]+\\})+(?=\\s*\\{(?:\\}|\\s|[^}][^:{}]*[:{][^}]))/,\n\t\tinside: {\n\t\t\t'parent': {\n\t\t\t\tpattern: /&/,\n\t\t\t\talias: 'important'\n\t\t\t},\n\t\t\t'placeholder': /%[-\\w]+/,\n\t\t\t'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n\t\t}\n\t},\n\t'property': {\n\t\tpattern: /(?:[-\\w]|\\$[-\\w]|#\\{\\$[-\\w]+\\})+(?=\\s*:)/,\n\t\tinside: {\n\t\t\t'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n\t\t}\n\t}\n});\n\nPrism.languages.insertBefore('scss', 'atrule', {\n\t'keyword': [\n\t\t/@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\\b/i,\n\t\t{\n\t\t\tpattern: /( )(?:from|through)(?= )/,\n\t\t\tlookbehind: true\n\t\t}\n\t]\n});\n\nPrism.languages.insertBefore('scss', 'important', {\n\t// var and interpolated vars\n\t'variable': /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n});\n\nPrism.languages.insertBefore('scss', 'function', {\n\t'module-modifier': {\n\t\tpattern: /\\b(?:as|hide|show|with)\\b/i,\n\t\talias: 'keyword'\n\t},\n\t'placeholder': {\n\t\tpattern: /%[-\\w]+/,\n\t\talias: 'selector'\n\t},\n\t'statement': {\n\t\tpattern: /\\B!(?:default|optional)\\b/i,\n\t\talias: 'keyword'\n\t},\n\t'boolean': /\\b(?:false|true)\\b/,\n\t'null': {\n\t\tpattern: /\\bnull\\b/,\n\t\talias: 'keyword'\n\t},\n\t'operator': {\n\t\tpattern: /(\\s)(?:[-+*\\/%]|[=!]=|<=?|>=?|and|not|or)(?=\\s)/,\n\t\tlookbehind: true\n\t}\n});\n\nPrism.languages.scss['atrule'].inside.rest = Prism.languages.scss;\n"], "names": [], "mappings": "AAAA,MAAM,SAAS,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC,OAAO;IACpD,WAAW;QACV,SAAS;QACT,YAAY;IACb;IACA,UAAU;QACT,SAAS;QACT,QAAQ;YACP,QAAQ;QAET;IACD;IACA,oBAAoB;IACpB,OAAO;IACP,iDAAiD;IACjD,mEAAmE;IACnE,kGAAkG;IAClG,uIAAuI;IACvI,mIAAmI;IACnI,iDAAiD;IACjD,wEAAwE;IACxE,YAAY;QACX,oEAAoE;QACpE,SAAS;QACT,QAAQ;YACP,UAAU;gBACT,SAAS;gBACT,OAAO;YACR;YACA,eAAe;YACf,YAAY;QACb;IACD;IACA,YAAY;QACX,SAAS;QACT,QAAQ;YACP,YAAY;QACb;IACD;AACD;AAEA,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,UAAU;IAC9C,WAAW;QACV;QACA;YACC,SAAS;YACT,YAAY;QACb;KACA;AACF;AAEA,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,aAAa;IACjD,4BAA4B;IAC5B,YAAY;AACb;AAEA,MAAM,SAAS,CAAC,YAAY,CAAC,QAAQ,YAAY;IAChD,mBAAmB;QAClB,SAAS;QACT,OAAO;IACR;IACA,eAAe;QACd,SAAS;QACT,OAAO;IACR;IACA,aAAa;QACZ,SAAS;QACT,OAAO;IACR;IACA,WAAW;IACX,QAAQ;QACP,SAAS;QACT,OAAO;IACR;IACA,YAAY;QACX,SAAS;QACT,YAAY;IACb;AACD;AAEA,MAAM,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,SAAS,CAAC,IAAI", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\nimport { getToken } from 'next-auth/jwt'\n\n// 需要认证的路径\nconst protectedPaths = [\n  '/dashboard',\n  '/admin',\n  '/files',\n  '/profile',\n  '/settings',\n  '/api/admin',\n  '/api/files',\n  '/api/articles/create',\n  '/api/articles/edit',\n  '/api/articles/delete',\n]\n\n// 管理员专用路径\nconst adminPaths = [\n  '/admin',\n  '/api/admin',\n]\n\n// 协作者及以上权限路径\nconst collaboratorPaths = [\n  '/dashboard',\n  '/api/articles/create',\n  '/api/articles/edit',\n]\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // 检查是否是受保护的路径\n  const isProtectedPath = protectedPaths.some(path =>\n    pathname.startsWith(path)\n  )\n\n  // 检查是否是管理员路径\n  const isAdminPath = adminPaths.some(path =>\n    pathname.startsWith(path)\n  )\n\n  // 检查是否是协作者路径\n  const isCollaboratorPath = collaboratorPaths.some(path =>\n    pathname.startsWith(path)\n  )\n\n  if (isProtectedPath) {\n    // 获取用户 token\n    const token = await getToken({\n      req: request,\n      secret: process.env.NEXTAUTH_SECRET\n    })\n\n    if (!token) {\n      // 未认证，重定向到登录页\n      const loginUrl = new URL('/auth/signin', request.url)\n      loginUrl.searchParams.set('callbackUrl', pathname)\n      return NextResponse.redirect(loginUrl)\n    }\n\n    // 检查管理员权限\n    if (isAdminPath && token.role !== 'admin') {\n      return NextResponse.redirect(new URL('/unauthorized', request.url))\n    }\n\n    // 检查协作者权限\n    if (isCollaboratorPath && token.role !== 'admin' && token.role !== 'collaborator') {\n      return NextResponse.redirect(new URL('/unauthorized', request.url))\n    }\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEA,UAAU;AACV,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,UAAU;AACV,MAAM,aAAa;IACjB;IACA;CACD;AAED,aAAa;AACb,MAAM,oBAAoB;IACxB;IACA;IACA;CACD;AAEM,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,cAAc;IACd,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAA,OAC1C,SAAS,UAAU,CAAC;IAGtB,aAAa;IACb,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,OAClC,SAAS,UAAU,CAAC;IAGtB,aAAa;IACb,MAAM,qBAAqB,kBAAkB,IAAI,CAAC,CAAA,OAChD,SAAS,UAAU,CAAC;IAGtB,IAAI,iBAAiB;QACnB,aAAa;QACb,MAAM,QAAQ,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;YAC3B,KAAK;YACL,QAAQ,QAAQ,GAAG,CAAC,eAAe;QACrC;QAEA,IAAI,CAAC,OAAO;YACV,cAAc;YACd,MAAM,WAAW,IAAI,IAAI,gBAAgB,QAAQ,GAAG;YACpD,SAAS,YAAY,CAAC,GAAG,CAAC,eAAe;YACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,UAAU;QACV,IAAI,eAAe,MAAM,IAAI,KAAK,SAAS;YACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,iBAAiB,QAAQ,GAAG;QACnE;QAEA,UAAU;QACV,IAAI,sBAAsB,MAAM,IAAI,KAAK,WAAW,MAAM,IAAI,KAAK,gBAAgB;YACjF,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,iBAAiB,QAAQ,GAAG;QACnE;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}
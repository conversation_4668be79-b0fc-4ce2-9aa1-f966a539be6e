#!/bin/bash

# =============================================================================
# 现代化博客系统 - 环境变量和密钥设置脚本
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v wrangler &> /dev/null; then
        log_error "wrangler CLI 未安装。请运行: npm install -g wrangler"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，某些功能可能不可用"
    fi
    
    log_success "依赖检查完成"
}

# 验证 Cloudflare 认证
verify_auth() {
    log_info "验证 Cloudflare 认证..."
    
    if ! wrangler whoami &> /dev/null; then
        log_error "Cloudflare 认证失败。请运行: wrangler login"
        exit 1
    fi
    
    log_success "Cloudflare 认证验证成功"
}

# 设置环境变量
setup_environment() {
    local env=$1
    log_info "设置 $env 环境的密钥..."
    
    # JWT 密钥
    if [ -z "$JWT_SECRET" ]; then
        JWT_SECRET=$(openssl rand -base64 32)
        log_info "生成新的 JWT 密钥"
    fi
    echo "$JWT_SECRET" | wrangler secret put JWT_SECRET --env $env
    
    # GitHub OAuth 密钥
    if [ -n "$GITHUB_CLIENT_SECRET" ]; then
        echo "$GITHUB_CLIENT_SECRET" | wrangler secret put GITHUB_CLIENT_SECRET --env $env
    else
        log_warning "GITHUB_CLIENT_SECRET 未设置，请手动设置"
    fi
    
    # 管理员邮箱
    if [ -n "$ADMIN_EMAILS" ]; then
        echo "$ADMIN_EMAILS" | wrangler secret put ADMIN_EMAILS --env $env
    else
        log_warning "ADMIN_EMAILS 未设置，请手动设置"
    fi
    
    # OpenAI API 密钥 (可选)
    if [ -n "$OPENAI_API_KEY" ]; then
        echo "$OPENAI_API_KEY" | wrangler secret put OPENAI_API_KEY --env $env
    fi
    
    # Sentry DSN (可选)
    if [ -n "$SENTRY_DSN" ]; then
        echo "$SENTRY_DSN" | wrangler secret put SENTRY_DSN --env $env
    fi
    
    # 邮件服务密钥 (可选)
    if [ -n "$RESEND_API_KEY" ]; then
        echo "$RESEND_API_KEY" | wrangler secret put RESEND_API_KEY --env $env
    fi
    
    if [ -n "$SENDGRID_API_KEY" ]; then
        echo "$SENDGRID_API_KEY" | wrangler secret put SENDGRID_API_KEY --env $env
    fi
    
    log_success "$env 环境密钥设置完成"
}

# 创建数据库
create_database() {
    local env=$1
    local db_name="modern-blog-db-$env"
    
    log_info "创建 $env 环境数据库: $db_name"
    
    if wrangler d1 list | grep -q "$db_name"; then
        log_warning "数据库 $db_name 已存在"
    else
        wrangler d1 create "$db_name"
        log_success "数据库 $db_name 创建成功"
    fi
}

# 创建 KV 命名空间
create_kv_namespaces() {
    local env=$1
    
    log_info "创建 $env 环境 KV 命名空间..."
    
    local namespaces=("cache" "session" "rate-limit")
    
    for namespace in "${namespaces[@]}"; do
        local kv_name="modern-blog-$namespace-$env"
        
        if wrangler kv:namespace list | grep -q "$kv_name"; then
            log_warning "KV 命名空间 $kv_name 已存在"
        else
            wrangler kv:namespace create "$kv_name"
            log_success "KV 命名空间 $kv_name 创建成功"
        fi
    done
}

# 创建 R2 存储桶
create_r2_buckets() {
    local env=$1
    local bucket_name="modern-blog-storage-$env"
    
    log_info "创建 $env 环境 R2 存储桶: $bucket_name"
    
    if wrangler r2 bucket list | grep -q "$bucket_name"; then
        log_warning "R2 存储桶 $bucket_name 已存在"
    else
        wrangler r2 bucket create "$bucket_name"
        log_success "R2 存储桶 $bucket_name 创建成功"
    fi
}

# 运行数据库迁移
run_migrations() {
    local env=$1
    local db_name="modern-blog-db-$env"
    
    log_info "运行 $env 环境数据库迁移..."
    
    cd workers
    
    if [ -d "migrations" ] && [ "$(ls -A migrations)" ]; then
        wrangler d1 migrations apply "$db_name" --env $env
        log_success "$env 环境数据库迁移完成"
    else
        log_warning "未找到数据库迁移文件"
    fi
    
    cd ..
}

# 验证部署
verify_deployment() {
    local env=$1
    
    log_info "验证 $env 环境部署..."
    
    cd workers
    wrangler deploy --env $env --dry-run
    log_success "$env 环境部署验证通过"
    cd ..
}

# 显示帮助信息
show_help() {
    echo "现代化博客系统 - 环境设置脚本"
    echo ""
    echo "用法: $0 [选项] <环境>"
    echo ""
    echo "环境:"
    echo "  development  开发环境"
    echo "  staging      测试环境"
    echo "  production   生产环境"
    echo "  all          所有环境"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --secrets-only 仅设置密钥"
    echo "  --resources-only 仅创建资源"
    echo "  --verify-only  仅验证部署"
    echo ""
    echo "环境变量:"
    echo "  JWT_SECRET           JWT 密钥 (自动生成)"
    echo "  GITHUB_CLIENT_SECRET GitHub OAuth 客户端密钥"
    echo "  ADMIN_EMAILS         管理员邮箱列表 (逗号分隔)"
    echo "  OPENAI_API_KEY       OpenAI API 密钥 (可选)"
    echo "  SENTRY_DSN           Sentry DSN (可选)"
    echo "  RESEND_API_KEY       Resend API 密钥 (可选)"
    echo "  SENDGRID_API_KEY     SendGrid API 密钥 (可选)"
    echo ""
    echo "示例:"
    echo "  $0 development"
    echo "  $0 --secrets-only production"
    echo "  ADMIN_EMAILS=<EMAIL> $0 all"
}

# 主函数
main() {
    local environment=""
    local secrets_only=false
    local resources_only=false
    local verify_only=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --secrets-only)
                secrets_only=true
                shift
                ;;
            --resources-only)
                resources_only=true
                shift
                ;;
            --verify-only)
                verify_only=true
                shift
                ;;
            development|staging|production|all)
                environment=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    if [ -z "$environment" ]; then
        log_error "请指定环境"
        show_help
        exit 1
    fi
    
    # 检查依赖和认证
    check_dependencies
    verify_auth
    
    # 确定要处理的环境列表
    local environments=()
    if [ "$environment" = "all" ]; then
        environments=("development" "staging" "production")
    else
        environments=("$environment")
    fi
    
    # 处理每个环境
    for env in "${environments[@]}"; do
        log_info "处理 $env 环境..."
        
        if [ "$verify_only" = true ]; then
            verify_deployment "$env"
        elif [ "$secrets_only" = true ]; then
            setup_environment "$env"
        elif [ "$resources_only" = true ]; then
            create_database "$env"
            create_kv_namespaces "$env"
            create_r2_buckets "$env"
        else
            # 完整设置
            create_database "$env"
            create_kv_namespaces "$env"
            create_r2_buckets "$env"
            setup_environment "$env"
            run_migrations "$env"
            verify_deployment "$env"
        fi
        
        log_success "$env 环境处理完成"
    done
    
    log_success "所有环境设置完成！"
    log_info "请更新 wrangler.toml 中的资源 ID"
    log_info "然后运行: wrangler deploy --env <environment>"
}

# 运行主函数
main "$@"

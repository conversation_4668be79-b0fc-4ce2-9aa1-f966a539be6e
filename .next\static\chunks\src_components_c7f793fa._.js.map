{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      // 确保按钮有适当的类型\n      type={!asChild && !props.type ? \"button\" : props.type}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAa;QACb,MAAM,CAAC,WAAW,CAAC,MAAM,IAAI,GAAG,WAAW,MAAM,IAAI;QACpD,GAAG,KAAK;;;;;;AAGf;KArBS", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/article/ai-assistant.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON><PERSON>, <PERSON>, FileText, BarChart3, <PERSON><PERSON>2, <PERSON><PERSON>, Check } from \"lucide-react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Separator } from \"@/components/ui/separator\"\n\ninterface AIAssistantProps {\n  title: string\n  content: string\n  onSummaryGenerated: (summary: string) => void\n  onTagsRecommended: (tags: string[]) => void\n}\n\nexport function AIAssistant({\n  title,\n  content,\n  onSummaryGenerated,\n  onTagsRecommended,\n}: AIAssistantProps) {\n  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false)\n  const [isRecommendingTags, setIsRecommendingTags] = useState(false)\n  const [isAnalyzing, setIsAnalyzing] = useState(false)\n  const [copiedSummary, setCopiedSummary] = useState(false)\n  \n  const [generatedSummary, setGeneratedSummary] = useState<string>(\"\")\n  const [recommendedTags, setRecommendedTags] = useState<string[]>([])\n  const [contentAnalysis, setContentAnalysis] = useState<any>(null)\n\n  // 生成摘要\n  const handleGenerateSummary = async () => {\n    if (!content.trim()) return\n    \n    setIsGeneratingSummary(true)\n    try {\n      const response = await fetch(\"/api/ai/summary\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          content,\n          options: {\n            maxLength: 150,\n            language: \"zh\",\n            style: \"formal\",\n          },\n        }),\n      })\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        setGeneratedSummary(data.data)\n        onSummaryGenerated(data.data)\n      } else {\n        console.error(\"摘要生成失败:\", data.error)\n      }\n    } catch (error) {\n      console.error(\"摘要生成错误:\", error)\n    } finally {\n      setIsGeneratingSummary(false)\n    }\n  }\n\n  // 推荐标签\n  const handleRecommendTags = async () => {\n    if (!title.trim() || !content.trim()) return\n    \n    setIsRecommendingTags(true)\n    try {\n      const response = await fetch(\"/api/ai/tags\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          title,\n          content,\n          options: {\n            language: \"zh\",\n          },\n        }),\n      })\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        setRecommendedTags(data.data)\n        onTagsRecommended(data.data)\n      } else {\n        console.error(\"标签推荐失败:\", data.error)\n      }\n    } catch (error) {\n      console.error(\"标签推荐错误:\", error)\n    } finally {\n      setIsRecommendingTags(false)\n    }\n  }\n\n  // 分析内容质量\n  const handleAnalyzeContent = async () => {\n    if (!title.trim() || !content.trim()) return\n    \n    setIsAnalyzing(true)\n    try {\n      const response = await fetch(\"/api/ai/analyze\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          title,\n          content,\n        }),\n      })\n      \n      const data = await response.json()\n      \n      if (data.success) {\n        setContentAnalysis(data.data)\n      } else {\n        console.error(\"内容分析失败:\", data.error)\n      }\n    } catch (error) {\n      console.error(\"内容分析错误:\", error)\n    } finally {\n      setIsAnalyzing(false)\n    }\n  }\n\n  // 复制摘要\n  const handleCopySummary = async () => {\n    if (!generatedSummary) return\n    \n    try {\n      await navigator.clipboard.writeText(generatedSummary)\n      setCopiedSummary(true)\n      setTimeout(() => setCopiedSummary(false), 2000)\n    } catch (error) {\n      console.error(\"复制失败:\", error)\n    }\n  }\n\n  const canUseAI = title.trim() && content.trim()\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Sparkles className=\"h-5 w-5 text-blue-500\" />\n          AI 助手\n        </CardTitle>\n        <CardDescription>\n          使用 AI 生成摘要、推荐标签和分析内容质量\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <Tabs defaultValue=\"summary\" className=\"space-y-4\">\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"summary\">摘要生成</TabsTrigger>\n            <TabsTrigger value=\"tags\">标签推荐</TabsTrigger>\n            <TabsTrigger value=\"analysis\">内容分析</TabsTrigger>\n          </TabsList>\n          \n          <TabsContent value=\"summary\" className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <Button\n                onClick={handleGenerateSummary}\n                disabled={!canUseAI || isGeneratingSummary}\n                className=\"w-full\"\n              >\n                {isGeneratingSummary ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    生成中...\n                  </>\n                ) : (\n                  <>\n                    <FileText className=\"w-4 h-4 mr-2\" />\n                    生成 AI 摘要\n                  </>\n                )}\n              </Button>\n              \n              {generatedSummary && (\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium\">生成的摘要:</span>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={handleCopySummary}\n                    >\n                      {copiedSummary ? (\n                        <Check className=\"w-3 h-3\" />\n                      ) : (\n                        <Copy className=\"w-3 h-3\" />\n                      )}\n                    </Button>\n                  </div>\n                  <div className=\"p-3 bg-muted rounded-lg text-sm\">\n                    {generatedSummary}\n                  </div>\n                </div>\n              )}\n            </div>\n          </TabsContent>\n          \n          <TabsContent value=\"tags\" className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <Button\n                onClick={handleRecommendTags}\n                disabled={!canUseAI || isRecommendingTags}\n                className=\"w-full\"\n              >\n                {isRecommendingTags ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    推荐中...\n                  </>\n                ) : (\n                  <>\n                    <Tag className=\"w-4 h-4 mr-2\" />\n                    推荐标签\n                  </>\n                )}\n              </Button>\n              \n              {recommendedTags.length > 0 && (\n                <div className=\"space-y-2\">\n                  <span className=\"text-sm font-medium\">推荐的标签:</span>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {recommendedTags.map((tag) => (\n                      <Badge\n                        key={tag}\n                        variant=\"secondary\"\n                        className=\"cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors\"\n                        onClick={() => onTagsRecommended([tag])}\n                      >\n                        {tag}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </TabsContent>\n          \n          <TabsContent value=\"analysis\" className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <Button\n                onClick={handleAnalyzeContent}\n                disabled={!canUseAI || isAnalyzing}\n                className=\"w-full\"\n              >\n                {isAnalyzing ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    分析中...\n                  </>\n                ) : (\n                  <>\n                    <BarChart3 className=\"w-4 h-4 mr-2\" />\n                    分析内容质量\n                  </>\n                )}\n              </Button>\n              \n              {contentAnalysis && (\n                <div className=\"space-y-4\">\n                  {/* 总体分数 */}\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm font-medium\">内容质量分数</span>\n                      <span className=\"text-lg font-bold text-primary\">\n                        {contentAnalysis.score}/100\n                      </span>\n                    </div>\n                    <Progress value={contentAnalysis.score} className=\"h-2\" />\n                  </div>\n                  \n                  <Separator />\n                  \n                  {/* 详细分析 */}\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <span className=\"font-medium\">可读性:</span>\n                      <span className=\"ml-2\">{contentAnalysis.readabilityScore}/100</span>\n                    </div>\n                    <div>\n                      <span className=\"font-medium\">情感分数:</span>\n                      <span className=\"ml-2\">{contentAnalysis.sentimentScore}/100</span>\n                    </div>\n                  </div>\n                  \n                  {/* 优势 */}\n                  {contentAnalysis.strengths.length > 0 && (\n                    <div className=\"space-y-2\">\n                      <span className=\"text-sm font-medium text-green-600\">优势:</span>\n                      <ul className=\"text-sm space-y-1\">\n                        {contentAnalysis.strengths.map((strength: string, index: number) => (\n                          <li key={index} className=\"flex items-start gap-2\">\n                            <span className=\"text-green-500\">✓</span>\n                            {strength}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                  \n                  {/* 建议 */}\n                  {contentAnalysis.suggestions.length > 0 && (\n                    <div className=\"space-y-2\">\n                      <span className=\"text-sm font-medium text-orange-600\">改进建议:</span>\n                      <ul className=\"text-sm space-y-1\">\n                        {contentAnalysis.suggestions.map((suggestion: string, index: number) => (\n                          <li key={index} className=\"flex items-start gap-2\">\n                            <span className=\"text-orange-500\">•</span>\n                            {suggestion}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          </TabsContent>\n        </Tabs>\n        \n        {!canUseAI && (\n          <div className=\"text-center py-4 text-sm text-muted-foreground\">\n            请先输入标题和内容以使用 AI 功能\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAkBO,SAAS,YAAY,EAC1B,KAAK,EACL,OAAO,EACP,kBAAkB,EAClB,iBAAiB,EACA;;IACjB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAE5D,OAAO;IACP,MAAM,wBAAwB;QAC5B,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,uBAAuB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,SAAS;wBACP,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,oBAAoB,KAAK,IAAI;gBAC7B,mBAAmB,KAAK,IAAI;YAC9B,OAAO;gBACL,QAAQ,KAAK,CAAC,WAAW,KAAK,KAAK;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,OAAO;IACP,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;QAEtC,sBAAsB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA,SAAS;wBACP,UAAU;oBACZ;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,IAAI;gBAC5B,kBAAkB,KAAK,IAAI;YAC7B,OAAO;gBACL,QAAQ,KAAK,CAAC,WAAW,KAAK,KAAK;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,SAAS;IACT,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;QAEtC,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,mBAAmB,KAAK,IAAI;YAC9B,OAAO;gBACL,QAAQ,KAAK,CAAC,WAAW,KAAK,KAAK;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;IACP,MAAM,oBAAoB;QACxB,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,iBAAiB;YACjB,WAAW,IAAM,iBAAiB,QAAQ;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,WAAW,MAAM,IAAI,MAAM,QAAQ,IAAI;IAE7C,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAA0B;;;;;;;kCAGhD,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;;kCACV,6LAAC,mIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAU,WAAU;;0CACrC,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAU;;;;;;kDAC7B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAO;;;;;;kDAC1B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;;;;;;;0CAGhC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,YAAY;4CACvB,WAAU;sDAET,oCACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;wCAM1C,kCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;sEAER,8BACC,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;qFAEjB,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAItB,6LAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;0CAOX,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;0CAClC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,YAAY;4CACvB,WAAU;sDAET,mCACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;wCAMrC,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,oBACpB,6LAAC,oIAAA,CAAA,QAAK;4DAEJ,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,kBAAkB;oEAAC;iEAAI;sEAErC;2DALI;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAcnB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,YAAY;4CACvB,WAAU;sDAET,4BACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;wCAM3C,iCACC,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC;oEAAK,WAAU;;wEACb,gBAAgB,KAAK;wEAAC;;;;;;;;;;;;;sEAG3B,6LAAC,uIAAA,CAAA,WAAQ;4DAAC,OAAO,gBAAgB,KAAK;4DAAE,WAAU;;;;;;;;;;;;8DAGpD,6LAAC,wIAAA,CAAA,YAAS;;;;;8DAGV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,6LAAC;oEAAK,WAAU;;wEAAQ,gBAAgB,gBAAgB;wEAAC;;;;;;;;;;;;;sEAE3D,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,6LAAC;oEAAK,WAAU;;wEAAQ,gBAAgB,cAAc;wEAAC;;;;;;;;;;;;;;;;;;;gDAK1D,gBAAgB,SAAS,CAAC,MAAM,GAAG,mBAClC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAG,WAAU;sEACX,gBAAgB,SAAS,CAAC,GAAG,CAAC,CAAC,UAAkB,sBAChD,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC;4EAAK,WAAU;sFAAiB;;;;;;wEAChC;;mEAFM;;;;;;;;;;;;;;;;gDAUhB,gBAAgB,WAAW,CAAC,MAAM,GAAG,mBACpC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAsC;;;;;;sEACtD,6LAAC;4DAAG,WAAU;sEACX,gBAAgB,WAAW,CAAC,GAAG,CAAC,CAAC,YAAoB,sBACpD,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC;4EAAK,WAAU;sFAAkB;;;;;;wEACjC;;mEAFM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAc1B,CAAC,0BACA,6LAAC;wBAAI,WAAU;kCAAiD;;;;;;;;;;;;;;;;;;AAO1E;GA/TgB;KAAA", "debugId": null}}]}
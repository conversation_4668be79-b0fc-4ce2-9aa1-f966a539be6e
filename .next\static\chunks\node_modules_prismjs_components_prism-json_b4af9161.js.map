{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/node_modules/prismjs/components/prism-json.js"], "sourcesContent": ["// https://www.json.org/json-en.html\nPrism.languages.json = {\n\t'property': {\n\t\tpattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n\t\tlookbehind: true,\n\t\tgreedy: true\n\t},\n\t'string': {\n\t\tpattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?!\\s*:)/,\n\t\tlookbehind: true,\n\t\tgreedy: true\n\t},\n\t'comment': {\n\t\tpattern: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n\t\tgreedy: true\n\t},\n\t'number': /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n\t'punctuation': /[{}[\\],]/,\n\t'operator': /:/,\n\t'boolean': /\\b(?:false|true)\\b/,\n\t'null': {\n\t\tpattern: /\\bnull\\b/,\n\t\talias: 'keyword'\n\t}\n};\n\nPrism.languages.webmanifest = Prism.languages.json;\n"], "names": [], "mappings": "AAAA,oCAAoC;AACpC,MAAM,SAAS,CAAC,IAAI,GAAG;IACtB,YAAY;QACX,SAAS;QACT,YAAY;QACZ,QAAQ;IACT;IACA,UAAU;QACT,SAAS;QACT,YAAY;QACZ,QAAQ;IACT;IACA,WAAW;QACV,SAAS;QACT,QAAQ;IACT;IACA,UAAU;IACV,eAAe;IACf,YAAY;IACZ,WAAW;IACX,QAAQ;QACP,SAAS;QACT,OAAO;IACR;AACD;AAEA,MAAM,SAAS,CAAC,WAAW,GAAG,MAAM,SAAS,CAAC,IAAI", "ignoreList": [0], "debugId": null}}]}
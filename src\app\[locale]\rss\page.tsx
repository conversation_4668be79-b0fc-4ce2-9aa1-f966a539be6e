import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { MainLayout } from '@/components/layout/main-layout'
import { PageContainer } from '@/components/layout/page-container'
import { RSSLinks } from '@/components/rss/rss-links'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('rss')
  
  return {
    title: t('title'),
    description: t('subtitle'),
  }
}

export default function RSSPage() {
  return (
    <MainLayout>
      <PageContainer>
        <div className="space-y-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">RSS 订阅</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              通过 RSS 阅读器订阅本站最新内容，第一时间获取更新通知
            </p>
          </div>
          
          <RSSLinks showTitle={false} />
        </div>
      </PageContainer>
    </MainLayout>
  )
}

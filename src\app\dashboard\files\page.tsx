import { requireCollaborator } from "@/lib/auth-utils"
import { AdminLayout } from "@/components/admin/admin-layout"
import { FileManager } from "@/components/files/file-manager"

export default async function FilesPage() {
  const user = await requireCollaborator()

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">文件管理</h1>
          <p className="text-muted-foreground">
            管理您的媒体文件和文档
          </p>
        </div>

        <FileManager user={user} />
      </div>
    </AdminLayout>
  )
}

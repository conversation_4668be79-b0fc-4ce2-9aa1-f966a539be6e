import { NextRequest, NextResponse } from 'next/server'
import { D1Database } from '@cloudflare/workers-types'
import { getRequestContext } from '@/lib/cloudflare'

// 模拟站点地图文件数据
const mockSitemapFiles = [
  {
    type: 'main',
    filename: 'sitemap.xml',
    urls: 1254,
    size: 512000, // 500KB
    lastModified: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
    status: 'active'
  },
  {
    type: 'images',
    filename: 'sitemap-images.xml',
    urls: 856,
    size: 384000, // 375KB
    lastModified: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
    status: 'active'
  },
  {
    type: 'language',
    filename: 'sitemap-zh.xml',
    urls: 421,
    size: 215000, // 210KB
    lastModified: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
    status: 'active'
  },
  {
    type: 'language',
    filename: 'sitemap-en.xml',
    urls: 421,
    size: 215000, // 210KB
    lastModified: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
    status: 'active'
  },
  {
    type: 'language',
    filename: 'sitemap-ja.xml',
    urls: 412,
    size: 210000, // 205KB
    lastModified: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
    status: 'active'
  }
]

export async function GET(request: NextRequest) {
  try {
    // 在实际应用中，这里应该从数据库或存储中获取文件列表
    // const { env } = getRequestContext()
    // const db = env.DB as D1Database
    // const result = await db.prepare('SELECT * FROM sitemap_files').all()
    // const files = result.results || mockSitemapFiles
    
    // 目前使用模拟数据
    const files = mockSitemapFiles
    
    return NextResponse.json({
      success: true,
      data: { files }
    })
  } catch (error) {
    console.error('Sitemap files API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch sitemap files' },
      { status: 500 }
    )
  }
}

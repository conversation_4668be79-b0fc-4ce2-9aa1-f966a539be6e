{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6EACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/providers/session-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/session-provider.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+EACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/providers/session-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/session-provider.tsx\",\n    \"AuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2DACA", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/performance/performance-monitor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LazyComponents = registerClientReference(\n    function() { throw new Error(\"Attempted to call LazyComponents() from the server but LazyComponents is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/performance-monitor.tsx <module evaluation>\",\n    \"LazyComponents\",\n);\nexport const OptimizedImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call OptimizedImage() from the server but OptimizedImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/performance-monitor.tsx <module evaluation>\",\n    \"OptimizedImage\",\n);\nexport const PerformanceMonitor = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/performance-monitor.tsx <module evaluation>\",\n    \"PerformanceMonitor\",\n);\nexport const useResourcePreload = registerClientReference(\n    function() { throw new Error(\"Attempted to call useResourcePreload() from the server but useResourcePreload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/performance-monitor.tsx <module evaluation>\",\n    \"useResourcePreload\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oFACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oFACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,oFACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,oFACA", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/performance/performance-monitor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LazyComponents = registerClientReference(\n    function() { throw new Error(\"Attempted to call LazyComponents() from the server but LazyComponents is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/performance-monitor.tsx\",\n    \"LazyComponents\",\n);\nexport const OptimizedImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call OptimizedImage() from the server but OptimizedImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/performance-monitor.tsx\",\n    \"OptimizedImage\",\n);\nexport const PerformanceMonitor = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/performance-monitor.tsx\",\n    \"PerformanceMonitor\",\n);\nexport const useResourcePreload = registerClientReference(\n    function() { throw new Error(\"Attempted to call useResourcePreload() from the server but useResourcePreload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/performance/performance-monitor.tsx\",\n    \"useResourcePreload\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gEACA", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/accessibility.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AccessibleDialog = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccessibleDialog() from the server but AccessibleDialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx <module evaluation>\",\n    \"AccessibleDialog\",\n);\nexport const AccessibleLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccessibleLabel() from the server but AccessibleLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx <module evaluation>\",\n    \"AccessibleLabel\",\n);\nexport const FocusTrap = registerClientReference(\n    function() { throw new Error(\"Attempted to call FocusTrap() from the server but FocusTrap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx <module evaluation>\",\n    \"FocusTrap\",\n);\nexport const LiveAnnouncer = registerClientReference(\n    function() { throw new Error(\"Attempted to call LiveAnnouncer() from the server but LiveAnnouncer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx <module evaluation>\",\n    \"LiveAnnouncer\",\n);\nexport const SkipToContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SkipToContent() from the server but SkipToContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx <module evaluation>\",\n    \"SkipToContent\",\n);\nexport const VisuallyHidden = registerClientReference(\n    function() { throw new Error(\"Attempted to call VisuallyHidden() from the server but VisuallyHidden is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx <module evaluation>\",\n    \"VisuallyHidden\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,qEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qEACA", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/components/ui/accessibility.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AccessibleDialog = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccessibleDialog() from the server but AccessibleDialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx\",\n    \"AccessibleDialog\",\n);\nexport const AccessibleLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccessibleLabel() from the server but AccessibleLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx\",\n    \"AccessibleLabel\",\n);\nexport const FocusTrap = registerClientReference(\n    function() { throw new Error(\"Attempted to call FocusTrap() from the server but FocusTrap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx\",\n    \"FocusTrap\",\n);\nexport const LiveAnnouncer = registerClientReference(\n    function() { throw new Error(\"Attempted to call LiveAnnouncer() from the server but LiveAnnouncer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx\",\n    \"LiveAnnouncer\",\n);\nexport const SkipToContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SkipToContent() from the server but SkipToContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx\",\n    \"SkipToContent\",\n);\nexport const VisuallyHidden = registerClientReference(\n    function() { throw new Error(\"Attempted to call VisuallyHidden() from the server but VisuallyHidden is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accessibility.tsx\",\n    \"VisuallyHidden\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iDACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,iDACA", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/config/site.ts"], "sourcesContent": ["export const siteConfig = {\n  name: \"现代博客系统\",\n  description: \"基于 Next.js 的现代化博客系统，支持 AI 摘要、多语言、深色模式等功能\",\n  url: \"https://your-domain.com\",\n  ogImage: \"https://your-domain.com/og.jpg\",\n  links: {\n    github: \"https://github.com/yourusername/your-repo\",\n    twitter: \"https://twitter.com/yourusername\",\n  },\n  author: {\n    name: \"Your Name\",\n    email: \"<EMAIL>\",\n    twitter: \"@yourusername\",\n  },\n  // 主题配置\n  theme: {\n    defaultTheme: \"light\" as const,\n    enableSystemTheme: true,\n  },\n  // 语言配置\n  i18n: {\n    defaultLocale: \"zh\" as const,\n    locales: [\"zh\", \"en\"] as const,\n  },\n  // 分页配置\n  pagination: {\n    articlesPerPage: 10,\n    commentsPerPage: 20,\n    filesPerPage: 20,\n  },\n  // 文件上传配置\n  upload: {\n    maxFileSize: 10 * 1024 * 1024, // 10MB\n    allowedTypes: [\n      \"image/jpeg\",\n      \"image/png\",\n      \"image/gif\",\n      \"image/webp\",\n      \"application/pdf\",\n      \"text/plain\",\n      \"text/markdown\",\n    ],\n  },\n  // SEO 配置\n  seo: {\n    titleTemplate: \"%s | 现代博客系统\",\n    defaultTitle: \"现代博客系统\",\n    description: \"基于 Next.js 的现代化博客系统\",\n    openGraph: {\n      type: \"website\",\n      locale: \"zh_CN\",\n      url: \"https://your-domain.com\",\n      siteName: \"现代博客系统\",\n    },\n    twitter: {\n      handle: \"@yourusername\",\n      site: \"@yourusername\",\n      cardType: \"summary_large_image\",\n    },\n  },\n  // 功能开关\n  features: {\n    enableComments: true,\n    enableSearch: true,\n    enableAnalytics: true,\n    enableAISummary: true,\n    enableFileManagement: true,\n    enableMultiLanguage: true,\n    enableDarkMode: true,\n  },\n  // 外部服务配置\n  services: {\n    cloudflareWorker: {\n      authUrl: process.env.CLOUDFLARE_AUTH_URL || \"\",\n      fileUrl: process.env.CLOUDFLARE_FILE_URL || \"\",\n    },\n    github: {\n      clientId: process.env.GITHUB_CLIENT_ID || \"\",\n      clientSecret: process.env.GITHUB_CLIENT_SECRET || \"\",\n    },\n    analytics: {\n      googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID || \"\",\n      plausibleDomain: process.env.PLAUSIBLE_DOMAIN || \"\",\n    },\n  },\n};\n\nexport type SiteConfig = typeof siteConfig;\n"], "names": [], "mappings": ";;;AAAO,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK;IACL,SAAS;IACT,OAAO;QACL,QAAQ;QACR,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,SAAS;IACX;IACA,OAAO;IACP,OAAO;QACL,cAAc;QACd,mBAAmB;IACrB;IACA,OAAO;IACP,MAAM;QACJ,eAAe;QACf,SAAS;YAAC;YAAM;SAAK;IACvB;IACA,OAAO;IACP,YAAY;QACV,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;IAChB;IACA,SAAS;IACT,QAAQ;QACN,aAAa,KAAK,OAAO;QACzB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;IACT,KAAK;QACH,eAAe;QACf,cAAc;QACd,aAAa;QACb,WAAW;YACT,MAAM;YACN,QAAQ;YACR,KAAK;YACL,UAAU;QACZ;QACA,SAAS;YACP,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;IACF;IACA,OAAO;IACP,UAAU;QACR,gBAAgB;QAChB,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,sBAAsB;QACtB,qBAAqB;QACrB,gBAAgB;IAClB;IACA,SAAS;IACT,UAAU;QACR,kBAAkB;YAChB,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YAC5C,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QAC9C;QACA,QAAQ;YACN,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,WAAW;YACT,mBAAmB,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YACtD,iBAAiB,QAAQ,GAAG,CAAC,gBAAgB,IAAI;QACnD;IACF;AACF", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ThemeProvider } from \"@/components/providers/theme-provider\";\nimport { AuthProvider } from \"@/components/providers/session-provider\";\nimport { ErrorBoundary } from \"@/components/error/error-boundary\";\nimport { PerformanceMonitor } from \"@/components/performance/performance-monitor\";\nimport { SkipToContent } from \"@/components/ui/accessibility\";\nimport { siteConfig } from \"@/config/site\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nexport const metadata: Metadata = {\n  title: {\n    default: siteConfig.name,\n    template: `%s | ${siteConfig.name}`,\n  },\n  description: siteConfig.description,\n  keywords: [\n    \"博客\",\n    \"Next.js\",\n    \"React\",\n    \"TypeScript\",\n    \"Tailwind CSS\",\n    \"AI\",\n    \"现代化\",\n  ],\n  authors: [\n    {\n      name: siteConfig.author.name,\n      url: siteConfig.url,\n    },\n  ],\n  creator: siteConfig.author.name,\n  openGraph: {\n    type: \"website\",\n    locale: \"zh_CN\",\n    url: siteConfig.url,\n    title: siteConfig.name,\n    description: siteConfig.description,\n    siteName: siteConfig.name,\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: siteConfig.name,\n    description: siteConfig.description,\n    creator: siteConfig.author.twitter,\n  },\n  icons: {\n    icon: \"/favicon.ico\",\n    shortcut: \"/favicon-16x16.png\",\n    apple: \"/apple-touch-icon.png\",\n  },\n  manifest: \"/site.webmanifest\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\" suppressHydrationWarning>\n      <head>\n        {/* 预加载关键资源 */}\n        <link rel=\"preload\" href=\"/fonts/inter.woff2\" as=\"font\" type=\"font/woff2\" crossOrigin=\"anonymous\" />\n      </head>\n      <body\n        className={`${inter.variable} font-sans antialiased min-h-screen bg-background text-foreground`}\n      >\n        <SkipToContent />\n        <PerformanceMonitor />\n\n        <ErrorBoundary>\n          <AuthProvider>\n            <ThemeProvider\n              attribute=\"class\"\n              defaultTheme=\"system\"\n              enableSystem\n              disableTransitionOnChange\n            >\n              <div id=\"main-content\">\n                {children}\n              </div>\n            </ThemeProvider>\n          </AuthProvider>\n        </ErrorBoundary>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAOO,MAAM,WAAqB;IAChC,OAAO;QACL,SAAS,qHAAA,CAAA,aAAU,CAAC,IAAI;QACxB,UAAU,CAAC,KAAK,EAAE,qHAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACrC;IACA,aAAa,qHAAA,CAAA,aAAU,CAAC,WAAW;IACnC,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QACP;YACE,MAAM,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,IAAI;YAC5B,KAAK,qHAAA,CAAA,aAAU,CAAC,GAAG;QACrB;KACD;IACD,SAAS,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,IAAI;IAC/B,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,qHAAA,CAAA,aAAU,CAAC,GAAG;QACnB,OAAO,qHAAA,CAAA,aAAU,CAAC,IAAI;QACtB,aAAa,qHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,UAAU,qHAAA,CAAA,aAAU,CAAC,IAAI;IAC3B;IACA,SAAS;QACP,MAAM;QACN,OAAO,qHAAA,CAAA,aAAU,CAAC,IAAI;QACtB,aAAa,qHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,SAAS,qHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO;IACpC;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAQ,wBAAwB;;0BACzC,8OAAC;0BAEC,cAAA,8OAAC;oBAAK,KAAI;oBAAU,MAAK;oBAAqB,IAAG;oBAAO,MAAK;oBAAa,aAAY;;;;;;;;;;;0BAExF,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,iEAAiE,CAAC;;kCAE/F,8OAAC,yIAAA,CAAA,gBAAa;;;;;kCACd,8OAAC,2JAAA,CAAA,qBAAkB;;;;;kCAEnB,8OAAC,gJAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,sJAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,oJAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,cAAa;gCACb,YAAY;gCACZ,yBAAyB;0CAEzB,cAAA,8OAAC;oCAAI,IAAG;8CACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/15268/Desktop/cs/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}